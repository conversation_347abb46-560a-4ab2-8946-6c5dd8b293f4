var rm=Object.defineProperty,nm=Object.defineProperties;var om=Object.getOwnPropertyDescriptors;var Os=Object.getOwnPropertySymbols;var Ph=Object.prototype.hasOwnProperty,Mh=Object.prototype.propertyIsEnumerable;var Ih=(v,lt,xt)=>lt in v?rm(v,lt,{enumerable:!0,configurable:!0,writable:!0,value:xt}):v[lt]=xt,re=(v,lt)=>{for(var xt in lt||(lt={}))Ph.call(lt,xt)&&Ih(v,xt,lt[xt]);if(Os)for(var xt of Os(lt))Mh.call(lt,xt)&&Ih(v,xt,lt[xt]);return v},Hn=(v,lt)=>nm(v,om(lt));var Xn=(v,lt)=>{var xt={};for(var At in v)Ph.call(v,At)&&lt.indexOf(At)<0&&(xt[At]=v[At]);if(v!=null&&Os)for(var At of Os(v))lt.indexOf(At)<0&&Mh.call(v,At)&&(xt[At]=v[At]);return xt};/*!
 * pixi.js-legacy - v7.0.5
 * Compiled Mon, 12 Dec 2022 15:56:40 UTC
 *
 * pixi.js-legacy is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */var PIXI=function(v){"use strict";var lt=(i=>(i[i.WEBGL_LEGACY=0]="WEBGL_LEGACY",i[i.WEBGL=1]="WEBGL",i[i.WEBGL2=2]="WEBGL2",i))(lt||{}),xt=(i=>(i[i.UNKNOWN=0]="UNKNOWN",i[i.WEBGL=1]="WEBGL",i[i.CANVAS=2]="CANVAS",i))(xt||{}),At=(i=>(i[i.COLOR=16384]="COLOR",i[i.DEPTH=256]="DEPTH",i[i.STENCIL=1024]="STENCIL",i))(At||{}),C=(i=>(i[i.NORMAL=0]="NORMAL",i[i.ADD=1]="ADD",i[i.MULTIPLY=2]="MULTIPLY",i[i.SCREEN=3]="SCREEN",i[i.OVERLAY=4]="OVERLAY",i[i.DARKEN=5]="DARKEN",i[i.LIGHTEN=6]="LIGHTEN",i[i.COLOR_DODGE=7]="COLOR_DODGE",i[i.COLOR_BURN=8]="COLOR_BURN",i[i.HARD_LIGHT=9]="HARD_LIGHT",i[i.SOFT_LIGHT=10]="SOFT_LIGHT",i[i.DIFFERENCE=11]="DIFFERENCE",i[i.EXCLUSION=12]="EXCLUSION",i[i.HUE=13]="HUE",i[i.SATURATION=14]="SATURATION",i[i.COLOR=15]="COLOR",i[i.LUMINOSITY=16]="LUMINOSITY",i[i.NORMAL_NPM=17]="NORMAL_NPM",i[i.ADD_NPM=18]="ADD_NPM",i[i.SCREEN_NPM=19]="SCREEN_NPM",i[i.NONE=20]="NONE",i[i.SRC_OVER=0]="SRC_OVER",i[i.SRC_IN=21]="SRC_IN",i[i.SRC_OUT=22]="SRC_OUT",i[i.SRC_ATOP=23]="SRC_ATOP",i[i.DST_OVER=24]="DST_OVER",i[i.DST_IN=25]="DST_IN",i[i.DST_OUT=26]="DST_OUT",i[i.DST_ATOP=27]="DST_ATOP",i[i.ERASE=26]="ERASE",i[i.SUBTRACT=28]="SUBTRACT",i[i.XOR=29]="XOR",i))(C||{}),Bt=(i=>(i[i.POINTS=0]="POINTS",i[i.LINES=1]="LINES",i[i.LINE_LOOP=2]="LINE_LOOP",i[i.LINE_STRIP=3]="LINE_STRIP",i[i.TRIANGLES=4]="TRIANGLES",i[i.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",i[i.TRIANGLE_FAN=6]="TRIANGLE_FAN",i))(Bt||{}),N=(i=>(i[i.RGBA=6408]="RGBA",i[i.RGB=6407]="RGB",i[i.RG=33319]="RG",i[i.RED=6403]="RED",i[i.RGBA_INTEGER=36249]="RGBA_INTEGER",i[i.RGB_INTEGER=36248]="RGB_INTEGER",i[i.RG_INTEGER=33320]="RG_INTEGER",i[i.RED_INTEGER=36244]="RED_INTEGER",i[i.ALPHA=6406]="ALPHA",i[i.LUMINANCE=6409]="LUMINANCE",i[i.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",i[i.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",i[i.DEPTH_STENCIL=34041]="DEPTH_STENCIL",i))(N||{}),ye=(i=>(i[i.TEXTURE_2D=3553]="TEXTURE_2D",i[i.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",i[i.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",i[i.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",i[i.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",i[i.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",i[i.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",i[i.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",i[i.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",i))(ye||{}),H=(i=>(i[i.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",i[i.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",i[i.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",i[i.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",i[i.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",i[i.UNSIGNED_INT=5125]="UNSIGNED_INT",i[i.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",i[i.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",i[i.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",i[i.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",i[i.BYTE=5120]="BYTE",i[i.SHORT=5122]="SHORT",i[i.INT=5124]="INT",i[i.FLOAT=5126]="FLOAT",i[i.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",i[i.HALF_FLOAT=36193]="HALF_FLOAT",i))(H||{}),Fi=(i=>(i[i.FLOAT=0]="FLOAT",i[i.INT=1]="INT",i[i.UINT=2]="UINT",i))(Fi||{}),Dt=(i=>(i[i.NEAREST=0]="NEAREST",i[i.LINEAR=1]="LINEAR",i))(Dt||{}),Kt=(i=>(i[i.CLAMP=33071]="CLAMP",i[i.REPEAT=10497]="REPEAT",i[i.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",i))(Kt||{}),Gt=(i=>(i[i.OFF=0]="OFF",i[i.POW2=1]="POW2",i[i.ON=2]="ON",i[i.ON_MANUAL=3]="ON_MANUAL",i))(Gt||{}),Nt=(i=>(i[i.NPM=0]="NPM",i[i.UNPACK=1]="UNPACK",i[i.PMA=2]="PMA",i[i.NO_PREMULTIPLIED_ALPHA=0]="NO_PREMULTIPLIED_ALPHA",i[i.PREMULTIPLY_ON_UPLOAD=1]="PREMULTIPLY_ON_UPLOAD",i[i.PREMULTIPLIED_ALPHA=2]="PREMULTIPLIED_ALPHA",i))(Nt||{}),Ht=(i=>(i[i.NO=0]="NO",i[i.YES=1]="YES",i[i.AUTO=2]="AUTO",i[i.BLEND=0]="BLEND",i[i.CLEAR=1]="CLEAR",i[i.BLIT=2]="BLIT",i))(Ht||{}),Oi=(i=>(i[i.AUTO=0]="AUTO",i[i.MANUAL=1]="MANUAL",i))(Oi||{}),Rt=(i=>(i.LOW="lowp",i.MEDIUM="mediump",i.HIGH="highp",i))(Rt||{}),mt=(i=>(i[i.NONE=0]="NONE",i[i.SCISSOR=1]="SCISSOR",i[i.STENCIL=2]="STENCIL",i[i.SPRITE=3]="SPRITE",i[i.COLOR=4]="COLOR",i))(mt||{}),Vn=(i=>(i[i.RED=1]="RED",i[i.GREEN=2]="GREEN",i[i.BLUE=4]="BLUE",i[i.ALPHA=8]="ALPHA",i))(Vn||{}),ft=(i=>(i[i.NONE=0]="NONE",i[i.LOW=2]="LOW",i[i.MEDIUM=4]="MEDIUM",i[i.HIGH=8]="HIGH",i))(ft||{}),Xt=(i=>(i[i.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",i[i.ARRAY_BUFFER=34962]="ARRAY_BUFFER",i[i.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",i))(Xt||{});const zn={createCanvas:(i,t)=>{const e=document.createElement("canvas");return e.width=i,e.height=t,e},getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>{var i;return(i=document.baseURI)!=null?i:window.location.href},getFontFaceSet:()=>document.fonts,fetch:(i,t)=>fetch(i,t),parseXML:i=>new DOMParser().parseFromString(i,"text/xml")};var Ls=/iPhone/i,Wn=/iPod/i,jn=/iPad/i,Yn=/\biOS-universal(?:.+)Mac\b/i,Us=/\bAndroid(?:.+)Mobile\b/i,$n=/Android/i,Oe=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,Li=/Silk/i,ne=/Windows Phone/i,qn=/\bWindows(?:.+)ARM\b/i,Kn=/BlackBerry/i,Zn=/BB10/i,Qn=/Opera Mini/i,Jn=/\b(CriOS|Chrome)(?:.+)Mobile/i,to=/Mobile(?:.+)Firefox\b/i,eo=function(i){return typeof i!="undefined"&&i.platform==="MacIntel"&&typeof i.maxTouchPoints=="number"&&i.maxTouchPoints>1&&typeof MSStream=="undefined"};function Bh(i){return function(t){return t.test(i)}}function Dh(i){var t={userAgent:"",platform:"",maxTouchPoints:0};!i&&typeof navigator!="undefined"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof i=="string"?t.userAgent=i:i&&i.userAgent&&(t={userAgent:i.userAgent,platform:i.platform,maxTouchPoints:i.maxTouchPoints||0});var e=t.userAgent,s=e.split("[FBAN");typeof s[1]!="undefined"&&(e=s[0]),s=e.split("Twitter"),typeof s[1]!="undefined"&&(e=s[0]);var r=Bh(e),n={apple:{phone:r(Ls)&&!r(ne),ipod:r(Wn),tablet:!r(Ls)&&(r(jn)||eo(t))&&!r(ne),universal:r(Yn),device:(r(Ls)||r(Wn)||r(jn)||r(Yn)||eo(t))&&!r(ne)},amazon:{phone:r(Oe),tablet:!r(Oe)&&r(Li),device:r(Oe)||r(Li)},android:{phone:!r(ne)&&r(Oe)||!r(ne)&&r(Us),tablet:!r(ne)&&!r(Oe)&&!r(Us)&&(r(Li)||r($n)),device:!r(ne)&&(r(Oe)||r(Li)||r(Us)||r($n))||r(/\bokhttp\b/i)},windows:{phone:r(ne),tablet:r(qn),device:r(ne)||r(qn)},other:{blackberry:r(Kn),blackberry10:r(Zn),opera:r(Qn),firefox:r(to),chrome:r(Jn),device:r(Kn)||r(Zn)||r(Qn)||r(to)||r(Jn)},any:!1,phone:!1,tablet:!1};return n.any=n.apple.device||n.android.device||n.windows.device||n.other.device,n.phone=n.apple.phone||n.android.phone||n.windows.phone,n.tablet=n.apple.tablet||n.android.tablet||n.windows.tablet,n}const Vt=Dh(globalThis.navigator);function Nh(){return!Vt.apple.device}function Fh(i){let t=!0;if(Vt.tablet||Vt.phone){if(Vt.apple.device){const e=navigator.userAgent.match(/OS (\d+)_(\d+)?/);e&&parseInt(e[1],10)<11&&(t=!1)}if(Vt.android.device){const e=navigator.userAgent.match(/Android\s([0-9.]*)/);e&&parseInt(e[1],10)<7&&(t=!1)}}return t?i:4}const P={ADAPTER:zn,MIPMAP_TEXTURES:Gt.POW2,ANISOTROPIC_LEVEL:0,RESOLUTION:1,FILTER_RESOLUTION:1,FILTER_MULTISAMPLE:ft.NONE,SPRITE_MAX_TEXTURES:Fh(32),SPRITE_BATCH_SIZE:4096,RENDER_OPTIONS:{view:null,antialias:!1,autoDensity:!1,backgroundColor:0,backgroundAlpha:1,premultipliedAlpha:!0,clearBeforeRender:!0,preserveDrawingBuffer:!1,width:800,height:600,legacy:!1,hello:!1},GC_MODE:Oi.AUTO,GC_MAX_IDLE:60*60,GC_MAX_CHECK_COUNT:60*10,WRAP_MODE:Kt.CLAMP,SCALE_MODE:Dt.LINEAR,PRECISION_VERTEX:Rt.HIGH,PRECISION_FRAGMENT:Vt.apple.device?Rt.HIGH:Rt.MEDIUM,CAN_UPLOAD_SAME_BUFFER:Nh(),CREATE_IMAGE_BITMAP:!1,ROUND_PIXELS:!1};P.PREFER_ENV=lt.WEBGL2,P.STRICT_TEXTURE_CACHE=!1;var F=(i=>(i.Renderer="renderer",i.Application="application",i.RendererSystem="renderer-webgl-system",i.RendererPlugin="renderer-webgl-plugin",i.CanvasRendererSystem="renderer-canvas-system",i.CanvasRendererPlugin="renderer-canvas-plugin",i.Asset="asset",i.LoadParser="load-parser",i.ResolveParser="resolve-parser",i.CacheParser="cache-parser",i.DetectionParser="detection-parser",i))(F||{});const ks=i=>{if(typeof i=="function"||typeof i=="object"&&i.extension){if(!i.extension)throw new Error("Extension class must have an extension object");const t=typeof i.extension!="object"?{type:i.extension}:i.extension;i=Hn(re({},t),{ref:i})}if(typeof i=="object")i=re({},i);else throw new Error("Invalid extension type");return typeof i.type=="string"&&(i.type=[i.type]),i},io=(i,t)=>{var e;return(e=ks(i).priority)!=null?e:t},U={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...i){return i.map(ks).forEach(t=>{t.type.forEach(e=>{var s,r;return(r=(s=this._removeHandlers)[e])==null?void 0:r.call(s,t)})}),this},add(...i){return i.map(ks).forEach(t=>{t.type.forEach(e=>{const s=this._addHandlers,r=this._queue;s[e]?s[e](t):(r[e]=r[e]||[],r[e].push(t))})}),this},handle(i,t,e){const s=this._addHandlers,r=this._removeHandlers;if(s[i]||r[i])throw new Error(`Extension type ${i} already has a handler`);s[i]=t,r[i]=e;const n=this._queue;return n[i]&&(n[i].forEach(o=>t(o)),delete n[i]),this},handleByMap(i,t){return this.handle(i,e=>{t[e.name]=e.ref},e=>{delete t[e.name]})},handleByList(i,t,e=-1){return this.handle(i,s=>{t.includes(s.ref)||(t.push(s.ref),t.sort((r,n)=>io(n,e)-io(r,e)))},s=>{const r=t.indexOf(s.ref);r!==-1&&t.splice(r,1)})}},Ke=Math.PI*2,so=180/Math.PI,ro=Math.PI/180;var et=(i=>(i[i.POLY=0]="POLY",i[i.RECT=1]="RECT",i[i.CIRC=2]="CIRC",i[i.ELIP=3]="ELIP",i[i.RREC=4]="RREC",i))(et||{});class W{constructor(t=0,e=0){this.x=0,this.y=0,this.x=t,this.y=e}clone(){return new W(this.x,this.y)}copyFrom(t){return this.set(t.x,t.y),this}copyTo(t){return t.set(this.x,this.y),t}equals(t){return t.x===this.x&&t.y===this.y}set(t=0,e=t){return this.x=t,this.y=e,this}toString(){return`[@pixi/math:Point x=${this.x} y=${this.y}]`}}const Ui=[new W,new W,new W,new W];class j{constructor(t=0,e=0,s=0,r=0){this.x=Number(t),this.y=Number(e),this.width=Number(s),this.height=Number(r),this.type=et.RECT}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}static get EMPTY(){return new j(0,0,0,0)}clone(){return new j(this.x,this.y,this.width,this.height)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}contains(t,e){return this.width<=0||this.height<=0?!1:t>=this.x&&t<this.x+this.width&&e>=this.y&&e<this.y+this.height}intersects(t,e){if(!e){const b=this.x<t.x?t.x:this.x;if((this.right>t.right?t.right:this.right)<=b)return!1;const O=this.y<t.y?t.y:this.y;return(this.bottom>t.bottom?t.bottom:this.bottom)>O}const s=this.left,r=this.right,n=this.top,o=this.bottom;if(r<=s||o<=n)return!1;const a=Ui[0].set(t.left,t.top),h=Ui[1].set(t.left,t.bottom),l=Ui[2].set(t.right,t.top),c=Ui[3].set(t.right,t.bottom);if(l.x<=a.x||h.y<=a.y)return!1;const u=Math.sign(e.a*e.d-e.b*e.c);if(u===0||(e.apply(a,a),e.apply(h,h),e.apply(l,l),e.apply(c,c),Math.max(a.x,h.x,l.x,c.x)<=s||Math.min(a.x,h.x,l.x,c.x)>=r||Math.max(a.y,h.y,l.y,c.y)<=n||Math.min(a.y,h.y,l.y,c.y)>=o))return!1;const d=u*(h.y-a.y),f=u*(a.x-h.x),p=d*s+f*n,g=d*r+f*n,m=d*s+f*o,x=d*r+f*o;if(Math.max(p,g,m,x)<=d*a.x+f*a.y||Math.min(p,g,m,x)>=d*c.x+f*c.y)return!1;const T=u*(a.y-l.y),_=u*(l.x-a.x),y=T*s+_*n,E=T*r+_*n,B=T*s+_*o,A=T*r+_*o;return!(Math.max(y,E,B,A)<=T*a.x+_*a.y||Math.min(y,E,B,A)>=T*c.x+_*c.y)}pad(t=0,e=t){return this.x-=t,this.y-=e,this.width+=t*2,this.height+=e*2,this}fit(t){const e=Math.max(this.x,t.x),s=Math.min(this.x+this.width,t.x+t.width),r=Math.max(this.y,t.y),n=Math.min(this.y+this.height,t.y+t.height);return this.x=e,this.width=Math.max(s-e,0),this.y=r,this.height=Math.max(n-r,0),this}ceil(t=1,e=.001){const s=Math.ceil((this.x+this.width-e)*t)/t,r=Math.ceil((this.y+this.height-e)*t)/t;return this.x=Math.floor((this.x+e)*t)/t,this.y=Math.floor((this.y+e)*t)/t,this.width=s-this.x,this.height=r-this.y,this}enlarge(t){const e=Math.min(this.x,t.x),s=Math.max(this.x+this.width,t.x+t.width),r=Math.min(this.y,t.y),n=Math.max(this.y+this.height,t.y+t.height);return this.x=e,this.width=s-e,this.y=r,this.height=n-r,this}toString(){return`[@pixi/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}class ki{constructor(t=0,e=0,s=0){this.x=t,this.y=e,this.radius=s,this.type=et.CIRC}clone(){return new ki(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const s=this.radius*this.radius;let r=this.x-t,n=this.y-e;return r*=r,n*=n,r+n<=s}getBounds(){return new j(this.x-this.radius,this.y-this.radius,this.radius*2,this.radius*2)}toString(){return`[@pixi/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Gi{constructor(t=0,e=0,s=0,r=0){this.x=t,this.y=e,this.width=s,this.height=r,this.type=et.ELIP}clone(){return new Gi(this.x,this.y,this.width,this.height)}contains(t,e){if(this.width<=0||this.height<=0)return!1;let s=(t-this.x)/this.width,r=(e-this.y)/this.height;return s*=s,r*=r,s+r<=1}getBounds(){return new j(this.x-this.width,this.y-this.height,this.width,this.height)}toString(){return`[@pixi/math:Ellipse x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}class xe{constructor(...t){let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const s=[];for(let r=0,n=e.length;r<n;r++)s.push(e[r].x,e[r].y);e=s}this.points=e,this.type=et.POLY,this.closeStroke=!0}clone(){const t=this.points.slice(),e=new xe(t);return e.closeStroke=this.closeStroke,e}contains(t,e){let s=!1;const r=this.points.length/2;for(let n=0,o=r-1;n<r;o=n++){const a=this.points[n*2],h=this.points[n*2+1],l=this.points[o*2],c=this.points[o*2+1];h>e!=c>e&&t<(l-a)*((e-h)/(c-h))+a&&(s=!s)}return s}toString(){return`[@pixi/math:PolygoncloseStroke=${this.closeStroke}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}}class Hi{constructor(t=0,e=0,s=0,r=0,n=20){this.x=t,this.y=e,this.width=s,this.height=r,this.radius=n,this.type=et.RREC}clone(){return new Hi(this.x,this.y,this.width,this.height,this.radius)}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const s=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+s&&e<=this.y+this.height-s||t>=this.x+s&&t<=this.x+this.width-s)return!0;let r=t-(this.x+s),n=e-(this.y+s);const o=s*s;if(r*r+n*n<=o||(r=t-(this.x+this.width-s),r*r+n*n<=o)||(n=e-(this.y+this.height-s),r*r+n*n<=o)||(r=t-(this.x+s),r*r+n*n<=o))return!0}return!1}toString(){return`[@pixi/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}class oe{constructor(t,e,s=0,r=0){this._x=s,this._y=r,this.cb=t,this.scope=e}clone(t=this.cb,e=this.scope){return new oe(t,e,this._x,this._y)}set(t=0,e=t){return(this._x!==t||this._y!==e)&&(this._x=t,this._y=e,this.cb.call(this.scope)),this}copyFrom(t){return(this._x!==t.x||this._y!==t.y)&&(this._x=t.x,this._y=t.y,this.cb.call(this.scope)),this}copyTo(t){return t.set(this._x,this._y),t}equals(t){return t.x===this._x&&t.y===this._y}toString(){return`[@pixi/math:ObservablePoint x=${0} y=${0} scope=${this.scope}]`}get x(){return this._x}set x(t){this._x!==t&&(this._x=t,this.cb.call(this.scope))}get y(){return this._y}set y(t){this._y!==t&&(this._y=t,this.cb.call(this.scope))}}class Z{constructor(t=1,e=0,s=0,r=1,n=0,o=0){this.array=null,this.a=t,this.b=e,this.c=s,this.d=r,this.tx=n,this.ty=o}fromArray(t){this.a=t[0],this.b=t[1],this.c=t[3],this.d=t[4],this.tx=t[2],this.ty=t[5]}set(t,e,s,r,n,o){return this.a=t,this.b=e,this.c=s,this.d=r,this.tx=n,this.ty=o,this}toArray(t,e){this.array||(this.array=new Float32Array(9));const s=e||this.array;return t?(s[0]=this.a,s[1]=this.b,s[2]=0,s[3]=this.c,s[4]=this.d,s[5]=0,s[6]=this.tx,s[7]=this.ty,s[8]=1):(s[0]=this.a,s[1]=this.c,s[2]=this.tx,s[3]=this.b,s[4]=this.d,s[5]=this.ty,s[6]=0,s[7]=0,s[8]=1),s}apply(t,e){e=e||new W;const s=t.x,r=t.y;return e.x=this.a*s+this.c*r+this.tx,e.y=this.b*s+this.d*r+this.ty,e}applyInverse(t,e){e=e||new W;const s=1/(this.a*this.d+this.c*-this.b),r=t.x,n=t.y;return e.x=this.d*s*r+-this.c*s*n+(this.ty*this.c-this.tx*this.d)*s,e.y=this.a*s*n+-this.b*s*r+(-this.ty*this.a+this.tx*this.b)*s,e}translate(t,e){return this.tx+=t,this.ty+=e,this}scale(t,e){return this.a*=t,this.d*=e,this.c*=t,this.b*=e,this.tx*=t,this.ty*=e,this}rotate(t){const e=Math.cos(t),s=Math.sin(t),r=this.a,n=this.c,o=this.tx;return this.a=r*e-this.b*s,this.b=r*s+this.b*e,this.c=n*e-this.d*s,this.d=n*s+this.d*e,this.tx=o*e-this.ty*s,this.ty=o*s+this.ty*e,this}append(t){const e=this.a,s=this.b,r=this.c,n=this.d;return this.a=t.a*e+t.b*r,this.b=t.a*s+t.b*n,this.c=t.c*e+t.d*r,this.d=t.c*s+t.d*n,this.tx=t.tx*e+t.ty*r+this.tx,this.ty=t.tx*s+t.ty*n+this.ty,this}setTransform(t,e,s,r,n,o,a,h,l){return this.a=Math.cos(a+l)*n,this.b=Math.sin(a+l)*n,this.c=-Math.sin(a-h)*o,this.d=Math.cos(a-h)*o,this.tx=t-(s*this.a+r*this.c),this.ty=e-(s*this.b+r*this.d),this}prepend(t){const e=this.tx;if(t.a!==1||t.b!==0||t.c!==0||t.d!==1){const s=this.a,r=this.c;this.a=s*t.a+this.b*t.c,this.b=s*t.b+this.b*t.d,this.c=r*t.a+this.d*t.c,this.d=r*t.b+this.d*t.d}return this.tx=e*t.a+this.ty*t.c+t.tx,this.ty=e*t.b+this.ty*t.d+t.ty,this}decompose(t){const e=this.a,s=this.b,r=this.c,n=this.d,o=t.pivot,a=-Math.atan2(-r,n),h=Math.atan2(s,e),l=Math.abs(a+h);return l<1e-5||Math.abs(Ke-l)<1e-5?(t.rotation=h,t.skew.x=t.skew.y=0):(t.rotation=0,t.skew.x=a,t.skew.y=h),t.scale.x=Math.sqrt(e*e+s*s),t.scale.y=Math.sqrt(r*r+n*n),t.position.x=this.tx+(o.x*e+o.y*r),t.position.y=this.ty+(o.x*s+o.y*n),t}invert(){const t=this.a,e=this.b,s=this.c,r=this.d,n=this.tx,o=t*r-e*s;return this.a=r/o,this.b=-e/o,this.c=-s/o,this.d=t/o,this.tx=(s*this.ty-r*n)/o,this.ty=-(t*this.ty-e*n)/o,this}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const t=new Z;return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyTo(t){return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyFrom(t){return this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.tx=t.tx,this.ty=t.ty,this}toString(){return`[@pixi/math:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return new Z}static get TEMP_MATRIX(){return new Z}}const Te=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],be=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Ee=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],we=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],Gs=[],no=[],Xi=Math.sign;function Oh(){for(let i=0;i<16;i++){const t=[];Gs.push(t);for(let e=0;e<16;e++){const s=Xi(Te[i]*Te[e]+Ee[i]*be[e]),r=Xi(be[i]*Te[e]+we[i]*be[e]),n=Xi(Te[i]*Ee[e]+Ee[i]*we[e]),o=Xi(be[i]*Ee[e]+we[i]*we[e]);for(let a=0;a<16;a++)if(Te[a]===s&&be[a]===r&&Ee[a]===n&&we[a]===o){t.push(a);break}}}for(let i=0;i<16;i++){const t=new Z;t.set(Te[i],be[i],Ee[i],we[i],0,0),no.push(t)}}Oh();const nt={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:i=>Te[i],uY:i=>be[i],vX:i=>Ee[i],vY:i=>we[i],inv:i=>i&8?i&15:-i&7,add:(i,t)=>Gs[i][t],sub:(i,t)=>Gs[i][nt.inv(t)],rotate180:i=>i^4,isVertical:i=>(i&3)===2,byDirection:(i,t)=>Math.abs(i)*2<=Math.abs(t)?t>=0?nt.S:nt.N:Math.abs(t)*2<=Math.abs(i)?i>0?nt.E:nt.W:t>0?i>0?nt.SE:nt.SW:i>0?nt.NE:nt.NW,matrixAppendRotationInv:(i,t,e=0,s=0)=>{const r=no[nt.inv(t)];r.tx=e,r.ty=s,i.append(r)}},oo=class{constructor(){this.worldTransform=new Z,this.localTransform=new Z,this.position=new oe(this.onChange,this,0,0),this.scale=new oe(this.onChange,this,1,1),this.pivot=new oe(this.onChange,this,0,0),this.skew=new oe(this.updateSkew,this,0,0),this._rotation=0,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._localID=0,this._currentLocalID=0,this._worldID=0,this._parentID=0}onChange(){this._localID++}updateSkew(){this._cx=Math.cos(this._rotation+this.skew.y),this._sx=Math.sin(this._rotation+this.skew.y),this._cy=-Math.sin(this._rotation-this.skew.x),this._sy=Math.cos(this._rotation-this.skew.x),this._localID++}toString(){return`[@pixi/math:Transform position=(${this.position.x}, ${this.position.y}) rotation=${this.rotation} scale=(${this.scale.x}, ${this.scale.y}) skew=(${this.skew.x}, ${this.skew.y}) ]`}updateLocalTransform(){const i=this.localTransform;this._localID!==this._currentLocalID&&(i.a=this._cx*this.scale.x,i.b=this._sx*this.scale.x,i.c=this._cy*this.scale.y,i.d=this._sy*this.scale.y,i.tx=this.position.x-(this.pivot.x*i.a+this.pivot.y*i.c),i.ty=this.position.y-(this.pivot.x*i.b+this.pivot.y*i.d),this._currentLocalID=this._localID,this._parentID=-1)}updateTransform(i){const t=this.localTransform;if(this._localID!==this._currentLocalID&&(t.a=this._cx*this.scale.x,t.b=this._sx*this.scale.x,t.c=this._cy*this.scale.y,t.d=this._sy*this.scale.y,t.tx=this.position.x-(this.pivot.x*t.a+this.pivot.y*t.c),t.ty=this.position.y-(this.pivot.x*t.b+this.pivot.y*t.d),this._currentLocalID=this._localID,this._parentID=-1),this._parentID!==i._worldID){const e=i.worldTransform,s=this.worldTransform;s.a=t.a*e.a+t.b*e.c,s.b=t.a*e.b+t.b*e.d,s.c=t.c*e.a+t.d*e.c,s.d=t.c*e.b+t.d*e.d,s.tx=t.tx*e.a+t.ty*e.c+e.tx,s.ty=t.tx*e.b+t.ty*e.d+e.ty,this._parentID=i._worldID,this._worldID++}}setFromMatrix(i){i.decompose(this),this._localID++}get rotation(){return this._rotation}set rotation(i){this._rotation!==i&&(this._rotation=i,this.updateSkew())}};let Ze=oo;Ze.IDENTITY=new oo;class It{constructor(t){this.items=[],this._name=t,this._aliasCount=0}emit(t,e,s,r,n,o,a,h){if(arguments.length>8)throw new Error("max arguments reached");const{name:l,items:c}=this;this._aliasCount++;for(let u=0,d=c.length;u<d;u++)c[u][l](t,e,s,r,n,o,a,h);return c===this.items&&this._aliasCount--,this}ensureNonAliasedItems(){this._aliasCount>0&&this.items.length>1&&(this._aliasCount=0,this.items=this.items.slice(0))}add(t){return t[this._name]&&(this.ensureNonAliasedItems(),this.remove(t),this.items.push(t)),this}remove(t){const e=this.items.indexOf(t);return e!==-1&&(this.ensureNonAliasedItems(),this.items.splice(e,1)),this}contains(t){return this.items.includes(t)}removeAll(){return this.ensureNonAliasedItems(),this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}Object.defineProperties(It.prototype,{dispatch:{value:It.prototype.emit},run:{value:It.prototype.emit}}),P.TARGET_FPMS=.06;var pe=(i=>(i[i.HIGH=25]="HIGH",i[i.NORMAL=0]="NORMAL",i[i.LOW=-25]="LOW",i[i.UTILITY=-50]="UTILITY",i))(pe||{});class Hs{constructor(t,e=null,s=0,r=!1){this.next=null,this.previous=null,this._destroyed=!1,this.fn=t,this.context=e,this.priority=s,this.once=r}match(t,e=null){return this.fn===t&&this.context===e}emit(t){this.fn&&(this.context?this.fn.call(this.context,t):this.fn(t));const e=this.next;return this.once&&this.destroy(!0),this._destroyed&&(this.next=null),e}connect(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this}destroy(t=!1){this._destroyed=!0,this.fn=null,this.context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const e=this.next;return this.next=t?null:e,this.previous=null,e}}class ot{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new Hs(null,null,1/0),this.deltaMS=1/P.TARGET_FPMS,this.elapsedMS=1/P.TARGET_FPMS,this._tick=t=>{this._requestId=null,this.started&&(this.update(t),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(t,e,s=pe.NORMAL){return this._addListener(new Hs(t,e,s))}addOnce(t,e,s=pe.NORMAL){return this._addListener(new Hs(t,e,s,!0))}_addListener(t){let e=this._head.next,s=this._head;if(!e)t.connect(s);else{for(;e;){if(t.priority>e.priority){t.connect(s);break}s=e,e=e.next}t.previous||t.connect(s)}return this._startIfPossible(),this}remove(t,e){let s=this._head.next;for(;s;)s.match(t,e)?s=s.destroy():s=s.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let t=0,e=this._head;for(;e=e.next;)t++;return t}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let t=this._head.next;for(;t;)t=t.destroy(!0);this._head.destroy(),this._head=null}}update(t=performance.now()){let e;if(t>this.lastTime){if(e=this.elapsedMS=t-this.lastTime,e>this._maxElapsedMS&&(e=this._maxElapsedMS),e*=this.speed,this._minElapsedMS){const n=t-this._lastFrame|0;if(n<this._minElapsedMS)return;this._lastFrame=t-n%this._minElapsedMS}this.deltaMS=e,this.deltaTime=this.deltaMS*P.TARGET_FPMS;const s=this._head;let r=s.next;for(;r;)r=r.emit(this.deltaTime);s.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(t){const e=Math.min(this.maxFPS,t),s=Math.min(Math.max(0,e)/1e3,P.TARGET_FPMS);this._maxElapsedMS=1/s}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(t){if(t===0)this._minElapsedMS=0;else{const e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3)}}static get shared(){if(!ot._shared){const t=ot._shared=new ot;t.autoStart=!0,t._protected=!0}return ot._shared}static get system(){if(!ot._system){const t=ot._system=new ot;t.autoStart=!0,t._protected=!0}return ot._system}}class Xs{static init(t){t=Object.assign({autoStart:!0,sharedTicker:!1},t),Object.defineProperty(this,"ticker",{set(e){this._ticker&&this._ticker.remove(this.render,this),this._ticker=e,e&&e.add(this.render,this,pe.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=t.sharedTicker?ot.shared:new ot,t.autoStart&&this.start()}static destroy(){if(this._ticker){const t=this._ticker;this.ticker=null,t.destroy()}}}Xs.extension=F.Application,U.add(Xs);var Vs=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function am(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}function zs(i,t,e){return e={path:t,exports:{},require:function(s,r){return Lh(s,r==null?e.path:r)}},i(e,e.exports),e.exports}function hm(i){return i&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}function lm(i){return i&&Object.prototype.hasOwnProperty.call(i,"default")&&Object.keys(i).length===1?i.default:i}function cm(i){if(i.__esModule)return i;var t=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(i).forEach(function(e){var s=Object.getOwnPropertyDescriptor(i,e);Object.defineProperty(t,e,s.get?s:{enumerable:!0,get:function(){return i[e]}})}),t}function Lh(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var Le=zs(function(i){"use strict";var t=Object.prototype.hasOwnProperty,e="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(e=!1));function r(h,l,c){this.fn=h,this.context=l,this.once=c||!1}function n(h,l,c,u,d){if(typeof c!="function")throw new TypeError("The listener must be a function");var f=new r(c,u||h,d),p=e?e+l:l;return h._events[p]?h._events[p].fn?h._events[p]=[h._events[p],f]:h._events[p].push(f):(h._events[p]=f,h._eventsCount++),h}function o(h,l){--h._eventsCount===0?h._events=new s:delete h._events[l]}function a(){this._events=new s,this._eventsCount=0}a.prototype.eventNames=function(){var l=[],c,u;if(this._eventsCount===0)return l;for(u in c=this._events)t.call(c,u)&&l.push(e?u.slice(1):u);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(c)):l},a.prototype.listeners=function(l){var c=e?e+l:l,u=this._events[c];if(!u)return[];if(u.fn)return[u.fn];for(var d=0,f=u.length,p=new Array(f);d<f;d++)p[d]=u[d].fn;return p},a.prototype.listenerCount=function(l){var c=e?e+l:l,u=this._events[c];return u?u.fn?1:u.length:0},a.prototype.emit=function(l,c,u,d,f,p){var g=e?e+l:l;if(!this._events[g])return!1;var m=this._events[g],x=arguments.length,T,_;if(m.fn){switch(m.once&&this.removeListener(l,m.fn,void 0,!0),x){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,c),!0;case 3:return m.fn.call(m.context,c,u),!0;case 4:return m.fn.call(m.context,c,u,d),!0;case 5:return m.fn.call(m.context,c,u,d,f),!0;case 6:return m.fn.call(m.context,c,u,d,f,p),!0}for(_=1,T=new Array(x-1);_<x;_++)T[_-1]=arguments[_];m.fn.apply(m.context,T)}else{var y=m.length,E;for(_=0;_<y;_++)switch(m[_].once&&this.removeListener(l,m[_].fn,void 0,!0),x){case 1:m[_].fn.call(m[_].context);break;case 2:m[_].fn.call(m[_].context,c);break;case 3:m[_].fn.call(m[_].context,c,u);break;case 4:m[_].fn.call(m[_].context,c,u,d);break;default:if(!T)for(E=1,T=new Array(x-1);E<x;E++)T[E-1]=arguments[E];m[_].fn.apply(m[_].context,T)}}return!0},a.prototype.on=function(l,c,u){return n(this,l,c,u,!1)},a.prototype.once=function(l,c,u){return n(this,l,c,u,!0)},a.prototype.removeListener=function(l,c,u,d){var f=e?e+l:l;if(!this._events[f])return this;if(!c)return o(this,f),this;var p=this._events[f];if(p.fn)p.fn===c&&(!d||p.once)&&(!u||p.context===u)&&o(this,f);else{for(var g=0,m=[],x=p.length;g<x;g++)(p[g].fn!==c||d&&!p[g].once||u&&p[g].context!==u)&&m.push(p[g]);m.length?this._events[f]=m.length===1?m[0]:m:o(this,f)}return this},a.prototype.removeAllListeners=function(l){var c;return l?(c=e?e+l:l,this._events[c]&&o(this,c)):(this._events=new s,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,i.exports=a}),Ws=Vi,Uh=Vi;function Vi(i,t,e){e=e||2;var s=t&&t.length,r=s?t[0]*e:i.length,n=ao(i,0,r,e,!0),o=[];if(!n||n.next===n.prev)return o;var a,h,l,c,u,d,f;if(s&&(n=Vh(i,t,n,e)),i.length>80*e){a=l=i[0],h=c=i[1];for(var p=e;p<r;p+=e)u=i[p],d=i[p+1],u<a&&(a=u),d<h&&(h=d),u>l&&(l=u),d>c&&(c=d);f=Math.max(l-a,c-h),f=f!==0?32767/f:0}return Qe(n,o,e,a,h,f,0),o}function ao(i,t,e,s,r){var n,o;if(r===$s(i,t,e,s)>0)for(n=t;n<e;n+=s)o=co(n,i[n],i[n+1],o);else for(n=e-s;n>=t;n-=s)o=co(n,i[n],i[n+1],o);return o&&zi(o,o.next)&&(ti(o),o=o.next),o}function Se(i,t){if(!i)return i;t||(t=i);var e=i,s;do if(s=!1,!e.steiner&&(zi(e,e.next)||ut(e.prev,e,e.next)===0)){if(ti(e),e=t=e.prev,e===e.next)break;s=!0}else e=e.next;while(s||e!==t);return t}function Qe(i,t,e,s,r,n,o){if(!!i){!o&&n&&$h(i,s,r,n);for(var a=i,h,l;i.prev!==i.next;){if(h=i.prev,l=i.next,n?Gh(i,s,r,n):kh(i)){t.push(h.i/e|0),t.push(i.i/e|0),t.push(l.i/e|0),ti(i),i=l.next,a=l.next;continue}if(i=l,i===a){o?o===1?(i=Hh(Se(i),t,e),Qe(i,t,e,s,r,n,2)):o===2&&Xh(i,t,e,s,r,n):Qe(Se(i),t,e,s,r,n,1);break}}}}function kh(i){var t=i.prev,e=i,s=i.next;if(ut(t,e,s)>=0)return!1;for(var r=t.x,n=e.x,o=s.x,a=t.y,h=e.y,l=s.y,c=r<n?r<o?r:o:n<o?n:o,u=a<h?a<l?a:l:h<l?h:l,d=r>n?r>o?r:o:n>o?n:o,f=a>h?a>l?a:l:h>l?h:l,p=s.next;p!==t;){if(p.x>=c&&p.x<=d&&p.y>=u&&p.y<=f&&Ue(r,a,n,h,o,l,p.x,p.y)&&ut(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function Gh(i,t,e,s){var r=i.prev,n=i,o=i.next;if(ut(r,n,o)>=0)return!1;for(var a=r.x,h=n.x,l=o.x,c=r.y,u=n.y,d=o.y,f=a<h?a<l?a:l:h<l?h:l,p=c<u?c<d?c:d:u<d?u:d,g=a>h?a>l?a:l:h>l?h:l,m=c>u?c>d?c:d:u>d?u:d,x=js(f,p,t,e,s),T=js(g,m,t,e,s),_=i.prevZ,y=i.nextZ;_&&_.z>=x&&y&&y.z<=T;){if(_.x>=f&&_.x<=g&&_.y>=p&&_.y<=m&&_!==r&&_!==o&&Ue(a,c,h,u,l,d,_.x,_.y)&&ut(_.prev,_,_.next)>=0||(_=_.prevZ,y.x>=f&&y.x<=g&&y.y>=p&&y.y<=m&&y!==r&&y!==o&&Ue(a,c,h,u,l,d,y.x,y.y)&&ut(y.prev,y,y.next)>=0))return!1;y=y.nextZ}for(;_&&_.z>=x;){if(_.x>=f&&_.x<=g&&_.y>=p&&_.y<=m&&_!==r&&_!==o&&Ue(a,c,h,u,l,d,_.x,_.y)&&ut(_.prev,_,_.next)>=0)return!1;_=_.prevZ}for(;y&&y.z<=T;){if(y.x>=f&&y.x<=g&&y.y>=p&&y.y<=m&&y!==r&&y!==o&&Ue(a,c,h,u,l,d,y.x,y.y)&&ut(y.prev,y,y.next)>=0)return!1;y=y.nextZ}return!0}function Hh(i,t,e){var s=i;do{var r=s.prev,n=s.next.next;!zi(r,n)&&ho(r,s,s.next,n)&&Je(r,n)&&Je(n,r)&&(t.push(r.i/e|0),t.push(s.i/e|0),t.push(n.i/e|0),ti(s),ti(s.next),s=i=n),s=s.next}while(s!==i);return Se(s)}function Xh(i,t,e,s,r,n){var o=i;do{for(var a=o.next.next;a!==o.prev;){if(o.i!==a.i&&Zh(o,a)){var h=lo(o,a);o=Se(o,o.next),h=Se(h,h.next),Qe(o,t,e,s,r,n,0),Qe(h,t,e,s,r,n,0);return}a=a.next}o=o.next}while(o!==i)}function Vh(i,t,e,s){var r=[],n,o,a,h,l;for(n=0,o=t.length;n<o;n++)a=t[n]*s,h=n<o-1?t[n+1]*s:i.length,l=ao(i,a,h,s,!1),l===l.next&&(l.steiner=!0),r.push(Kh(l));for(r.sort(zh),n=0;n<r.length;n++)e=Wh(r[n],e);return e}function zh(i,t){return i.x-t.x}function Wh(i,t){var e=jh(i,t);if(!e)return t;var s=lo(e,i);return Se(s,s.next),Se(e,e.next)}function jh(i,t){var e=t,s=i.x,r=i.y,n=-1/0,o;do{if(r<=e.y&&r>=e.next.y&&e.next.y!==e.y){var a=e.x+(r-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(a<=s&&a>n&&(n=a,o=e.x<e.next.x?e:e.next,a===s))return o}e=e.next}while(e!==t);if(!o)return null;var h=o,l=o.x,c=o.y,u=1/0,d;e=o;do s>=e.x&&e.x>=l&&s!==e.x&&Ue(r<c?s:n,r,l,c,r<c?n:s,r,e.x,e.y)&&(d=Math.abs(r-e.y)/(s-e.x),Je(e,i)&&(d<u||d===u&&(e.x>o.x||e.x===o.x&&Yh(o,e)))&&(o=e,u=d)),e=e.next;while(e!==h);return o}function Yh(i,t){return ut(i.prev,i,t.prev)<0&&ut(t.next,i,i.next)<0}function $h(i,t,e,s){var r=i;do r.z===0&&(r.z=js(r.x,r.y,t,e,s)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==i);r.prevZ.nextZ=null,r.prevZ=null,qh(r)}function qh(i){var t,e,s,r,n,o,a,h,l=1;do{for(e=i,i=null,n=null,o=0;e;){for(o++,s=e,a=0,t=0;t<l&&(a++,s=s.nextZ,!!s);t++);for(h=l;a>0||h>0&&s;)a!==0&&(h===0||!s||e.z<=s.z)?(r=e,e=e.nextZ,a--):(r=s,s=s.nextZ,h--),n?n.nextZ=r:i=r,r.prevZ=n,n=r;e=s}n.nextZ=null,l*=2}while(o>1);return i}function js(i,t,e,s,r){return i=(i-e)*r|0,t=(t-s)*r|0,i=(i|i<<8)&16711935,i=(i|i<<4)&252645135,i=(i|i<<2)&858993459,i=(i|i<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,i|t<<1}function Kh(i){var t=i,e=i;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==i);return e}function Ue(i,t,e,s,r,n,o,a){return(r-o)*(t-a)>=(i-o)*(n-a)&&(i-o)*(s-a)>=(e-o)*(t-a)&&(e-o)*(n-a)>=(r-o)*(s-a)}function Zh(i,t){return i.next.i!==t.i&&i.prev.i!==t.i&&!Qh(i,t)&&(Je(i,t)&&Je(t,i)&&Jh(i,t)&&(ut(i.prev,i,t.prev)||ut(i,t.prev,t))||zi(i,t)&&ut(i.prev,i,i.next)>0&&ut(t.prev,t,t.next)>0)}function ut(i,t,e){return(t.y-i.y)*(e.x-t.x)-(t.x-i.x)*(e.y-t.y)}function zi(i,t){return i.x===t.x&&i.y===t.y}function ho(i,t,e,s){var r=ji(ut(i,t,e)),n=ji(ut(i,t,s)),o=ji(ut(e,s,i)),a=ji(ut(e,s,t));return!!(r!==n&&o!==a||r===0&&Wi(i,e,t)||n===0&&Wi(i,s,t)||o===0&&Wi(e,i,s)||a===0&&Wi(e,t,s))}function Wi(i,t,e){return t.x<=Math.max(i.x,e.x)&&t.x>=Math.min(i.x,e.x)&&t.y<=Math.max(i.y,e.y)&&t.y>=Math.min(i.y,e.y)}function ji(i){return i>0?1:i<0?-1:0}function Qh(i,t){var e=i;do{if(e.i!==i.i&&e.next.i!==i.i&&e.i!==t.i&&e.next.i!==t.i&&ho(e,e.next,i,t))return!0;e=e.next}while(e!==i);return!1}function Je(i,t){return ut(i.prev,i,i.next)<0?ut(i,t,i.next)>=0&&ut(i,i.prev,t)>=0:ut(i,t,i.prev)<0||ut(i,i.next,t)<0}function Jh(i,t){var e=i,s=!1,r=(i.x+t.x)/2,n=(i.y+t.y)/2;do e.y>n!=e.next.y>n&&e.next.y!==e.y&&r<(e.next.x-e.x)*(n-e.y)/(e.next.y-e.y)+e.x&&(s=!s),e=e.next;while(e!==i);return s}function lo(i,t){var e=new Ys(i.i,i.x,i.y),s=new Ys(t.i,t.x,t.y),r=i.next,n=t.prev;return i.next=t,t.prev=i,e.next=r,r.prev=e,s.next=e,e.prev=s,n.next=s,s.prev=n,s}function co(i,t,e,s){var r=new Ys(i,t,e);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function ti(i){i.next.prev=i.prev,i.prev.next=i.next,i.prevZ&&(i.prevZ.nextZ=i.nextZ),i.nextZ&&(i.nextZ.prevZ=i.prevZ)}function Ys(i,t,e){this.i=i,this.x=t,this.y=e,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}Vi.deviation=function(i,t,e,s){var r=t&&t.length,n=r?t[0]*e:i.length,o=Math.abs($s(i,0,n,e));if(r)for(var a=0,h=t.length;a<h;a++){var l=t[a]*e,c=a<h-1?t[a+1]*e:i.length;o-=Math.abs($s(i,l,c,e))}var u=0;for(a=0;a<s.length;a+=3){var d=s[a]*e,f=s[a+1]*e,p=s[a+2]*e;u+=Math.abs((i[d]-i[p])*(i[f+1]-i[d+1])-(i[d]-i[f])*(i[p+1]-i[d+1]))}return o===0&&u===0?0:Math.abs((u-o)/o)};function $s(i,t,e,s){for(var r=0,n=t,o=e-s;n<e;n+=s)r+=(i[o]-i[n])*(i[n+1]+i[o+1]),o=n;return r}Vi.flatten=function(i){for(var t=i[0][0].length,e={vertices:[],holes:[],dimensions:t},s=0,r=0;r<i.length;r++){for(var n=0;n<i[r].length;n++)for(var o=0;o<t;o++)e.vertices.push(i[r][n][o]);r>0&&(s+=i[r-1].length,e.holes.push(s))}return e},Ws.default=Uh;var tl=zs(function(i,t){/*! https://mths.be/punycode v1.3.2 by @mathias */(function(e){var s=t&&!t.nodeType&&t,r=i&&!i.nodeType&&i,n=typeof Vs=="object"&&Vs;(n.global===n||n.window===n||n.self===n)&&(e=n);var o,a=2147483647,h=36,l=1,c=26,u=38,d=700,f=72,p=128,g="-",m=/^xn--/,x=/[^\x20-\x7E]/,T=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},y=h-l,E=Math.floor,B=String.fromCharCode,A;function b(M){throw RangeError(_[M])}function I(M,V){for(var Y=M.length,q=[];Y--;)q[Y]=V(M[Y]);return q}function O(M,V){var Y=M.split("@"),q="";Y.length>1&&(q=Y[0]+"@",M=Y[1]),M=M.replace(T,".");var K=M.split("."),tt=I(K,V).join(".");return q+tt}function w(M){for(var V=[],Y=0,q=M.length,K,tt;Y<q;)K=M.charCodeAt(Y++),K>=55296&&K<=56319&&Y<q?(tt=M.charCodeAt(Y++),(tt&64512)==56320?V.push(((K&1023)<<10)+(tt&1023)+65536):(V.push(K),Y--)):V.push(K);return V}function D(M){return I(M,function(V){var Y="";return V>65535&&(V-=65536,Y+=B(V>>>10&1023|55296),V=56320|V&1023),Y+=B(V),Y}).join("")}function R(M){return M-48<10?M-22:M-65<26?M-65:M-97<26?M-97:h}function S(M,V){return M+22+75*(M<26)-((V!=0)<<5)}function X(M,V,Y){var q=0;for(M=Y?E(M/d):M>>1,M+=E(M/V);M>y*c>>1;q+=h)M=E(M/y);return E(q+(y+1)*M/(M+u))}function z(M){var V=[],Y=M.length,q,K=0,tt=p,Q=f,it,ht,gt,rt,ct,_t,vt,ie,se;for(it=M.lastIndexOf(g),it<0&&(it=0),ht=0;ht<it;++ht)M.charCodeAt(ht)>=128&&b("not-basic"),V.push(M.charCodeAt(ht));for(gt=it>0?it+1:0;gt<Y;){for(rt=K,ct=1,_t=h;gt>=Y&&b("invalid-input"),vt=R(M.charCodeAt(gt++)),(vt>=h||vt>E((a-K)/ct))&&b("overflow"),K+=vt*ct,ie=_t<=Q?l:_t>=Q+c?c:_t-Q,!(vt<ie);_t+=h)se=h-ie,ct>E(a/se)&&b("overflow"),ct*=se;q=V.length+1,Q=X(K-rt,q,rt==0),E(K/q)>a-tt&&b("overflow"),tt+=E(K/q),K%=q,V.splice(K++,0,tt)}return D(V)}function k(M){var V,Y,q,K,tt,Q,it,ht,gt,rt,ct,_t=[],vt,ie,se,Ni;for(M=w(M),vt=M.length,V=p,Y=0,tt=f,Q=0;Q<vt;++Q)ct=M[Q],ct<128&&_t.push(B(ct));for(q=K=_t.length,K&&_t.push(g);q<vt;){for(it=a,Q=0;Q<vt;++Q)ct=M[Q],ct>=V&&ct<it&&(it=ct);for(ie=q+1,it-V>E((a-Y)/ie)&&b("overflow"),Y+=(it-V)*ie,V=it,Q=0;Q<vt;++Q)if(ct=M[Q],ct<V&&++Y>a&&b("overflow"),ct==V){for(ht=Y,gt=h;rt=gt<=tt?l:gt>=tt+c?c:gt-tt,!(ht<rt);gt+=h)Ni=ht-rt,se=h-rt,_t.push(B(S(rt+Ni%se,0))),ht=E(Ni/se);_t.push(B(S(ht,0))),tt=X(Y,ie,q==K),Y=0,++q}++Y,++V}return _t.join("")}function J(M){return O(M,function(V){return m.test(V)?z(V.slice(4).toLowerCase()):V})}function st(M){return O(M,function(V){return x.test(V)?"xn--"+k(V):V})}if(o={version:"1.3.2",ucs2:{decode:w,encode:D},decode:z,encode:k,toASCII:st,toUnicode:J},s&&r)if(i.exports==s)r.exports=o;else for(A in o)o.hasOwnProperty(A)&&(s[A]=o[A]);else e.punycode=o})(Vs)}),Zt={isString:function(i){return typeof i=="string"},isObject:function(i){return typeof i=="object"&&i!==null},isNull:function(i){return i===null},isNullOrUndefined:function(i){return i==null}};function el(i,t){return Object.prototype.hasOwnProperty.call(i,t)}var il=function(i,t,e,s){t=t||"&",e=e||"=";var r={};if(typeof i!="string"||i.length===0)return r;var n=/\+/g;i=i.split(t);var o=1e3;s&&typeof s.maxKeys=="number"&&(o=s.maxKeys);var a=i.length;o>0&&a>o&&(a=o);for(var h=0;h<a;++h){var l=i[h].replace(n,"%20"),c=l.indexOf(e),u,d,f,p;c>=0?(u=l.substr(0,c),d=l.substr(c+1)):(u=l,d=""),f=decodeURIComponent(u),p=decodeURIComponent(d),el(r,f)?Array.isArray(r[f])?r[f].push(p):r[f]=[r[f],p]:r[f]=p}return r},ei=function(i){switch(typeof i){case"string":return i;case"boolean":return i?"true":"false";case"number":return isFinite(i)?i:"";default:return""}},sl=function(i,t,e,s){return t=t||"&",e=e||"=",i===null&&(i=void 0),typeof i=="object"?Object.keys(i).map(function(r){var n=encodeURIComponent(ei(r))+e;return Array.isArray(i[r])?i[r].map(function(o){return n+encodeURIComponent(ei(o))}).join(t):n+encodeURIComponent(ei(i[r]))}).join(t):s?encodeURIComponent(ei(s))+e+encodeURIComponent(ei(i)):""},qs=zs(function(i,t){"use strict";t.decode=t.parse=il,t.encode=t.stringify=sl}),uo=ii,fo=ml,rl=gl,po=pl,nl=Ft;function Ft(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var ol=/^([a-z0-9.+-]+:)/i,al=/:[0-9]*$/,hl=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,ll=["<",">",'"',"`"," ","\r",`
`,"	"],cl=["{","}","|","\\","^","`"].concat(ll),Ks=["'"].concat(cl),mo=["%","/","?",";","#"].concat(Ks),go=["/","?","#"],ul=255,_o=/^[+a-z0-9A-Z_-]{0,63}$/,dl=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,fl={javascript:!0,"javascript:":!0},Zs={javascript:!0,"javascript:":!0},ke={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function ii(i,t,e){if(i&&Zt.isObject(i)&&i instanceof Ft)return i;var s=new Ft;return s.parse(i,t,e),s}Ft.prototype.parse=function(i,t,e){if(!Zt.isString(i))throw new TypeError("Parameter 'url' must be a string, not "+typeof i);var s=i.indexOf("?"),r=s!==-1&&s<i.indexOf("#")?"?":"#",n=i.split(r),o=/\\/g;n[0]=n[0].replace(o,"/"),i=n.join(r);var a=i;if(a=a.trim(),!e&&i.split("#").length===1){var h=hl.exec(a);if(h)return this.path=a,this.href=a,this.pathname=h[1],h[2]?(this.search=h[2],t?this.query=qs.parse(this.search.substr(1)):this.query=this.search.substr(1)):t&&(this.search="",this.query={}),this}var l=ol.exec(a);if(l){l=l[0];var c=l.toLowerCase();this.protocol=c,a=a.substr(l.length)}if(e||l||a.match(/^\/\/[^@\/]+@[^@\/]+/)){var u=a.substr(0,2)==="//";u&&!(l&&Zs[l])&&(a=a.substr(2),this.slashes=!0)}if(!Zs[l]&&(u||l&&!ke[l])){for(var d=-1,f=0;f<go.length;f++){var p=a.indexOf(go[f]);p!==-1&&(d===-1||p<d)&&(d=p)}var g,m;d===-1?m=a.lastIndexOf("@"):m=a.lastIndexOf("@",d),m!==-1&&(g=a.slice(0,m),a=a.slice(m+1),this.auth=decodeURIComponent(g)),d=-1;for(var f=0;f<mo.length;f++){var p=a.indexOf(mo[f]);p!==-1&&(d===-1||p<d)&&(d=p)}d===-1&&(d=a.length),this.host=a.slice(0,d),a=a.slice(d),this.parseHost(),this.hostname=this.hostname||"";var x=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!x)for(var T=this.hostname.split(/\./),f=0,_=T.length;f<_;f++){var y=T[f];if(!!y&&!y.match(_o)){for(var E="",B=0,A=y.length;B<A;B++)y.charCodeAt(B)>127?E+="x":E+=y[B];if(!E.match(_o)){var b=T.slice(0,f),I=T.slice(f+1),O=y.match(dl);O&&(b.push(O[1]),I.unshift(O[2])),I.length&&(a="/"+I.join(".")+a),this.hostname=b.join(".");break}}}this.hostname.length>ul?this.hostname="":this.hostname=this.hostname.toLowerCase(),x||(this.hostname=tl.toASCII(this.hostname));var w=this.port?":"+this.port:"",D=this.hostname||"";this.host=D+w,this.href+=this.host,x&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),a[0]!=="/"&&(a="/"+a))}if(!fl[c])for(var f=0,_=Ks.length;f<_;f++){var R=Ks[f];if(a.indexOf(R)!==-1){var S=encodeURIComponent(R);S===R&&(S=escape(R)),a=a.split(R).join(S)}}var X=a.indexOf("#");X!==-1&&(this.hash=a.substr(X),a=a.slice(0,X));var z=a.indexOf("?");if(z!==-1?(this.search=a.substr(z),this.query=a.substr(z+1),t&&(this.query=qs.parse(this.query)),a=a.slice(0,z)):t&&(this.search="",this.query={}),a&&(this.pathname=a),ke[c]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var w=this.pathname||"",k=this.search||"";this.path=w+k}return this.href=this.format(),this};function pl(i){return Zt.isString(i)&&(i=ii(i)),i instanceof Ft?i.format():Ft.prototype.format.call(i)}Ft.prototype.format=function(){var i=this.auth||"";i&&(i=encodeURIComponent(i),i=i.replace(/%3A/i,":"),i+="@");var t=this.protocol||"",e=this.pathname||"",s=this.hash||"",r=!1,n="";this.host?r=i+this.host:this.hostname&&(r=i+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(r+=":"+this.port)),this.query&&Zt.isObject(this.query)&&Object.keys(this.query).length&&(n=qs.stringify(this.query));var o=this.search||n&&"?"+n||"";return t&&t.substr(-1)!==":"&&(t+=":"),this.slashes||(!t||ke[t])&&r!==!1?(r="//"+(r||""),e&&e.charAt(0)!=="/"&&(e="/"+e)):r||(r=""),s&&s.charAt(0)!=="#"&&(s="#"+s),o&&o.charAt(0)!=="?"&&(o="?"+o),e=e.replace(/[?#]/g,function(a){return encodeURIComponent(a)}),o=o.replace("#","%23"),t+r+e+o+s};function ml(i,t){return ii(i,!1,!0).resolve(t)}Ft.prototype.resolve=function(i){return this.resolveObject(ii(i,!1,!0)).format()};function gl(i,t){return i?ii(i,!1,!0).resolveObject(t):t}Ft.prototype.resolveObject=function(i){if(Zt.isString(i)){var t=new Ft;t.parse(i,!1,!0),i=t}for(var e=new Ft,s=Object.keys(this),r=0;r<s.length;r++){var n=s[r];e[n]=this[n]}if(e.hash=i.hash,i.href==="")return e.href=e.format(),e;if(i.slashes&&!i.protocol){for(var o=Object.keys(i),a=0;a<o.length;a++){var h=o[a];h!=="protocol"&&(e[h]=i[h])}return ke[e.protocol]&&e.hostname&&!e.pathname&&(e.path=e.pathname="/"),e.href=e.format(),e}if(i.protocol&&i.protocol!==e.protocol){if(!ke[i.protocol]){for(var l=Object.keys(i),c=0;c<l.length;c++){var u=l[c];e[u]=i[u]}return e.href=e.format(),e}if(e.protocol=i.protocol,!i.host&&!Zs[i.protocol]){for(var _=(i.pathname||"").split("/");_.length&&!(i.host=_.shift()););i.host||(i.host=""),i.hostname||(i.hostname=""),_[0]!==""&&_.unshift(""),_.length<2&&_.unshift(""),e.pathname=_.join("/")}else e.pathname=i.pathname;if(e.search=i.search,e.query=i.query,e.host=i.host||"",e.auth=i.auth,e.hostname=i.hostname||i.host,e.port=i.port,e.pathname||e.search){var d=e.pathname||"",f=e.search||"";e.path=d+f}return e.slashes=e.slashes||i.slashes,e.href=e.format(),e}var p=e.pathname&&e.pathname.charAt(0)==="/",g=i.host||i.pathname&&i.pathname.charAt(0)==="/",m=g||p||e.host&&i.pathname,x=m,T=e.pathname&&e.pathname.split("/")||[],_=i.pathname&&i.pathname.split("/")||[],y=e.protocol&&!ke[e.protocol];if(y&&(e.hostname="",e.port=null,e.host&&(T[0]===""?T[0]=e.host:T.unshift(e.host)),e.host="",i.protocol&&(i.hostname=null,i.port=null,i.host&&(_[0]===""?_[0]=i.host:_.unshift(i.host)),i.host=null),m=m&&(_[0]===""||T[0]==="")),g)e.host=i.host||i.host===""?i.host:e.host,e.hostname=i.hostname||i.hostname===""?i.hostname:e.hostname,e.search=i.search,e.query=i.query,T=_;else if(_.length)T||(T=[]),T.pop(),T=T.concat(_),e.search=i.search,e.query=i.query;else if(!Zt.isNullOrUndefined(i.search)){if(y){e.hostname=e.host=T.shift();var E=e.host&&e.host.indexOf("@")>0?e.host.split("@"):!1;E&&(e.auth=E.shift(),e.host=e.hostname=E.shift())}return e.search=i.search,e.query=i.query,(!Zt.isNull(e.pathname)||!Zt.isNull(e.search))&&(e.path=(e.pathname?e.pathname:"")+(e.search?e.search:"")),e.href=e.format(),e}if(!T.length)return e.pathname=null,e.search?e.path="/"+e.search:e.path=null,e.href=e.format(),e;for(var B=T.slice(-1)[0],A=(e.host||i.host||T.length>1)&&(B==="."||B==="..")||B==="",b=0,I=T.length;I>=0;I--)B=T[I],B==="."?T.splice(I,1):B===".."?(T.splice(I,1),b++):b&&(T.splice(I,1),b--);if(!m&&!x)for(;b--;b)T.unshift("..");m&&T[0]!==""&&(!T[0]||T[0].charAt(0)!=="/")&&T.unshift(""),A&&T.join("/").substr(-1)!=="/"&&T.push("");var O=T[0]===""||T[0]&&T[0].charAt(0)==="/";if(y){e.hostname=e.host=O?"":T.length?T.shift():"";var E=e.host&&e.host.indexOf("@")>0?e.host.split("@"):!1;E&&(e.auth=E.shift(),e.host=e.hostname=E.shift())}return m=m||e.host&&T.length,m&&!O&&T.unshift(""),T.length?e.pathname=T.join("/"):(e.pathname=null,e.path=null),(!Zt.isNull(e.pathname)||!Zt.isNull(e.search))&&(e.path=(e.pathname?e.pathname:"")+(e.search?e.search:"")),e.auth=i.auth||e.auth,e.slashes=e.slashes||i.slashes,e.href=e.format(),e},Ft.prototype.parseHost=function(){var i=this.host,t=al.exec(i);t&&(t=t[0],t!==":"&&(this.port=t.substr(1)),i=i.substr(0,i.length-t.length)),i&&(this.hostname=i)};var um={parse:uo,resolve:fo,resolveObject:rl,format:po,Url:nl};const vo={parse:uo,format:po,resolve:fo};function zt(i){if(typeof i!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(i)}`)}function yo(i){return i.split("?")[0].split("#")[0]}function _l(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function vl(i,t,e){return i.replace(new RegExp(_l(t),"g"),e)}function yl(i,t){let e="",s=0,r=-1,n=0,o;for(let a=0;a<=i.length;++a){if(a<i.length)o=i.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(r===a-1||n===1))if(r!==a-1&&n===2){if(e.length<2||s!==2||e.charCodeAt(e.length-1)!==46||e.charCodeAt(e.length-2)!==46){if(e.length>2){const h=e.lastIndexOf("/");if(h!==e.length-1){h===-1?(e="",s=0):(e=e.slice(0,h),s=e.length-1-e.lastIndexOf("/")),r=a,n=0;continue}}else if(e.length===2||e.length===1){e="",s=0,r=a,n=0;continue}}t&&(e.length>0?e+="/..":e="..",s=2)}else e.length>0?e+=`/${i.slice(r+1,a)}`:e=i.slice(r+1,a),s=a-r-1;r=a,n=0}else o===46&&n!==-1?++n:n=-1}return e}const yt={toPosix(i){return vl(i,"\\","/")},isUrl(i){return/^https?:/.test(this.toPosix(i))},isDataUrl(i){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(i)},hasProtocol(i){return/^[^/:]+:\//.test(this.toPosix(i))},getProtocol(i){zt(i),i=this.toPosix(i);let t="";const e=/^file:\/\/\//.exec(i),s=/^[^/:]+:\/\//.exec(i),r=/^[^/:]+:\//.exec(i);if(e||s||r){const n=(e==null?void 0:e[0])||(s==null?void 0:s[0])||(r==null?void 0:r[0]);t=n,i=i.slice(n.length)}return t},toAbsolute(i,t,e){if(this.isDataUrl(i))return i;const s=yo(this.toPosix(t!=null?t:P.ADAPTER.getBaseUrl())),r=yo(this.toPosix(e!=null?e:this.rootname(s)));return zt(i),i=this.toPosix(i),i.startsWith("/")?yt.join(r,i.slice(1)):this.isAbsolute(i)?i:this.join(s,i)},normalize(i){if(i=this.toPosix(i),zt(i),i.length===0)return".";let t="";const e=i.startsWith("/");this.hasProtocol(i)&&(t=this.rootname(i),i=i.slice(t.length));const s=i.endsWith("/");return i=yl(i,!1),i.length>0&&s&&(i+="/"),e?`/${i}`:t+i},isAbsolute(i){return zt(i),i=this.toPosix(i),this.hasProtocol(i)?!0:i.startsWith("/")},join(...i){var e;if(i.length===0)return".";let t;for(let s=0;s<i.length;++s){const r=i[s];if(zt(r),r.length>0)if(t===void 0)t=r;else{const n=(e=i[s-1])!=null?e:"";this.extname(n)?t+=`/../${r}`:t+=`/${r}`}}return t===void 0?".":this.normalize(t)},dirname(i){if(zt(i),i.length===0)return".";i=this.toPosix(i);let t=i.charCodeAt(0);const e=t===47;let s=-1,r=!0;const n=this.getProtocol(i),o=i;i=i.slice(n.length);for(let a=i.length-1;a>=1;--a)if(t=i.charCodeAt(a),t===47){if(!r){s=a;break}}else r=!1;return s===-1?e?"/":this.isUrl(o)?n+i:n:e&&s===1?"//":n+i.slice(0,s)},rootname(i){zt(i),i=this.toPosix(i);let t="";if(i.startsWith("/")?t="/":t=this.getProtocol(i),this.isUrl(i)){const e=i.indexOf("/",t.length);e!==-1?t=i.slice(0,e):t=i,t.endsWith("/")||(t+="/")}return t},basename(i,t){zt(i),t&&zt(t),i=this.toPosix(i);let e=0,s=-1,r=!0,n;if(t!==void 0&&t.length>0&&t.length<=i.length){if(t.length===i.length&&t===i)return"";let o=t.length-1,a=-1;for(n=i.length-1;n>=0;--n){const h=i.charCodeAt(n);if(h===47){if(!r){e=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(h===t.charCodeAt(o)?--o===-1&&(s=n):(o=-1,s=a))}return e===s?s=a:s===-1&&(s=i.length),i.slice(e,s)}for(n=i.length-1;n>=0;--n)if(i.charCodeAt(n)===47){if(!r){e=n+1;break}}else s===-1&&(r=!1,s=n+1);return s===-1?"":i.slice(e,s)},extname(i){zt(i),i=this.toPosix(i);let t=-1,e=0,s=-1,r=!0,n=0;for(let o=i.length-1;o>=0;--o){const a=i.charCodeAt(o);if(a===47){if(!r){e=o+1;break}continue}s===-1&&(r=!1,s=o+1),a===46?t===-1?t=o:n!==1&&(n=1):t!==-1&&(n=-1)}return t===-1||s===-1||n===0||n===1&&t===s-1&&t===e+1?"":i.slice(t,s)},parse(i){zt(i);const t={root:"",dir:"",base:"",ext:"",name:""};if(i.length===0)return t;i=this.toPosix(i);let e=i.charCodeAt(0);const s=this.isAbsolute(i);let r;const n="";t.root=this.rootname(i),s||this.hasProtocol(i)?r=1:r=0;let o=-1,a=0,h=-1,l=!0,c=i.length-1,u=0;for(;c>=r;--c){if(e=i.charCodeAt(c),e===47){if(!l){a=c+1;break}continue}h===-1&&(l=!1,h=c+1),e===46?o===-1?o=c:u!==1&&(u=1):o!==-1&&(u=-1)}return o===-1||h===-1||u===0||u===1&&o===h-1&&o===a+1?h!==-1&&(a===0&&s?t.base=t.name=i.slice(1,h):t.base=t.name=i.slice(a,h)):(a===0&&s?(t.name=i.slice(1,o),t.base=i.slice(1,h)):(t.name=i.slice(a,o),t.base=i.slice(a,h)),t.ext=i.slice(o,h)),t.dir=this.dirname(i),n&&(t.dir=n+t.dir),t},sep:"/",delimiter:":"};P.RETINA_PREFIX=/@([0-9\.]+)x/,P.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT=!1;const xo={};function bt(i,t,e=3){if(xo[t])return;let s=new Error().stack;typeof s=="undefined"?console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${i}`):(s=s.split(`
`).splice(e).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${t}
Deprecated since v${i}`),console.warn(s),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${i}`),console.warn(s))),xo[t]=!0}function xl(){bt("7.0.0","skipHello is deprecated, please use PIXI.settings.RENDER_OPTIONS.hello")}function Tl(){bt("7.0.0",`sayHello is deprecated, please use Renderer's "hello" option`)}let Qs;function To(){return typeof Qs=="undefined"&&(Qs=function(){const t={stencil:!0,failIfMajorPerformanceCaveat:P.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT};try{if(!P.ADAPTER.getWebGLRenderingContext())return!1;const e=P.ADAPTER.createCanvas();let s=e.getContext("webgl",t)||e.getContext("experimental-webgl",t);const r=!!(s&&s.getContextAttributes().stencil);if(s){const n=s.getExtension("WEBGL_lose_context");n&&n.loseContext()}return s=null,r}catch(e){return!1}}()),Qs}var bl="#f0f8ff",El="#faebd7",wl="#00ffff",Sl="#7fffd4",Al="#f0ffff",Cl="#f5f5dc",Rl="#ffe4c4",Il="#000000",Pl="#ffebcd",Ml="#0000ff",Bl="#8a2be2",Dl="#a52a2a",Nl="#deb887",Fl="#5f9ea0",Ol="#7fff00",Ll="#d2691e",Ul="#ff7f50",kl="#6495ed",Gl="#fff8dc",Hl="#dc143c",Xl="#00ffff",Vl="#00008b",zl="#008b8b",Wl="#b8860b",jl="#a9a9a9",Yl="#006400",$l="#a9a9a9",ql="#bdb76b",Kl="#8b008b",Zl="#556b2f",Ql="#ff8c00",Jl="#9932cc",tc="#8b0000",ec="#e9967a",ic="#8fbc8f",sc="#483d8b",rc="#2f4f4f",nc="#2f4f4f",oc="#00ced1",ac="#9400d3",hc="#ff1493",lc="#00bfff",cc="#696969",uc="#696969",dc="#1e90ff",fc="#b22222",pc="#fffaf0",mc="#228b22",gc="#ff00ff",_c="#dcdcdc",vc="#f8f8ff",yc="#daa520",xc="#ffd700",Tc="#808080",bc="#008000",Ec="#adff2f",wc="#808080",Sc="#f0fff0",Ac="#ff69b4",Cc="#cd5c5c",Rc="#4b0082",Ic="#fffff0",Pc="#f0e68c",Mc="#fff0f5",Bc="#e6e6fa",Dc="#7cfc00",Nc="#fffacd",Fc="#add8e6",Oc="#f08080",Lc="#e0ffff",Uc="#fafad2",kc="#d3d3d3",Gc="#90ee90",Hc="#d3d3d3",Xc="#ffb6c1",Vc="#ffa07a",zc="#20b2aa",Wc="#87cefa",jc="#778899",Yc="#778899",$c="#b0c4de",qc="#ffffe0",Kc="#00ff00",Zc="#32cd32",Qc="#faf0e6",Jc="#ff00ff",tu="#800000",eu="#66cdaa",iu="#0000cd",su="#ba55d3",ru="#9370db",nu="#3cb371",ou="#7b68ee",au="#00fa9a",hu="#48d1cc",lu="#c71585",cu="#191970",uu="#f5fffa",du="#ffe4e1",fu="#ffe4b5",pu="#ffdead",mu="#000080",gu="#fdf5e6",_u="#808000",vu="#6b8e23",yu="#ffa500",xu="#ff4500",Tu="#da70d6",bu="#eee8aa",Eu="#98fb98",wu="#afeeee",Su="#db7093",Au="#ffefd5",Cu="#ffdab9",Ru="#cd853f",Iu="#ffc0cb",Pu="#dda0dd",Mu="#b0e0e6",Bu="#800080",Du="#663399",Nu="#ff0000",Fu="#bc8f8f",Ou="#4169e1",Lu="#8b4513",Uu="#fa8072",ku="#f4a460",Gu="#2e8b57",Hu="#fff5ee",Xu="#a0522d",Vu="#c0c0c0",zu="#87ceeb",Wu="#6a5acd",ju="#708090",Yu="#708090",$u="#fffafa",qu="#00ff7f",Ku="#4682b4",Zu="#d2b48c",Qu="#008080",Ju="#d8bfd8",td="#ff6347",ed="#40e0d0",id="#ee82ee",sd="#f5deb3",rd="#ffffff",nd="#f5f5f5",od="#ffff00",ad="#9acd32",hd={aliceblue:bl,antiquewhite:El,aqua:wl,aquamarine:Sl,azure:Al,beige:Cl,bisque:Rl,black:Il,blanchedalmond:Pl,blue:Ml,blueviolet:Bl,brown:Dl,burlywood:Nl,cadetblue:Fl,chartreuse:Ol,chocolate:Ll,coral:Ul,cornflowerblue:kl,cornsilk:Gl,crimson:Hl,cyan:Xl,darkblue:Vl,darkcyan:zl,darkgoldenrod:Wl,darkgray:jl,darkgreen:Yl,darkgrey:$l,darkkhaki:ql,darkmagenta:Kl,darkolivegreen:Zl,darkorange:Ql,darkorchid:Jl,darkred:tc,darksalmon:ec,darkseagreen:ic,darkslateblue:sc,darkslategray:rc,darkslategrey:nc,darkturquoise:oc,darkviolet:ac,deeppink:hc,deepskyblue:lc,dimgray:cc,dimgrey:uc,dodgerblue:dc,firebrick:fc,floralwhite:pc,forestgreen:mc,fuchsia:gc,gainsboro:_c,ghostwhite:vc,goldenrod:yc,gold:xc,gray:Tc,green:bc,greenyellow:Ec,grey:wc,honeydew:Sc,hotpink:Ac,indianred:Cc,indigo:Rc,ivory:Ic,khaki:Pc,lavenderblush:Mc,lavender:Bc,lawngreen:Dc,lemonchiffon:Nc,lightblue:Fc,lightcoral:Oc,lightcyan:Lc,lightgoldenrodyellow:Uc,lightgray:kc,lightgreen:Gc,lightgrey:Hc,lightpink:Xc,lightsalmon:Vc,lightseagreen:zc,lightskyblue:Wc,lightslategray:jc,lightslategrey:Yc,lightsteelblue:$c,lightyellow:qc,lime:Kc,limegreen:Zc,linen:Qc,magenta:Jc,maroon:tu,mediumaquamarine:eu,mediumblue:iu,mediumorchid:su,mediumpurple:ru,mediumseagreen:nu,mediumslateblue:ou,mediumspringgreen:au,mediumturquoise:hu,mediumvioletred:lu,midnightblue:cu,mintcream:uu,mistyrose:du,moccasin:fu,navajowhite:pu,navy:mu,oldlace:gu,olive:_u,olivedrab:vu,orange:yu,orangered:xu,orchid:Tu,palegoldenrod:bu,palegreen:Eu,paleturquoise:wu,palevioletred:Su,papayawhip:Au,peachpuff:Cu,peru:Ru,pink:Iu,plum:Pu,powderblue:Mu,purple:Bu,rebeccapurple:Du,red:Nu,rosybrown:Fu,royalblue:Ou,saddlebrown:Lu,salmon:Uu,sandybrown:ku,seagreen:Gu,seashell:Hu,sienna:Xu,silver:Vu,skyblue:zu,slateblue:Wu,slategray:ju,slategrey:Yu,snow:$u,springgreen:qu,steelblue:Ku,tan:Zu,teal:Qu,thistle:Ju,tomato:td,turquoise:ed,violet:id,wheat:sd,white:rd,whitesmoke:nd,yellow:od,yellowgreen:ad};function ae(i,t=[]){return t[0]=(i>>16&255)/255,t[1]=(i>>8&255)/255,t[2]=(i&255)/255,t}function Yi(i){let t=i.toString(16);return t="000000".substring(0,6-t.length)+t,`#${t}`}function $i(i){if(typeof i=="string"&&(i=hd[i.toLowerCase()]||i,i[0]==="#"&&(i=i.slice(1)),i.length===3)){const[t,e,s]=i;i=t+t+e+e+s+s}return parseInt(i,16)}function Js(i){return(i[0]*255<<16)+(i[1]*255<<8)+(i[2]*255|0)}function ld(){const i=[],t=[];for(let s=0;s<32;s++)i[s]=s,t[s]=s;i[C.NORMAL_NPM]=C.NORMAL,i[C.ADD_NPM]=C.ADD,i[C.SCREEN_NPM]=C.SCREEN,t[C.NORMAL]=C.NORMAL_NPM,t[C.ADD]=C.ADD_NPM,t[C.SCREEN]=C.SCREEN_NPM;const e=[];return e.push(t),e.push(i),e}const tr=ld();function er(i,t){return tr[t?1:0][i]}function bo(i,t,e,s){return e=e||new Float32Array(4),s||s===void 0?(e[0]=i[0]*t,e[1]=i[1]*t,e[2]=i[2]*t):(e[0]=i[0],e[1]=i[1],e[2]=i[2]),e[3]=t,e}function qi(i,t){if(t===1)return(t*255<<24)+i;if(t===0)return 0;let e=i>>16&255,s=i>>8&255,r=i&255;return e=e*t+.5|0,s=s*t+.5|0,r=r*t+.5|0,(t*255<<24)+(e<<16)+(s<<8)+r}function ir(i,t,e,s){return e=e||new Float32Array(4),e[0]=(i>>16&255)/255,e[1]=(i>>8&255)/255,e[2]=(i&255)/255,(s||s===void 0)&&(e[0]*=t,e[1]*=t,e[2]*=t),e[3]=t,e}function Eo(i,t=null){const e=i*6;if(t=t||new Uint16Array(e),t.length!==e)throw new Error(`Out buffer length is incorrect, got ${t.length} and expected ${e}`);for(let s=0,r=0;s<e;s+=6,r+=4)t[s+0]=r+0,t[s+1]=r+1,t[s+2]=r+2,t[s+3]=r+0,t[s+4]=r+2,t[s+5]=r+3;return t}function Ki(i){if(i.BYTES_PER_ELEMENT===4)return i instanceof Float32Array?"Float32Array":i instanceof Uint32Array?"Uint32Array":"Int32Array";if(i.BYTES_PER_ELEMENT===2){if(i instanceof Uint16Array)return"Uint16Array"}else if(i.BYTES_PER_ELEMENT===1&&i instanceof Uint8Array)return"Uint8Array";return null}const cd={Float32Array,Uint32Array,Int32Array,Uint8Array};function ud(i,t){let e=0,s=0;const r={};for(let h=0;h<i.length;h++)s+=t[h],e+=i[h].length;const n=new ArrayBuffer(e*4);let o=null,a=0;for(let h=0;h<i.length;h++){const l=t[h],c=i[h],u=Ki(c);r[u]||(r[u]=new cd[u](n)),o=r[u];for(let d=0;d<c.length;d++){const f=(d/l|0)*s+a,p=d%l;o[f+p]=c[d]}a+=l}return new Float32Array(n)}function si(i){return i+=i===0?1:0,--i,i|=i>>>1,i|=i>>>2,i|=i>>>4,i|=i>>>8,i|=i>>>16,i+1}function sr(i){return!(i&i-1)&&!!i}function rr(i){let t=(i>65535?1:0)<<4;i>>>=t;let e=(i>255?1:0)<<3;return i>>>=e,t|=e,e=(i>15?1:0)<<2,i>>>=e,t|=e,e=(i>3?1:0)<<1,i>>>=e,t|=e,t|i>>1}function Ae(i,t,e){const s=i.length;let r;if(t>=s||e===0)return;e=t+e>s?s-t:e;const n=s-e;for(r=t;r<n;++r)i[r]=i[r+e];i.length=n}function Ce(i){return i===0?0:i<0?-1:1}let dd=0;function me(){return++dd}const nr={},Et=Object.create(null),Pt=Object.create(null);function fd(){let i;for(i in Et)Et[i].destroy();for(i in Pt)Pt[i].destroy()}function pd(){let i;for(i in Et)delete Et[i];for(i in Pt)delete Pt[i]}class Ge{constructor(t,e,s){this.canvas=P.ADAPTER.createCanvas(),this.context=this.canvas.getContext("2d"),this.resolution=s||P.RESOLUTION,this.resize(t,e)}clear(){this.context.setTransform(1,0,0,1,0,0),this.context.clearRect(0,0,this.canvas.width,this.canvas.height)}resize(t,e){this.canvas.width=Math.round(t*this.resolution),this.canvas.height=Math.round(e*this.resolution)}destroy(){this.context=null,this.canvas=null}get width(){return this.canvas.width}set width(t){this.canvas.width=Math.round(t)}get height(){return this.canvas.height}set height(t){this.canvas.height=Math.round(t)}}function wo(i,t,e){for(let s=0,r=4*e*t;s<t;++s,r+=4)if(i[r+3]!==0)return!1;return!0}function So(i,t,e,s,r){const n=4*t;for(let o=s,a=s*n+4*e;o<=r;++o,a+=n)if(i[a+3]!==0)return!1;return!0}function Ao(i){let{width:t,height:e}=i;const s=i.getContext("2d",{willReadFrequently:!0}),n=s.getImageData(0,0,t,e).data;let o=0,a=e-1,h=0,l=t-1;for(;o<e&&wo(n,t,o);)++o;if(o===e)return{width:0,height:0,data:null};for(;wo(n,t,a);)--a;for(;So(n,t,h,o,a);)++h;for(;So(n,t,l,o,a);)--l;return t=l-h+1,e=a-o+1,{width:t,height:e,data:s.getImageData(h,o,t,e)}}const Co=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;function md(i){const t=Co.exec(i);if(t)return{mediaType:t[1]?t[1].toLowerCase():void 0,subType:t[2]?t[2].toLowerCase():void 0,charset:t[3]?t[3].toLowerCase():void 0,encoding:t[4]?t[4].toLowerCase():void 0,data:t[5]}}let Zi;function Ro(i,t=globalThis.location){if(i.startsWith("data:"))return"";t=t||globalThis.location,Zi||(Zi=document.createElement("a")),Zi.href=i;const e=vo.parse(Zi.href),s=!e.port&&t.port===""||e.port===t.port;return e.hostname!==t.hostname||!s||e.protocol!==t.protocol?"anonymous":""}function he(i,t=1){const e=P.RETINA_PREFIX.exec(i);return e?parseFloat(e[1]):t}var Io={__proto__:null,isMobile:Vt,EventEmitter:Le,earcut:Ws,url:vo,path:yt,sayHello:Tl,skipHello:xl,isWebGLSupported:To,hex2rgb:ae,hex2string:Yi,rgb2hex:Js,string2hex:$i,correctBlendMode:er,premultiplyBlendMode:tr,premultiplyRgba:bo,premultiplyTint:qi,premultiplyTintToRgba:ir,createIndicesForQuads:Eo,getBufferType:Ki,interleaveTypedArrays:ud,isPow2:sr,log2:rr,nextPow2:si,removeItems:Ae,sign:Ce,uid:me,deprecation:bt,BaseTextureCache:Pt,ProgramCache:nr,TextureCache:Et,clearTextureCache:pd,destroyTextureCache:fd,CanvasRenderTarget:Ge,trimCanvas:Ao,decomposeDataUri:md,determineCrossOrigin:Ro,getResolutionOfUrl:he,DATA_URI:Co};const Qi=[];function or(i,t){if(!i)return null;let e="";if(typeof i=="string"){const s=/\.(\w{3,4})(?:$|\?|#)/i.exec(i);s&&(e=s[1].toLowerCase())}for(let s=Qi.length-1;s>=0;--s){const r=Qi[s];if(r.test&&r.test(i,e))return new r(i,t)}throw new Error("Unrecognized source type to auto-detect Resource")}class He{constructor(t=0,e=0){this._width=t,this._height=e,this.destroyed=!1,this.internal=!1,this.onResize=new It("setRealSize"),this.onUpdate=new It("update"),this.onError=new It("onError")}bind(t){this.onResize.add(t),this.onUpdate.add(t),this.onError.add(t),(this._width||this._height)&&this.onResize.emit(this._width,this._height)}unbind(t){this.onResize.remove(t),this.onUpdate.remove(t),this.onError.remove(t)}resize(t,e){(t!==this._width||e!==this._height)&&(this._width=t,this._height=e,this.onResize.emit(t,e))}get valid(){return!!this._width&&!!this._height}update(){this.destroyed||this.onUpdate.emit()}load(){return Promise.resolve(this)}get width(){return this._width}get height(){return this._height}style(t,e,s){return!1}dispose(){}destroy(){this.destroyed||(this.destroyed=!0,this.dispose(),this.onError.removeAll(),this.onError=null,this.onResize.removeAll(),this.onResize=null,this.onUpdate.removeAll(),this.onUpdate=null)}static test(t,e){return!1}}class Xe extends He{constructor(t,e){const{width:s,height:r}=e||{};if(!s||!r)throw new Error("BufferResource width or height invalid");super(s,r),this.data=t}upload(t,e,s){const r=t.gl;r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.alphaMode===Nt.UNPACK);const n=e.realWidth,o=e.realHeight;return s.width===n&&s.height===o?r.texSubImage2D(e.target,0,0,0,n,o,e.format,s.type,this.data):(s.width=n,s.height=o,r.texImage2D(e.target,0,s.internalFormat,n,o,0,e.format,s.type,this.data)),!0}dispose(){this.data=null}static test(t){return t instanceof Float32Array||t instanceof Uint8Array||t instanceof Uint32Array}}const gd={scaleMode:Dt.NEAREST,format:N.RGBA,alphaMode:Nt.NPM},ri=class extends Le{constructor(i=null,t=null){super(),t=t||{};const{alphaMode:e,mipmap:s,anisotropicLevel:r,scaleMode:n,width:o,height:a,wrapMode:h,format:l,type:c,target:u,resolution:d,resourceOptions:f}=t;i&&!(i instanceof He)&&(i=or(i,f),i.internal=!0),this.resolution=d||P.RESOLUTION,this.width=Math.round((o||0)*this.resolution)/this.resolution,this.height=Math.round((a||0)*this.resolution)/this.resolution,this._mipmap=s!=null?s:P.MIPMAP_TEXTURES,this.anisotropicLevel=r!=null?r:P.ANISOTROPIC_LEVEL,this._wrapMode=h||P.WRAP_MODE,this._scaleMode=n!=null?n:P.SCALE_MODE,this.format=l||N.RGBA,this.type=c||H.UNSIGNED_BYTE,this.target=u||ye.TEXTURE_2D,this.alphaMode=e!=null?e:Nt.UNPACK,this.uid=me(),this.touched=0,this.isPowerOfTwo=!1,this._refreshPOT(),this._glTextures={},this.dirtyId=0,this.dirtyStyleId=0,this.cacheId=null,this.valid=o>0&&a>0,this.textureCacheIds=[],this.destroyed=!1,this.resource=null,this._batchEnabled=0,this._batchLocation=0,this.parentTextureArray=null,this.setResource(i)}get realWidth(){return Math.round(this.width*this.resolution)}get realHeight(){return Math.round(this.height*this.resolution)}get mipmap(){return this._mipmap}set mipmap(i){this._mipmap!==i&&(this._mipmap=i,this.dirtyStyleId++)}get scaleMode(){return this._scaleMode}set scaleMode(i){this._scaleMode!==i&&(this._scaleMode=i,this.dirtyStyleId++)}get wrapMode(){return this._wrapMode}set wrapMode(i){this._wrapMode!==i&&(this._wrapMode=i,this.dirtyStyleId++)}setStyle(i,t){let e;return i!==void 0&&i!==this.scaleMode&&(this.scaleMode=i,e=!0),t!==void 0&&t!==this.mipmap&&(this.mipmap=t,e=!0),e&&this.dirtyStyleId++,this}setSize(i,t,e){return e=e||this.resolution,this.setRealSize(i*e,t*e,e)}setRealSize(i,t,e){return this.resolution=e||this.resolution,this.width=Math.round(i)/this.resolution,this.height=Math.round(t)/this.resolution,this._refreshPOT(),this.update(),this}_refreshPOT(){this.isPowerOfTwo=sr(this.realWidth)&&sr(this.realHeight)}setResolution(i){const t=this.resolution;return t===i?this:(this.resolution=i,this.valid&&(this.width=Math.round(this.width*t)/i,this.height=Math.round(this.height*t)/i,this.emit("update",this)),this._refreshPOT(),this)}setResource(i){if(this.resource===i)return this;if(this.resource)throw new Error("Resource can be set only once");return i.bind(this),this.resource=i,this}update(){this.valid?(this.dirtyId++,this.dirtyStyleId++,this.emit("update",this)):this.width>0&&this.height>0&&(this.valid=!0,this.emit("loaded",this),this.emit("update",this))}onError(i){this.emit("error",this,i)}destroy(){this.resource&&(this.resource.unbind(this),this.resource.internal&&this.resource.destroy(),this.resource=null),this.cacheId&&(delete Pt[this.cacheId],delete Et[this.cacheId],this.cacheId=null),this.dispose(),ri.removeFromCache(this),this.textureCacheIds=null,this.destroyed=!0}dispose(){this.emit("dispose",this)}castToBaseTexture(){return this}static from(i,t,e=P.STRICT_TEXTURE_CACHE){const s=typeof i=="string";let r=null;if(s)r=i;else{if(!i._pixiId){const o=(t==null?void 0:t.pixiIdPrefix)||"pixiid";i._pixiId=`${o}_${me()}`}r=i._pixiId}let n=Pt[r];if(s&&e&&!n)throw new Error(`The cacheId "${r}" does not exist in BaseTextureCache.`);return n||(n=new ri(i,t),n.cacheId=r,ri.addToCache(n,r)),n}static fromBuffer(i,t,e,s){i=i||new Float32Array(t*e*4);const r=new Xe(i,{width:t,height:e}),n=i instanceof Float32Array?H.FLOAT:H.UNSIGNED_BYTE;return new ri(r,Object.assign({},gd,s||{width:t,height:e,type:n}))}static addToCache(i,t){t&&(i.textureCacheIds.includes(t)||i.textureCacheIds.push(t),Pt[t]&&Pt[t]!==i&&console.warn(`BaseTexture added to the cache with an id [${t}] that already had an entry`),Pt[t]=i)}static removeFromCache(i){if(typeof i=="string"){const t=Pt[i];if(t){const e=t.textureCacheIds.indexOf(i);return e>-1&&t.textureCacheIds.splice(e,1),delete Pt[i],t}}else if(i!=null&&i.textureCacheIds){for(let t=0;t<i.textureCacheIds.length;++t)delete Pt[i.textureCacheIds[t]];return i.textureCacheIds.length=0,i}return null}};let $=ri;$._globalBatch=0;class ar extends He{constructor(t,e){const{width:s,height:r}=e||{};super(s,r),this.items=[],this.itemDirtyIds=[];for(let n=0;n<t;n++){const o=new $;this.items.push(o),this.itemDirtyIds.push(-2)}this.length=t,this._load=null,this.baseTexture=null}initFromArray(t,e){for(let s=0;s<this.length;s++)!t[s]||(t[s].castToBaseTexture?this.addBaseTextureAt(t[s].castToBaseTexture(),s):t[s]instanceof He?this.addResourceAt(t[s],s):this.addResourceAt(or(t[s],e),s))}dispose(){for(let t=0,e=this.length;t<e;t++)this.items[t].destroy();this.items=null,this.itemDirtyIds=null,this._load=null}addResourceAt(t,e){if(!this.items[e])throw new Error(`Index ${e} is out of bounds`);return t.valid&&!this.valid&&this.resize(t.width,t.height),this.items[e].setResource(t),this}bind(t){if(this.baseTexture!==null)throw new Error("Only one base texture per TextureArray is allowed");super.bind(t);for(let e=0;e<this.length;e++)this.items[e].parentTextureArray=t,this.items[e].on("update",t.update,t)}unbind(t){super.unbind(t);for(let e=0;e<this.length;e++)this.items[e].parentTextureArray=null,this.items[e].off("update",t.update,t)}load(){if(this._load)return this._load;const e=this.items.map(s=>s.resource).filter(s=>s).map(s=>s.load());return this._load=Promise.all(e).then(()=>{const{realWidth:s,realHeight:r}=this.items[0];return this.resize(s,r),Promise.resolve(this)}),this._load}}class Po extends ar{constructor(t,e){const{width:s,height:r}=e||{};let n,o;Array.isArray(t)?(n=t,o=t.length):o=t,super(o,{width:s,height:r}),n&&this.initFromArray(n,e)}addBaseTextureAt(t,e){if(t.resource)this.addResourceAt(t.resource,e);else throw new Error("ArrayResource does not support RenderTexture");return this}bind(t){super.bind(t),t.target=ye.TEXTURE_2D_ARRAY}upload(t,e,s){const{length:r,itemDirtyIds:n,items:o}=this,{gl:a}=t;s.dirtyId<0&&a.texImage3D(a.TEXTURE_2D_ARRAY,0,s.internalFormat,this._width,this._height,r,0,e.format,s.type,null);for(let h=0;h<r;h++){const l=o[h];n[h]<l.dirtyId&&(n[h]=l.dirtyId,l.valid&&a.texSubImage3D(a.TEXTURE_2D_ARRAY,0,0,0,h,l.resource.width,l.resource.height,1,e.format,s.type,l.resource.source))}return!0}}class le extends He{constructor(t){const e=t,s=e.naturalWidth||e.videoWidth||e.width,r=e.naturalHeight||e.videoHeight||e.height;super(s,r),this.source=t,this.noSubImage=!1}static crossOrigin(t,e,s){s===void 0&&!e.startsWith("data:")?t.crossOrigin=Ro(e):s!==!1&&(t.crossOrigin=typeof s=="string"?s:"anonymous")}upload(t,e,s,r){const n=t.gl,o=e.realWidth,a=e.realHeight;if(r=r||this.source,typeof HTMLImageElement!="undefined"&&r instanceof HTMLImageElement){if(!r.complete||r.naturalWidth===0)return!1}else if(typeof HTMLVideoElement!="undefined"&&r instanceof HTMLVideoElement&&r.readyState<=1&&r.buffered.length===0)return!1;return n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.alphaMode===Nt.UNPACK),!this.noSubImage&&e.target===n.TEXTURE_2D&&s.width===o&&s.height===a?n.texSubImage2D(n.TEXTURE_2D,0,0,0,e.format,s.type,r):(s.width=o,s.height=a,n.texImage2D(e.target,0,s.internalFormat,e.format,s.type,r)),!0}update(){if(this.destroyed)return;const t=this.source,e=t.naturalWidth||t.videoWidth||t.width,s=t.naturalHeight||t.videoHeight||t.height;this.resize(e,s),super.update()}dispose(){this.source=null}}class hr extends le{constructor(t){super(t)}static test(t){const{OffscreenCanvas:e}=globalThis;return e&&t instanceof e?!0:globalThis.HTMLCanvasElement&&t instanceof HTMLCanvasElement}}const ni=class extends ar{constructor(i,t){const{width:e,height:s,autoLoad:r,linkBaseTexture:n}=t||{};if(i&&i.length!==ni.SIDES)throw new Error(`Invalid length. Got ${i.length}, expected 6`);super(6,{width:e,height:s});for(let o=0;o<ni.SIDES;o++)this.items[o].target=ye.TEXTURE_CUBE_MAP_POSITIVE_X+o;this.linkBaseTexture=n!==!1,i&&this.initFromArray(i,t),r!==!1&&this.load()}bind(i){super.bind(i),i.target=ye.TEXTURE_CUBE_MAP}addBaseTextureAt(i,t,e){if(e===void 0&&(e=this.linkBaseTexture),!this.items[t])throw new Error(`Index ${t} is out of bounds`);if(!this.linkBaseTexture||i.parentTextureArray||Object.keys(i._glTextures).length>0)if(i.resource)this.addResourceAt(i.resource,t);else throw new Error("CubeResource does not support copying of renderTexture.");else i.target=ye.TEXTURE_CUBE_MAP_POSITIVE_X+t,i.parentTextureArray=this.baseTexture,this.items[t]=i;return i.valid&&!this.valid&&this.resize(i.realWidth,i.realHeight),this.items[t]=i,this}upload(i,t,e){const s=this.itemDirtyIds;for(let r=0;r<ni.SIDES;r++){const n=this.items[r];(s[r]<n.dirtyId||e.dirtyId<t.dirtyId)&&(n.valid&&n.resource?(n.resource.upload(i,n,e),s[r]=n.dirtyId):s[r]<-1&&(i.gl.texImage2D(n.target,0,e.internalFormat,t.realWidth,t.realHeight,0,t.format,e.type,null),s[r]=-1))}return!0}static test(i){return Array.isArray(i)&&i.length===ni.SIDES}};let lr=ni;lr.SIDES=6;class cr extends le{constructor(t,e){var s;if(e=e||{},typeof t=="string"){const r=new Image;le.crossOrigin(r,t,e.crossorigin),r.src=t,t=r}super(t),!t.complete&&!!this._width&&!!this._height&&(this._width=0,this._height=0),this.url=t.src,this._process=null,this.preserveBitmap=!1,this.createBitmap=((s=e.createBitmap)!=null?s:P.CREATE_IMAGE_BITMAP)&&!!globalThis.createImageBitmap,this.alphaMode=typeof e.alphaMode=="number"?e.alphaMode:null,this.bitmap=null,this._load=null,e.autoLoad!==!1&&this.load()}load(t){return this._load?this._load:(t!==void 0&&(this.createBitmap=t),this._load=new Promise((e,s)=>{const r=this.source;this.url=r.src;const n=()=>{this.destroyed||(r.onload=null,r.onerror=null,this.resize(r.width,r.height),this._load=null,this.createBitmap?e(this.process()):e(this))};r.complete&&r.src?n():(r.onload=n,r.onerror=o=>{s(o),this.onError.emit(o)})}),this._load)}process(){const t=this.source;if(this._process!==null)return this._process;if(this.bitmap!==null||!globalThis.createImageBitmap)return Promise.resolve(this);const e=globalThis.createImageBitmap,s=!t.crossOrigin||t.crossOrigin==="anonymous";return this._process=fetch(t.src,{mode:s?"cors":"no-cors"}).then(r=>r.blob()).then(r=>e(r,0,0,t.width,t.height,{premultiplyAlpha:this.alphaMode===null||this.alphaMode===Nt.UNPACK?"premultiply":"none"})).then(r=>this.destroyed?Promise.reject():(this.bitmap=r,this.update(),this._process=null,Promise.resolve(this))),this._process}upload(t,e,s){if(typeof this.alphaMode=="number"&&(e.alphaMode=this.alphaMode),!this.createBitmap)return super.upload(t,e,s);if(!this.bitmap&&(this.process(),!this.bitmap))return!1;if(super.upload(t,e,s,this.bitmap),!this.preserveBitmap){let r=!0;const n=e._glTextures;for(const o in n){const a=n[o];if(a!==s&&a.dirtyId!==e.dirtyId){r=!1;break}}r&&(this.bitmap.close&&this.bitmap.close(),this.bitmap=null)}return!0}dispose(){this.source.onload=null,this.source.onerror=null,super.dispose(),this.bitmap&&(this.bitmap.close(),this.bitmap=null),this._process=null,this._load=null}static test(t){return typeof HTMLImageElement!="undefined"&&(typeof t=="string"||t instanceof HTMLImageElement)}}const Ji=class extends le{constructor(i,t){t=t||{},super(P.ADAPTER.createCanvas()),this._width=0,this._height=0,this.svg=i,this.scale=t.scale||1,this._overrideWidth=t.width,this._overrideHeight=t.height,this._resolve=null,this._crossorigin=t.crossorigin,this._load=null,t.autoLoad!==!1&&this.load()}load(){return this._load?this._load:(this._load=new Promise(i=>{if(this._resolve=()=>{this.resize(this.source.width,this.source.height),i(this)},Ji.SVG_XML.test(this.svg.trim())){if(!btoa)throw new Error("Your browser doesn't support base64 conversions.");this.svg=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(this.svg)))}`}this._loadSvg()}),this._load)}_loadSvg(){const i=new Image;le.crossOrigin(i,this.svg,this._crossorigin),i.src=this.svg,i.onerror=t=>{!this._resolve||(i.onerror=null,this.onError.emit(t))},i.onload=()=>{if(!this._resolve)return;const t=i.width,e=i.height;if(!t||!e)throw new Error("The SVG image must have width and height defined (in pixels), canvas API needs them.");let s=t*this.scale,r=e*this.scale;(this._overrideWidth||this._overrideHeight)&&(s=this._overrideWidth||this._overrideHeight/e*t,r=this._overrideHeight||this._overrideWidth/t*e),s=Math.round(s),r=Math.round(r);const n=this.source;n.width=s,n.height=r,n._pixiId=`canvas_${me()}`,n.getContext("2d").drawImage(i,0,0,t,e,0,0,s,r),this._resolve(),this._resolve=null}}static getSize(i){const t=Ji.SVG_SIZE.exec(i),e={};return t&&(e[t[1]]=Math.round(parseFloat(t[3])),e[t[5]]=Math.round(parseFloat(t[7]))),e}dispose(){super.dispose(),this._resolve=null,this._crossorigin=null}static test(i,t){return t==="svg"||typeof i=="string"&&i.startsWith("data:image/svg+xml")||typeof i=="string"&&Ji.SVG_XML.test(i)}};let Ve=Ji;Ve.SVG_XML=/^(<\?xml[^?]+\?>)?\s*(<!--[^(-->)]*-->)?\s*\<svg/m,Ve.SVG_SIZE=/<svg[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*>/i;const ur=class extends le{constructor(i,t){if(t=t||{},!(i instanceof HTMLVideoElement)){const e=document.createElement("video");e.setAttribute("preload","auto"),e.setAttribute("webkit-playsinline",""),e.setAttribute("playsinline",""),typeof i=="string"&&(i=[i]);const s=i[0].src||i[0];le.crossOrigin(e,s,t.crossorigin);for(let r=0;r<i.length;++r){const n=document.createElement("source");let{src:o,mime:a}=i[r];o=o||i[r];const h=o.split("?").shift().toLowerCase(),l=h.slice(h.lastIndexOf(".")+1);a=a||ur.MIME_TYPES[l]||`video/${l}`,n.src=o,n.type=a,e.appendChild(n)}i=e}super(i),this.noSubImage=!0,this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=t.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=t.autoPlay!==!1,this._load=null,this._resolve=null,this._onCanPlay=this._onCanPlay.bind(this),this._onError=this._onError.bind(this),t.autoLoad!==!1&&this.load()}update(i=0){if(!this.destroyed){const t=ot.shared.elapsedMS*this.source.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-t),(!this._updateFPS||this._msToNextUpdate<=0)&&(super.update(),this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0)}}load(){if(this._load)return this._load;const i=this.source;return(i.readyState===i.HAVE_ENOUGH_DATA||i.readyState===i.HAVE_FUTURE_DATA)&&i.width&&i.height&&(i.complete=!0),i.addEventListener("play",this._onPlayStart.bind(this)),i.addEventListener("pause",this._onPlayStop.bind(this)),this._isSourceReady()?this._onCanPlay():(i.addEventListener("canplay",this._onCanPlay),i.addEventListener("canplaythrough",this._onCanPlay),i.addEventListener("error",this._onError,!0)),this._load=new Promise(t=>{this.valid?t(this):(this._resolve=t,i.load())}),this._load}_onError(i){this.source.removeEventListener("error",this._onError,!0),this.onError.emit(i)}_isSourcePlaying(){const i=this.source;return!i.paused&&!i.ended&&this._isSourceReady()}_isSourceReady(){return this.source.readyState>2}_onPlayStart(){this.valid||this._onCanPlay(),this.autoUpdate&&!this._isConnectedToTicker&&(ot.shared.add(this.update,this),this._isConnectedToTicker=!0)}_onPlayStop(){this._isConnectedToTicker&&(ot.shared.remove(this.update,this),this._isConnectedToTicker=!1)}_onCanPlay(){const i=this.source;i.removeEventListener("canplay",this._onCanPlay),i.removeEventListener("canplaythrough",this._onCanPlay);const t=this.valid;this.resize(i.videoWidth,i.videoHeight),!t&&this._resolve&&(this._resolve(this),this._resolve=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&i.play()}dispose(){this._isConnectedToTicker&&(ot.shared.remove(this.update,this),this._isConnectedToTicker=!1);const i=this.source;i&&(i.removeEventListener("error",this._onError,!0),i.pause(),i.src="",i.load()),super.dispose()}get autoUpdate(){return this._autoUpdate}set autoUpdate(i){i!==this._autoUpdate&&(this._autoUpdate=i,!this._autoUpdate&&this._isConnectedToTicker?(ot.shared.remove(this.update,this),this._isConnectedToTicker=!1):this._autoUpdate&&!this._isConnectedToTicker&&this._isSourcePlaying()&&(ot.shared.add(this.update,this),this._isConnectedToTicker=!0))}get updateFPS(){return this._updateFPS}set updateFPS(i){i!==this._updateFPS&&(this._updateFPS=i)}static test(i,t){return globalThis.HTMLVideoElement&&i instanceof HTMLVideoElement||ur.TYPES.includes(t)}};let ts=ur;ts.TYPES=["mp4","m4v","webm","ogg","ogv","h264","avi","mov"],ts.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};class Re extends le{constructor(t,e){var r;var s=(...n)=>{super(...n)};e=e||{},typeof t=="string"?(s(Re.EMPTY),this.url=t):(s(t),this.url=null),this.crossOrigin=(r=e.crossOrigin)!=null?r:!0,this.alphaMode=typeof e.alphaMode=="number"?e.alphaMode:null,this._load=null,e.autoLoad!==!1&&this.load()}load(){return this._load?this._load:(this._load=new Promise(async(t,e)=>{if(this.url===null){t(this);return}try{const s=await P.ADAPTER.fetch(this.url,{mode:this.crossOrigin?"cors":"no-cors"});if(this.destroyed)return;const r=await s.blob();if(this.destroyed)return;const n=await createImageBitmap(r,{premultiplyAlpha:this.alphaMode===null||this.alphaMode===Nt.UNPACK?"premultiply":"none"});if(this.destroyed)return;this.source=n,this.update(),t(this)}catch(s){if(this.destroyed)return;e(s),this.onError.emit(s)}}),this._load)}upload(t,e,s){return this.source instanceof ImageBitmap?(typeof this.alphaMode=="number"&&(e.alphaMode=this.alphaMode),super.upload(t,e,s)):(this.load(),!1)}dispose(){this.source instanceof ImageBitmap&&this.source.close(),super.dispose(),this._load=null}static test(t){return!!globalThis.createImageBitmap&&typeof ImageBitmap!="undefined"&&(typeof t=="string"||t instanceof ImageBitmap)}static get EMPTY(){var t;return Re._EMPTY=(t=Re._EMPTY)!=null?t:P.ADAPTER.createCanvas(0,0),Re._EMPTY}}Qi.push(Re,cr,hr,ts,Ve,Xe,lr,Po);class _d extends Xe{upload(t,e,s){const r=t.gl;r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.alphaMode===Nt.UNPACK);const n=e.realWidth,o=e.realHeight;return s.width===n&&s.height===o?r.texSubImage2D(e.target,0,0,0,n,o,e.format,s.type,this.data):(s.width=n,s.height=o,r.texImage2D(e.target,0,s.internalFormat,n,o,0,e.format,s.type,this.data)),!0}}class es{constructor(t,e){this.width=Math.round(t||100),this.height=Math.round(e||100),this.stencil=!1,this.depth=!1,this.dirtyId=0,this.dirtyFormat=0,this.dirtySize=0,this.depthTexture=null,this.colorTextures=[],this.glFramebuffers={},this.disposeRunner=new It("disposeFramebuffer"),this.multisample=ft.NONE}get colorTexture(){return this.colorTextures[0]}addColorTexture(t=0,e){return this.colorTextures[t]=e||new $(null,{scaleMode:Dt.NEAREST,resolution:1,mipmap:Gt.OFF,width:this.width,height:this.height}),this.dirtyId++,this.dirtyFormat++,this}addDepthTexture(t){return this.depthTexture=t||new $(new _d(null,{width:this.width,height:this.height}),{scaleMode:Dt.NEAREST,resolution:1,width:this.width,height:this.height,mipmap:Gt.OFF,format:N.DEPTH_COMPONENT,type:H.UNSIGNED_SHORT}),this.dirtyId++,this.dirtyFormat++,this}enableDepth(){return this.depth=!0,this.dirtyId++,this.dirtyFormat++,this}enableStencil(){return this.stencil=!0,this.dirtyId++,this.dirtyFormat++,this}resize(t,e){if(t=Math.round(t),e=Math.round(e),!(t===this.width&&e===this.height)){this.width=t,this.height=e,this.dirtyId++,this.dirtySize++;for(let s=0;s<this.colorTextures.length;s++){const r=this.colorTextures[s],n=r.resolution;r.setSize(t/n,e/n)}if(this.depthTexture){const s=this.depthTexture.resolution;this.depthTexture.setSize(t/s,e/s)}}}dispose(){this.disposeRunner.emit(this,!1)}destroyDepthTexture(){this.depthTexture&&(this.depthTexture.destroy(),this.depthTexture=null,++this.dirtyId,++this.dirtyFormat)}}class is extends ${constructor(t={}){var e;if(typeof t=="number"){const s=arguments[0],r=arguments[1],n=arguments[2],o=arguments[3];t={width:s,height:r,scaleMode:n,resolution:o}}t.width=t.width||100,t.height=t.height||100,(e=t.multisample)!=null||(t.multisample=ft.NONE),super(null,t),this.mipmap=Gt.OFF,this.valid=!0,this.clearColor=[0,0,0,0],this.framebuffer=new es(this.realWidth,this.realHeight).addColorTexture(0,this),this.framebuffer.multisample=t.multisample,this.maskStack=[],this.filterStack=[{}]}resize(t,e){this.framebuffer.resize(t*this.resolution,e*this.resolution),this.setRealSize(this.framebuffer.width,this.framebuffer.height)}dispose(){this.framebuffer.dispose(),super.dispose()}destroy(){super.destroy(),this.framebuffer.destroyDepthTexture(),this.framebuffer=null}}class dr{constructor(){this.x0=0,this.y0=0,this.x1=1,this.y1=0,this.x2=1,this.y2=1,this.x3=0,this.y3=1,this.uvsFloat32=new Float32Array(8)}set(t,e,s){const r=e.width,n=e.height;if(s){const o=t.width/2/r,a=t.height/2/n,h=t.x/r+o,l=t.y/n+a;s=nt.add(s,nt.NW),this.x0=h+o*nt.uX(s),this.y0=l+a*nt.uY(s),s=nt.add(s,2),this.x1=h+o*nt.uX(s),this.y1=l+a*nt.uY(s),s=nt.add(s,2),this.x2=h+o*nt.uX(s),this.y2=l+a*nt.uY(s),s=nt.add(s,2),this.x3=h+o*nt.uX(s),this.y3=l+a*nt.uY(s)}else this.x0=t.x/r,this.y0=t.y/n,this.x1=(t.x+t.width)/r,this.y1=t.y/n,this.x2=(t.x+t.width)/r,this.y2=(t.y+t.height)/n,this.x3=t.x/r,this.y3=(t.y+t.height)/n;this.uvsFloat32[0]=this.x0,this.uvsFloat32[1]=this.y0,this.uvsFloat32[2]=this.x1,this.uvsFloat32[3]=this.y1,this.uvsFloat32[4]=this.x2,this.uvsFloat32[5]=this.y2,this.uvsFloat32[6]=this.x3,this.uvsFloat32[7]=this.y3}toString(){return`[@pixi/core:TextureUvs x0=${this.x0} y0=${this.y0} x1=${this.x1} y1=${this.y1} x2=${this.x2} y2=${this.y2} x3=${this.x3} y3=${this.y3}]`}}const Mo=new dr;function ss(i){i.destroy=function(){},i.on=function(){},i.once=function(){},i.emit=function(){}}class L extends Le{constructor(t,e,s,r,n,o){if(super(),this.noFrame=!1,e||(this.noFrame=!0,e=new j(0,0,1,1)),t instanceof L&&(t=t.baseTexture),this.baseTexture=t,this._frame=e,this.trim=r,this.valid=!1,this._uvs=Mo,this.uvMatrix=null,this.orig=s||e,this._rotate=Number(n||0),n===!0)this._rotate=2;else if(this._rotate%2!==0)throw new Error("attempt to use diamond-shaped UVs. If you are sure, set rotation manually");this.defaultAnchor=o?new W(o.x,o.y):new W(0,0),this._updateID=0,this.textureCacheIds=[],t.valid?this.noFrame?t.valid&&this.onBaseTextureUpdated(t):this.frame=e:t.once("loaded",this.onBaseTextureUpdated,this),this.noFrame&&t.on("update",this.onBaseTextureUpdated,this)}update(){this.baseTexture.resource&&this.baseTexture.resource.update()}onBaseTextureUpdated(t){if(this.noFrame){if(!this.baseTexture.valid)return;this._frame.width=t.width,this._frame.height=t.height,this.valid=!0,this.updateUvs()}else this.frame=this._frame;this.emit("update",this)}destroy(t){if(this.baseTexture){if(t){const{resource:e}=this.baseTexture;(e==null?void 0:e.url)&&Et[e.url]&&L.removeFromCache(e.url),this.baseTexture.destroy()}this.baseTexture.off("loaded",this.onBaseTextureUpdated,this),this.baseTexture.off("update",this.onBaseTextureUpdated,this),this.baseTexture=null}this._frame=null,this._uvs=null,this.trim=null,this.orig=null,this.valid=!1,L.removeFromCache(this),this.textureCacheIds=null}clone(){var r;const t=this._frame.clone(),e=this._frame===this.orig?t:this.orig.clone(),s=new L(this.baseTexture,!this.noFrame&&t,e,(r=this.trim)==null?void 0:r.clone(),this.rotate,this.defaultAnchor);return this.noFrame&&(s._frame=t),s}updateUvs(){this._uvs===Mo&&(this._uvs=new dr),this._uvs.set(this._frame,this.baseTexture,this.rotate),this._updateID++}static from(t,e={},s=P.STRICT_TEXTURE_CACHE){const r=typeof t=="string";let n=null;if(r)n=t;else if(t instanceof $){if(!t.cacheId){const a=(e==null?void 0:e.pixiIdPrefix)||"pixiid";t.cacheId=`${a}-${me()}`,$.addToCache(t,t.cacheId)}n=t.cacheId}else{if(!t._pixiId){const a=(e==null?void 0:e.pixiIdPrefix)||"pixiid";t._pixiId=`${a}_${me()}`}n=t._pixiId}let o=Et[n];if(r&&s&&!o)throw new Error(`The cacheId "${n}" does not exist in TextureCache.`);return!o&&!(t instanceof $)?(e.resolution||(e.resolution=he(t)),o=new L(new $(t,e)),o.baseTexture.cacheId=n,$.addToCache(o.baseTexture,n),L.addToCache(o,n)):!o&&t instanceof $&&(o=new L(t),L.addToCache(o,n)),o}static fromURL(t,e){const s=Object.assign({autoLoad:!1},e==null?void 0:e.resourceOptions),r=L.from(t,Object.assign({resourceOptions:s},e),!1),n=r.baseTexture.resource;return r.baseTexture.valid?Promise.resolve(r):n.load().then(()=>Promise.resolve(r))}static fromBuffer(t,e,s,r){return new L($.fromBuffer(t,e,s,r))}static fromLoader(t,e,s,r){const n=new $(t,Object.assign({scaleMode:P.SCALE_MODE,resolution:he(e)},r)),{resource:o}=n;o instanceof cr&&(o.url=e);const a=new L(n);return s||(s=e),$.addToCache(a.baseTexture,s),L.addToCache(a,s),s!==e&&($.addToCache(a.baseTexture,e),L.addToCache(a,e)),a.baseTexture.valid?Promise.resolve(a):new Promise(h=>{a.baseTexture.once("loaded",()=>h(a))})}static addToCache(t,e){e&&(t.textureCacheIds.includes(e)||t.textureCacheIds.push(e),Et[e]&&Et[e]!==t&&console.warn(`Texture added to the cache with an id [${e}] that already had an entry`),Et[e]=t)}static removeFromCache(t){if(typeof t=="string"){const e=Et[t];if(e){const s=e.textureCacheIds.indexOf(t);return s>-1&&e.textureCacheIds.splice(s,1),delete Et[t],e}}else if(t!=null&&t.textureCacheIds){for(let e=0;e<t.textureCacheIds.length;++e)Et[t.textureCacheIds[e]]===t&&delete Et[t.textureCacheIds[e]];return t.textureCacheIds.length=0,t}return null}get resolution(){return this.baseTexture.resolution}get frame(){return this._frame}set frame(t){this._frame=t,this.noFrame=!1;const{x:e,y:s,width:r,height:n}=t,o=e+r>this.baseTexture.width,a=s+n>this.baseTexture.height;if(o||a){const h=o&&a?"and":"or",l=`X: ${e} + ${r} = ${e+r} > ${this.baseTexture.width}`,c=`Y: ${s} + ${n} = ${s+n} > ${this.baseTexture.height}`;throw new Error(`Texture Error: frame does not fit inside the base Texture dimensions: ${l} ${h} ${c}`)}this.valid=r&&n&&this.baseTexture.valid,!this.trim&&!this.rotate&&(this.orig=t),this.valid&&this.updateUvs()}get rotate(){return this._rotate}set rotate(t){this._rotate=t,this.valid&&this.updateUvs()}get width(){return this.orig.width}get height(){return this.orig.height}castToBaseTexture(){return this.baseTexture}static get EMPTY(){return L._EMPTY||(L._EMPTY=new L(new $),ss(L._EMPTY),ss(L._EMPTY.baseTexture)),L._EMPTY}static get WHITE(){if(!L._WHITE){const t=P.ADAPTER.createCanvas(16,16),e=t.getContext("2d");t.width=16,t.height=16,e.fillStyle="white",e.fillRect(0,0,16,16),L._WHITE=new L($.from(t)),ss(L._WHITE),ss(L._WHITE.baseTexture)}return L._WHITE}}class Ot extends L{constructor(t,e){super(t,e),this.valid=!0,this.filterFrame=null,this.filterPoolKey=null,this.updateUvs()}get framebuffer(){return this.baseTexture.framebuffer}get multisample(){return this.framebuffer.multisample}set multisample(t){this.framebuffer.multisample=t}resize(t,e,s=!0){const r=this.baseTexture.resolution,n=Math.round(t*r)/r,o=Math.round(e*r)/r;this.valid=n>0&&o>0,this._frame.width=this.orig.width=n,this._frame.height=this.orig.height=o,s&&this.baseTexture.resize(n,o),this.updateUvs()}setResolution(t){const{baseTexture:e}=this;e.resolution!==t&&(e.setResolution(t),this.resize(e.width,e.height,!1))}static create(t){return new Ot(new is(t))}}class fr{constructor(t){this.texturePool={},this.textureOptions=t||{},this.enableFullScreen=!1,this._pixelsWidth=0,this._pixelsHeight=0}createTexture(t,e,s=ft.NONE){const r=new is(Object.assign({width:t,height:e,resolution:1,multisample:s},this.textureOptions));return new Ot(r)}getOptimalTexture(t,e,s=1,r=ft.NONE){let n;t=Math.ceil(t*s-1e-6),e=Math.ceil(e*s-1e-6),!this.enableFullScreen||t!==this._pixelsWidth||e!==this._pixelsHeight?(t=si(t),e=si(e),n=((t&65535)<<16|e&65535)>>>0,r>1&&(n+=r*4294967296)):n=r>1?-r:-1,this.texturePool[n]||(this.texturePool[n]=[]);let o=this.texturePool[n].pop();return o||(o=this.createTexture(t,e,r)),o.filterPoolKey=n,o.setResolution(s),o}getFilterTexture(t,e,s){const r=this.getOptimalTexture(t.width,t.height,e||t.resolution,s||ft.NONE);return r.filterFrame=t.filterFrame,r}returnTexture(t){const e=t.filterPoolKey;t.filterFrame=null,this.texturePool[e].push(t)}returnFilterTexture(t){this.returnTexture(t)}clear(t){if(t=t!==!1,t)for(const e in this.texturePool){const s=this.texturePool[e];if(s)for(let r=0;r<s.length;r++)s[r].destroy(!0)}this.texturePool={}}setScreenSize(t){if(!(t.width===this._pixelsWidth&&t.height===this._pixelsHeight)){this.enableFullScreen=t.width>0&&t.height>0;for(const e in this.texturePool){if(!(Number(e)<0))continue;const s=this.texturePool[e];if(s)for(let r=0;r<s.length;r++)s[r].destroy(!0);this.texturePool[e]=[]}this._pixelsWidth=t.width,this._pixelsHeight=t.height}}}fr.SCREEN_KEY=-1;class oi{constructor(t,e=0,s=!1,r=H.FLOAT,n,o,a){this.buffer=t,this.size=e,this.normalized=s,this.type=r,this.stride=n,this.start=o,this.instance=a}destroy(){this.buffer=null}static from(t,e,s,r,n){return new oi(t,e,s,r,n)}}let vd=0;class dt{constructor(t,e=!0,s=!1){this.data=t||new Float32Array(1),this._glBuffers={},this._updateID=0,this.index=s,this.static=e,this.id=vd++,this.disposeRunner=new It("disposeBuffer")}update(t){t instanceof Array&&(t=new Float32Array(t)),this.data=t||this.data,this._updateID++}dispose(){this.disposeRunner.emit(this,!1)}destroy(){this.dispose(),this.data=null}set index(t){this.type=t?Xt.ELEMENT_ARRAY_BUFFER:Xt.ARRAY_BUFFER}get index(){return this.type===Xt.ELEMENT_ARRAY_BUFFER}static from(t){return t instanceof Array&&(t=new Float32Array(t)),new dt(t)}}const yd={Float32Array,Uint32Array,Int32Array,Uint8Array};function xd(i,t){let e=0,s=0;const r={};for(let h=0;h<i.length;h++)s+=t[h],e+=i[h].length;const n=new ArrayBuffer(e*4);let o=null,a=0;for(let h=0;h<i.length;h++){const l=t[h],c=i[h],u=Ki(c);r[u]||(r[u]=new yd[u](n)),o=r[u];for(let d=0;d<c.length;d++){const f=(d/l|0)*s+a,p=d%l;o[f+p]=c[d]}a+=l}return new Float32Array(n)}const Bo={5126:4,5123:2,5121:1};let Td=0;const bd={Float32Array,Uint32Array,Int32Array,Uint8Array,Uint16Array};class ce{constructor(t=[],e={}){this.buffers=t,this.indexBuffer=null,this.attributes=e,this.glVertexArrayObjects={},this.id=Td++,this.instanced=!1,this.instanceCount=1,this.disposeRunner=new It("disposeGeometry"),this.refCount=0}addAttribute(t,e,s=0,r=!1,n,o,a,h=!1){if(!e)throw new Error("You must pass a buffer when creating an attribute");e instanceof dt||(e instanceof Array&&(e=new Float32Array(e)),e=new dt(e));const l=t.split("|");if(l.length>1){for(let u=0;u<l.length;u++)this.addAttribute(l[u],e,s,r,n);return this}let c=this.buffers.indexOf(e);return c===-1&&(this.buffers.push(e),c=this.buffers.length-1),this.attributes[t]=new oi(c,s,r,n,o,a,h),this.instanced=this.instanced||h,this}getAttribute(t){return this.attributes[t]}getBuffer(t){return this.buffers[this.getAttribute(t).buffer]}addIndex(t){return t instanceof dt||(t instanceof Array&&(t=new Uint16Array(t)),t=new dt(t)),t.type=Xt.ELEMENT_ARRAY_BUFFER,this.indexBuffer=t,this.buffers.includes(t)||this.buffers.push(t),this}getIndex(){return this.indexBuffer}interleave(){if(this.buffers.length===1||this.buffers.length===2&&this.indexBuffer)return this;const t=[],e=[],s=new dt;let r;for(r in this.attributes){const n=this.attributes[r],o=this.buffers[n.buffer];t.push(o.data),e.push(n.size*Bo[n.type]/4),n.buffer=0}for(s.data=xd(t,e),r=0;r<this.buffers.length;r++)this.buffers[r]!==this.indexBuffer&&this.buffers[r].destroy();return this.buffers=[s],this.indexBuffer&&this.buffers.push(this.indexBuffer),this}getSize(){for(const t in this.attributes){const e=this.attributes[t];return this.buffers[e.buffer].data.length/(e.stride/4||e.size)}return 0}dispose(){this.disposeRunner.emit(this,!1)}destroy(){this.dispose(),this.buffers=null,this.indexBuffer=null,this.attributes=null}clone(){const t=new ce;for(let e=0;e<this.buffers.length;e++)t.buffers[e]=new dt(this.buffers[e].data.slice(0));for(const e in this.attributes){const s=this.attributes[e];t.attributes[e]=new oi(s.buffer,s.size,s.normalized,s.type,s.stride,s.start,s.instance)}return this.indexBuffer&&(t.indexBuffer=t.buffers[this.buffers.indexOf(this.indexBuffer)],t.indexBuffer.type=Xt.ELEMENT_ARRAY_BUFFER),t}static merge(t){const e=new ce,s=[],r=[],n=[];let o;for(let a=0;a<t.length;a++){o=t[a];for(let h=0;h<o.buffers.length;h++)r[h]=r[h]||0,r[h]+=o.buffers[h].data.length,n[h]=0}for(let a=0;a<o.buffers.length;a++)s[a]=new bd[Ki(o.buffers[a].data)](r[a]),e.buffers[a]=new dt(s[a]);for(let a=0;a<t.length;a++){o=t[a];for(let h=0;h<o.buffers.length;h++)s[h].set(o.buffers[h].data,n[h]),n[h]+=o.buffers[h].data.length}if(e.attributes=o.attributes,o.indexBuffer){e.indexBuffer=e.buffers[o.buffers.indexOf(o.indexBuffer)],e.indexBuffer.type=Xt.ELEMENT_ARRAY_BUFFER;let a=0,h=0,l=0,c=0;for(let u=0;u<o.buffers.length;u++)if(o.buffers[u]!==o.indexBuffer){c=u;break}for(const u in o.attributes){const d=o.attributes[u];(d.buffer|0)===c&&(h+=d.size*Bo[d.type]/4)}for(let u=0;u<t.length;u++){const d=t[u].indexBuffer.data;for(let f=0;f<d.length;f++)e.indexBuffer.data[f+l]+=a;a+=t[u].buffers[c].data.length/h,l+=d.length}}return e}}class Do extends ce{constructor(){super(),this.addAttribute("aVertexPosition",new Float32Array([0,0,1,0,1,1,0,1])).addIndex([0,1,3,2])}}class pr extends ce{constructor(){super(),this.vertices=new Float32Array([-1,-1,1,-1,1,1,-1,1]),this.uvs=new Float32Array([0,0,1,0,1,1,0,1]),this.vertexBuffer=new dt(this.vertices),this.uvBuffer=new dt(this.uvs),this.addAttribute("aVertexPosition",this.vertexBuffer).addAttribute("aTextureCoord",this.uvBuffer).addIndex([0,1,2,0,2,3])}map(t,e){let s=0,r=0;return this.uvs[0]=s,this.uvs[1]=r,this.uvs[2]=s+e.width/t.width,this.uvs[3]=r,this.uvs[4]=s+e.width/t.width,this.uvs[5]=r+e.height/t.height,this.uvs[6]=s,this.uvs[7]=r+e.height/t.height,s=e.x,r=e.y,this.vertices[0]=s,this.vertices[1]=r,this.vertices[2]=s+e.width,this.vertices[3]=r,this.vertices[4]=s+e.width,this.vertices[5]=r+e.height,this.vertices[6]=s,this.vertices[7]=r+e.height,this.invalidate(),this}invalidate(){return this.vertexBuffer._updateID++,this.uvBuffer._updateID++,this}}let Ed=0;class Lt{constructor(t,e,s){this.group=!0,this.syncUniforms={},this.dirtyId=0,this.id=Ed++,this.static=!!e,this.ubo=!!s,t instanceof dt?(this.buffer=t,this.buffer.type=Xt.UNIFORM_BUFFER,this.autoManage=!1,this.ubo=!0):(this.uniforms=t,this.ubo&&(this.buffer=new dt(new Float32Array(1)),this.buffer.type=Xt.UNIFORM_BUFFER,this.autoManage=!0))}update(){this.dirtyId++,!this.autoManage&&this.buffer&&this.buffer.update()}add(t,e,s){if(!this.ubo)this.uniforms[t]=new Lt(e,s);else throw new Error("[UniformGroup] uniform groups in ubo mode cannot be modified, or have uniform groups nested in them")}static from(t,e,s){return new Lt(t,e,s)}static uboFrom(t,e){return new Lt(t,e!=null?e:!0,!0)}}class No{constructor(){this.renderTexture=null,this.target=null,this.legacy=!1,this.resolution=1,this.multisample=ft.NONE,this.sourceFrame=new j,this.destinationFrame=new j,this.bindingSourceFrame=new j,this.bindingDestinationFrame=new j,this.filters=[],this.transform=null}clear(){this.target=null,this.filters=null,this.renderTexture=null}}const rs=[new W,new W,new W,new W],mr=new Z;class gr{constructor(t){this.renderer=t,this.defaultFilterStack=[{}],this.texturePool=new fr,this.statePool=[],this.quad=new Do,this.quadUv=new pr,this.tempRect=new j,this.activeState={},this.globalUniforms=new Lt({outputFrame:new j,inputSize:new Float32Array(4),inputPixel:new Float32Array(4),inputClamp:new Float32Array(4),resolution:1,filterArea:new Float32Array(4),filterClamp:new Float32Array(4)},!0),this.forceClear=!1,this.useMaxPadding=!1}init(){this.texturePool.setScreenSize(this.renderer.view)}push(t,e){var p,g;const s=this.renderer,r=this.defaultFilterStack,n=this.statePool.pop()||new No,o=this.renderer.renderTexture;let a=e[0].resolution,h=e[0].multisample,l=e[0].padding,c=e[0].autoFit,u=(p=e[0].legacy)!=null?p:!0;for(let m=1;m<e.length;m++){const x=e[m];a=Math.min(a,x.resolution),h=Math.min(h,x.multisample),l=this.useMaxPadding?Math.max(l,x.padding):l+x.padding,c=c&&x.autoFit,u=u||((g=x.legacy)!=null?g:!0)}r.length===1&&(this.defaultFilterStack[0].renderTexture=o.current),r.push(n),n.resolution=a,n.multisample=h,n.legacy=u,n.target=t,n.sourceFrame.copyFrom(t.filterArea||t.getBounds(!0)),n.sourceFrame.pad(l);const d=this.tempRect.copyFrom(o.sourceFrame);s.projection.transform&&this.transformAABB(mr.copyFrom(s.projection.transform).invert(),d),c?(n.sourceFrame.fit(d),(n.sourceFrame.width<=0||n.sourceFrame.height<=0)&&(n.sourceFrame.width=0,n.sourceFrame.height=0)):n.sourceFrame.intersects(d)||(n.sourceFrame.width=0,n.sourceFrame.height=0),this.roundFrame(n.sourceFrame,o.current?o.current.resolution:s.resolution,o.sourceFrame,o.destinationFrame,s.projection.transform),n.renderTexture=this.getOptimalFilterTexture(n.sourceFrame.width,n.sourceFrame.height,a,h),n.filters=e,n.destinationFrame.width=n.renderTexture.width,n.destinationFrame.height=n.renderTexture.height;const f=this.tempRect;f.x=0,f.y=0,f.width=n.sourceFrame.width,f.height=n.sourceFrame.height,n.renderTexture.filterFrame=n.sourceFrame,n.bindingSourceFrame.copyFrom(o.sourceFrame),n.bindingDestinationFrame.copyFrom(o.destinationFrame),n.transform=s.projection.transform,s.projection.transform=null,o.bind(n.renderTexture,n.sourceFrame,f),s.framebuffer.clear(0,0,0,0)}pop(){const t=this.defaultFilterStack,e=t.pop(),s=e.filters;this.activeState=e;const r=this.globalUniforms.uniforms;r.outputFrame=e.sourceFrame,r.resolution=e.resolution;const n=r.inputSize,o=r.inputPixel,a=r.inputClamp;if(n[0]=e.destinationFrame.width,n[1]=e.destinationFrame.height,n[2]=1/n[0],n[3]=1/n[1],o[0]=Math.round(n[0]*e.resolution),o[1]=Math.round(n[1]*e.resolution),o[2]=1/o[0],o[3]=1/o[1],a[0]=.5*o[2],a[1]=.5*o[3],a[2]=e.sourceFrame.width*n[2]-.5*o[2],a[3]=e.sourceFrame.height*n[3]-.5*o[3],e.legacy){const l=r.filterArea;l[0]=e.destinationFrame.width,l[1]=e.destinationFrame.height,l[2]=e.sourceFrame.x,l[3]=e.sourceFrame.y,r.filterClamp=r.inputClamp}this.globalUniforms.update();const h=t[t.length-1];if(this.renderer.framebuffer.blit(),s.length===1)s[0].apply(this,e.renderTexture,h.renderTexture,Ht.BLEND,e),this.returnFilterTexture(e.renderTexture);else{let l=e.renderTexture,c=this.getOptimalFilterTexture(l.width,l.height,e.resolution);c.filterFrame=l.filterFrame;let u=0;for(u=0;u<s.length-1;++u){u===1&&e.multisample>1&&(c=this.getOptimalFilterTexture(l.width,l.height,e.resolution),c.filterFrame=l.filterFrame),s[u].apply(this,l,c,Ht.CLEAR,e);const d=l;l=c,c=d}s[u].apply(this,l,h.renderTexture,Ht.BLEND,e),u>1&&e.multisample>1&&this.returnFilterTexture(e.renderTexture),this.returnFilterTexture(l),this.returnFilterTexture(c)}e.clear(),this.statePool.push(e)}bindAndClear(t,e=Ht.CLEAR){const{renderTexture:s,state:r}=this.renderer;if(t===this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?this.renderer.projection.transform=this.activeState.transform:this.renderer.projection.transform=null,t!=null&&t.filterFrame){const o=this.tempRect;o.x=0,o.y=0,o.width=t.filterFrame.width,o.height=t.filterFrame.height,s.bind(t,t.filterFrame,o)}else t!==this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?s.bind(t):this.renderer.renderTexture.bind(t,this.activeState.bindingSourceFrame,this.activeState.bindingDestinationFrame);const n=r.stateId&1||this.forceClear;(e===Ht.CLEAR||e===Ht.BLIT&&n)&&this.renderer.framebuffer.clear(0,0,0,0)}applyFilter(t,e,s,r){const n=this.renderer;n.state.set(t.state),this.bindAndClear(s,r),t.uniforms.uSampler=e,t.uniforms.filterGlobals=this.globalUniforms,n.shader.bind(t),t.legacy=!!t.program.attributeData.aTextureCoord,t.legacy?(this.quadUv.map(e._frame,e.filterFrame),n.geometry.bind(this.quadUv),n.geometry.draw(Bt.TRIANGLES)):(n.geometry.bind(this.quad),n.geometry.draw(Bt.TRIANGLE_STRIP))}calculateSpriteMatrix(t,e){const{sourceFrame:s,destinationFrame:r}=this.activeState,{orig:n}=e._texture,o=t.set(r.width,0,0,r.height,s.x,s.y),a=e.worldTransform.copyTo(Z.TEMP_MATRIX);return a.invert(),o.prepend(a),o.scale(1/n.width,1/n.height),o.translate(e.anchor.x,e.anchor.y),o}destroy(){this.renderer=null,this.texturePool.clear(!1)}getOptimalFilterTexture(t,e,s=1,r=ft.NONE){return this.texturePool.getOptimalTexture(t,e,s,r)}getFilterTexture(t,e,s){if(typeof t=="number"){const n=t;t=e,e=n}t=t||this.activeState.renderTexture;const r=this.texturePool.getOptimalTexture(t.width,t.height,e||t.resolution,s||ft.NONE);return r.filterFrame=t.filterFrame,r}returnFilterTexture(t){this.texturePool.returnTexture(t)}emptyPool(){this.texturePool.clear(!0)}resize(){this.texturePool.setScreenSize(this.renderer.view)}transformAABB(t,e){const s=rs[0],r=rs[1],n=rs[2],o=rs[3];s.set(e.left,e.top),r.set(e.left,e.bottom),n.set(e.right,e.top),o.set(e.right,e.bottom),t.apply(s,s),t.apply(r,r),t.apply(n,n),t.apply(o,o);const a=Math.min(s.x,r.x,n.x,o.x),h=Math.min(s.y,r.y,n.y,o.y),l=Math.max(s.x,r.x,n.x,o.x),c=Math.max(s.y,r.y,n.y,o.y);e.x=a,e.y=h,e.width=l-a,e.height=c-h}roundFrame(t,e,s,r,n){if(!(t.width<=0||t.height<=0||s.width<=0||s.height<=0)){if(n){const{a:o,b:a,c:h,d:l}=n;if((Math.abs(a)>1e-4||Math.abs(h)>1e-4)&&(Math.abs(o)>1e-4||Math.abs(l)>1e-4))return}n=n?mr.copyFrom(n):mr.identity(),n.translate(-s.x,-s.y).scale(r.width/s.width,r.height/s.height).translate(r.x,r.y),this.transformAABB(n,t),t.ceil(e),this.transformAABB(n.invert(),t)}}}gr.extension={type:F.RendererSystem,name:"filter"},U.add(gr);class ai{constructor(t){this.renderer=t}flush(){}destroy(){this.renderer=null}start(){}stop(){this.flush()}render(t){}}class _r{constructor(t){this.renderer=t,this.emptyRenderer=new ai(t),this.currentRenderer=this.emptyRenderer}setObjectRenderer(t){this.currentRenderer!==t&&(this.currentRenderer.stop(),this.currentRenderer=t,this.currentRenderer.start())}flush(){this.setObjectRenderer(this.emptyRenderer)}reset(){this.setObjectRenderer(this.emptyRenderer)}copyBoundTextures(t,e){const{boundTextures:s}=this.renderer.texture;for(let r=e-1;r>=0;--r)t[r]=s[r]||null,t[r]&&(t[r]._batchLocation=r)}boundArray(t,e,s,r){const{elements:n,ids:o,count:a}=t;let h=0;for(let l=0;l<a;l++){const c=n[l],u=c._batchLocation;if(u>=0&&u<r&&e[u]===c){o[l]=u;continue}for(;h<r;){const d=e[h];if(d&&d._batchEnabled===s&&d._batchLocation===h){h++;continue}o[l]=h,c._batchLocation=h,e[h]=c;break}}}destroy(){this.renderer=null}}_r.extension={type:F.RendererSystem,name:"batch"},U.add(_r);let Fo=0;class vr{constructor(t){this.renderer=t,this.webGLVersion=1,this.extensions={},this.supports={uint32Indices:!1},this.handleContextLost=this.handleContextLost.bind(this),this.handleContextRestored=this.handleContextRestored.bind(this)}get isLost(){return!this.gl||this.gl.isContextLost()}contextChange(t){this.gl=t,this.renderer.gl=t,this.renderer.CONTEXT_UID=Fo++}init(t){if(t.context)this.initFromContext(t.context);else{const e=this.renderer.background.alpha<1,s=t.premultipliedAlpha;this.preserveDrawingBuffer=t.preserveDrawingBuffer,this.useContextAlpha=t.useContextAlpha,this.powerPreference=t.powerPreference,this.initFromOptions({alpha:e,premultipliedAlpha:s,antialias:t.antialias,stencil:!0,preserveDrawingBuffer:t.preserveDrawingBuffer,powerPreference:t.powerPreference})}}initFromContext(t){this.gl=t,this.validateContext(t),this.renderer.gl=t,this.renderer.CONTEXT_UID=Fo++,this.renderer.runners.contextChange.emit(t);const e=this.renderer.view;e.addEventListener!==void 0&&(e.addEventListener("webglcontextlost",this.handleContextLost,!1),e.addEventListener("webglcontextrestored",this.handleContextRestored,!1))}initFromOptions(t){const e=this.createContext(this.renderer.view,t);this.initFromContext(e)}createContext(t,e){let s;if(P.PREFER_ENV>=lt.WEBGL2&&(s=t.getContext("webgl2",e)),s)this.webGLVersion=2;else if(this.webGLVersion=1,s=t.getContext("webgl",e)||t.getContext("experimental-webgl",e),!s)throw new Error("This browser does not support WebGL. Try using the canvas renderer");return this.gl=s,this.getExtensions(),this.gl}getExtensions(){const{gl:t}=this,e={loseContext:t.getExtension("WEBGL_lose_context"),anisotropicFiltering:t.getExtension("EXT_texture_filter_anisotropic"),floatTextureLinear:t.getExtension("OES_texture_float_linear"),s3tc:t.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:t.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:t.getExtension("WEBGL_compressed_texture_etc"),etc1:t.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:t.getExtension("WEBGL_compressed_texture_pvrtc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:t.getExtension("WEBGL_compressed_texture_atc"),astc:t.getExtension("WEBGL_compressed_texture_astc")};this.webGLVersion===1?Object.assign(this.extensions,e,{drawBuffers:t.getExtension("WEBGL_draw_buffers"),depthTexture:t.getExtension("WEBGL_depth_texture"),vertexArrayObject:t.getExtension("OES_vertex_array_object")||t.getExtension("MOZ_OES_vertex_array_object")||t.getExtension("WEBKIT_OES_vertex_array_object"),uint32ElementIndex:t.getExtension("OES_element_index_uint"),floatTexture:t.getExtension("OES_texture_float"),floatTextureLinear:t.getExtension("OES_texture_float_linear"),textureHalfFloat:t.getExtension("OES_texture_half_float"),textureHalfFloatLinear:t.getExtension("OES_texture_half_float_linear")}):this.webGLVersion===2&&Object.assign(this.extensions,e,{colorBufferFloat:t.getExtension("EXT_color_buffer_float")})}handleContextLost(t){t.preventDefault(),setTimeout(()=>{this.gl.isContextLost()&&this.extensions.loseContext&&this.extensions.loseContext.restoreContext()},0)}handleContextRestored(){this.renderer.runners.contextChange.emit(this.gl)}destroy(){const t=this.renderer.view;this.renderer=null,t.removeEventListener!==void 0&&(t.removeEventListener("webglcontextlost",this.handleContextLost),t.removeEventListener("webglcontextrestored",this.handleContextRestored)),this.gl.useProgram(null),this.extensions.loseContext&&this.extensions.loseContext.loseContext()}postrender(){this.renderer.objectRenderer.renderingToScreen&&this.gl.flush()}validateContext(t){const e=t.getContextAttributes(),s="WebGL2RenderingContext"in globalThis&&t instanceof globalThis.WebGL2RenderingContext;s&&(this.webGLVersion=2),e&&!e.stencil&&console.warn("Provided WebGL context does not have a stencil buffer, masks may not render correctly");const r=s||!!t.getExtension("OES_element_index_uint");this.supports.uint32Indices=r,r||console.warn("Provided WebGL context does not support 32 index buffer, complex graphics may not render correctly")}}vr.extension={type:F.RendererSystem,name:"context"},U.add(vr);class Oo{constructor(t){this.framebuffer=t,this.stencil=null,this.dirtyId=-1,this.dirtyFormat=-1,this.dirtySize=-1,this.multisample=ft.NONE,this.msaaBuffer=null,this.blitFramebuffer=null,this.mipLevel=0}}const wd=new j;class yr{constructor(t){this.renderer=t,this.managedFramebuffers=[],this.unknownFramebuffer=new es(10,10),this.msaaSamples=null}contextChange(){this.disposeAll(!0);const t=this.gl=this.renderer.gl;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.current=this.unknownFramebuffer,this.viewport=new j,this.hasMRT=!0,this.writeDepthTexture=!0,this.renderer.context.webGLVersion===1){let e=this.renderer.context.extensions.drawBuffers,s=this.renderer.context.extensions.depthTexture;P.PREFER_ENV===lt.WEBGL_LEGACY&&(e=null,s=null),e?t.drawBuffers=r=>e.drawBuffersWEBGL(r):(this.hasMRT=!1,t.drawBuffers=()=>{}),s||(this.writeDepthTexture=!1)}else this.msaaSamples=t.getInternalformatParameter(t.RENDERBUFFER,t.RGBA8,t.SAMPLES)}bind(t,e,s=0){const{gl:r}=this;if(t){const n=t.glFramebuffers[this.CONTEXT_UID]||this.initFramebuffer(t);this.current!==t&&(this.current=t,r.bindFramebuffer(r.FRAMEBUFFER,n.framebuffer)),n.mipLevel!==s&&(t.dirtyId++,t.dirtyFormat++,n.mipLevel=s),n.dirtyId!==t.dirtyId&&(n.dirtyId=t.dirtyId,n.dirtyFormat!==t.dirtyFormat?(n.dirtyFormat=t.dirtyFormat,n.dirtySize=t.dirtySize,this.updateFramebuffer(t,s)):n.dirtySize!==t.dirtySize&&(n.dirtySize=t.dirtySize,this.resizeFramebuffer(t)));for(let o=0;o<t.colorTextures.length;o++){const a=t.colorTextures[o];this.renderer.texture.unbind(a.parentTextureArray||a)}if(t.depthTexture&&this.renderer.texture.unbind(t.depthTexture),e){const o=e.width>>s,a=e.height>>s,h=o/e.width;this.setViewport(e.x*h,e.y*h,o,a)}else{const o=t.width>>s,a=t.height>>s;this.setViewport(0,0,o,a)}}else this.current&&(this.current=null,r.bindFramebuffer(r.FRAMEBUFFER,null)),e?this.setViewport(e.x,e.y,e.width,e.height):this.setViewport(0,0,this.renderer.width,this.renderer.height)}setViewport(t,e,s,r){const n=this.viewport;t=Math.round(t),e=Math.round(e),s=Math.round(s),r=Math.round(r),(n.width!==s||n.height!==r||n.x!==t||n.y!==e)&&(n.x=t,n.y=e,n.width=s,n.height=r,this.gl.viewport(t,e,s,r))}get size(){return this.current?{x:0,y:0,width:this.current.width,height:this.current.height}:{x:0,y:0,width:this.renderer.width,height:this.renderer.height}}clear(t,e,s,r,n=At.COLOR|At.DEPTH){const{gl:o}=this;o.clearColor(t,e,s,r),o.clear(n)}initFramebuffer(t){const{gl:e}=this,s=new Oo(e.createFramebuffer());return s.multisample=this.detectSamples(t.multisample),t.glFramebuffers[this.CONTEXT_UID]=s,this.managedFramebuffers.push(t),t.disposeRunner.add(this),s}resizeFramebuffer(t){const{gl:e}=this,s=t.glFramebuffers[this.CONTEXT_UID];s.stencil&&(e.bindRenderbuffer(e.RENDERBUFFER,s.stencil),s.msaaBuffer?e.renderbufferStorageMultisample(e.RENDERBUFFER,s.multisample,e.DEPTH24_STENCIL8,t.width,t.height):e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_STENCIL,t.width,t.height));const r=t.colorTextures;let n=r.length;e.drawBuffers||(n=Math.min(n,1));for(let o=0;o<n;o++){const a=r[o],h=a.parentTextureArray||a;this.renderer.texture.bind(h,0),o===0&&s.msaaBuffer&&(e.bindRenderbuffer(e.RENDERBUFFER,s.msaaBuffer),e.renderbufferStorageMultisample(e.RENDERBUFFER,s.multisample,h._glTextures[this.CONTEXT_UID].internalFormat,t.width,t.height))}t.depthTexture&&this.writeDepthTexture&&this.renderer.texture.bind(t.depthTexture,0)}updateFramebuffer(t,e){const{gl:s}=this,r=t.glFramebuffers[this.CONTEXT_UID],n=t.colorTextures;let o=n.length;s.drawBuffers||(o=Math.min(o,1)),r.multisample>1&&this.canMultisampleFramebuffer(t)?r.msaaBuffer=r.msaaBuffer||s.createRenderbuffer():r.msaaBuffer&&(s.deleteRenderbuffer(r.msaaBuffer),r.msaaBuffer=null,r.blitFramebuffer&&(r.blitFramebuffer.dispose(),r.blitFramebuffer=null));const a=[];for(let h=0;h<o;h++){const l=n[h],c=l.parentTextureArray||l;this.renderer.texture.bind(c,0),h===0&&r.msaaBuffer?(s.bindRenderbuffer(s.RENDERBUFFER,r.msaaBuffer),s.renderbufferStorageMultisample(s.RENDERBUFFER,r.multisample,c._glTextures[this.CONTEXT_UID].internalFormat,t.width,t.height),s.framebufferRenderbuffer(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.RENDERBUFFER,r.msaaBuffer)):(s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0+h,l.target,c._glTextures[this.CONTEXT_UID].texture,e),a.push(s.COLOR_ATTACHMENT0+h))}if(a.length>1&&s.drawBuffers(a),t.depthTexture&&this.writeDepthTexture){const l=t.depthTexture;this.renderer.texture.bind(l,0),s.framebufferTexture2D(s.FRAMEBUFFER,s.DEPTH_ATTACHMENT,s.TEXTURE_2D,l._glTextures[this.CONTEXT_UID].texture,e)}(t.stencil||t.depth)&&!(t.depthTexture&&this.writeDepthTexture)?(r.stencil=r.stencil||s.createRenderbuffer(),s.bindRenderbuffer(s.RENDERBUFFER,r.stencil),r.msaaBuffer?s.renderbufferStorageMultisample(s.RENDERBUFFER,r.multisample,s.DEPTH24_STENCIL8,t.width,t.height):s.renderbufferStorage(s.RENDERBUFFER,s.DEPTH_STENCIL,t.width,t.height),s.framebufferRenderbuffer(s.FRAMEBUFFER,s.DEPTH_STENCIL_ATTACHMENT,s.RENDERBUFFER,r.stencil)):r.stencil&&(s.deleteRenderbuffer(r.stencil),r.stencil=null)}canMultisampleFramebuffer(t){return this.renderer.context.webGLVersion!==1&&t.colorTextures.length<=1&&!t.depthTexture}detectSamples(t){const{msaaSamples:e}=this;let s=ft.NONE;if(t<=1||e===null)return s;for(let r=0;r<e.length;r++)if(e[r]<=t){s=e[r];break}return s===1&&(s=ft.NONE),s}blit(t,e,s){const{current:r,renderer:n,gl:o,CONTEXT_UID:a}=this;if(n.context.webGLVersion!==2||!r)return;const h=r.glFramebuffers[a];if(!h)return;if(!t){if(!h.msaaBuffer)return;const c=r.colorTextures[0];if(!c)return;h.blitFramebuffer||(h.blitFramebuffer=new es(r.width,r.height),h.blitFramebuffer.addColorTexture(0,c)),t=h.blitFramebuffer,t.colorTextures[0]!==c&&(t.colorTextures[0]=c,t.dirtyId++,t.dirtyFormat++),(t.width!==r.width||t.height!==r.height)&&(t.width=r.width,t.height=r.height,t.dirtyId++,t.dirtySize++)}e||(e=wd,e.width=r.width,e.height=r.height),s||(s=e);const l=e.width===s.width&&e.height===s.height;this.bind(t),o.bindFramebuffer(o.READ_FRAMEBUFFER,h.framebuffer),o.blitFramebuffer(e.left,e.top,e.right,e.bottom,s.left,s.top,s.right,s.bottom,o.COLOR_BUFFER_BIT,l?o.NEAREST:o.LINEAR)}disposeFramebuffer(t,e){const s=t.glFramebuffers[this.CONTEXT_UID],r=this.gl;if(!s)return;delete t.glFramebuffers[this.CONTEXT_UID];const n=this.managedFramebuffers.indexOf(t);n>=0&&this.managedFramebuffers.splice(n,1),t.disposeRunner.remove(this),e||(r.deleteFramebuffer(s.framebuffer),s.msaaBuffer&&r.deleteRenderbuffer(s.msaaBuffer),s.stencil&&r.deleteRenderbuffer(s.stencil)),s.blitFramebuffer&&s.blitFramebuffer.dispose()}disposeAll(t){const e=this.managedFramebuffers;this.managedFramebuffers=[];for(let s=0;s<e.length;s++)this.disposeFramebuffer(e[s],t)}forceStencil(){const t=this.current;if(!t)return;const e=t.glFramebuffers[this.CONTEXT_UID];if(!e||e.stencil)return;t.stencil=!0;const s=t.width,r=t.height,n=this.gl,o=n.createRenderbuffer();n.bindRenderbuffer(n.RENDERBUFFER,o),e.msaaBuffer?n.renderbufferStorageMultisample(n.RENDERBUFFER,e.multisample,n.DEPTH24_STENCIL8,s,r):n.renderbufferStorage(n.RENDERBUFFER,n.DEPTH_STENCIL,s,r),e.stencil=o,n.framebufferRenderbuffer(n.FRAMEBUFFER,n.DEPTH_STENCIL_ATTACHMENT,n.RENDERBUFFER,o)}reset(){this.current=this.unknownFramebuffer,this.viewport=new j}destroy(){this.renderer=null}}yr.extension={type:F.RendererSystem,name:"framebuffer"},U.add(yr);const xr={5126:4,5123:2,5121:1};class Tr{constructor(t){this.renderer=t,this._activeGeometry=null,this._activeVao=null,this.hasVao=!0,this.hasInstance=!0,this.canUseUInt32ElementIndex=!1,this.managedGeometries={}}contextChange(){this.disposeAll(!0);const t=this.gl=this.renderer.gl,e=this.renderer.context;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,e.webGLVersion!==2){let s=this.renderer.context.extensions.vertexArrayObject;P.PREFER_ENV===lt.WEBGL_LEGACY&&(s=null),s?(t.createVertexArray=()=>s.createVertexArrayOES(),t.bindVertexArray=r=>s.bindVertexArrayOES(r),t.deleteVertexArray=r=>s.deleteVertexArrayOES(r)):(this.hasVao=!1,t.createVertexArray=()=>null,t.bindVertexArray=()=>null,t.deleteVertexArray=()=>null)}if(e.webGLVersion!==2){const s=t.getExtension("ANGLE_instanced_arrays");s?(t.vertexAttribDivisor=(r,n)=>s.vertexAttribDivisorANGLE(r,n),t.drawElementsInstanced=(r,n,o,a,h)=>s.drawElementsInstancedANGLE(r,n,o,a,h),t.drawArraysInstanced=(r,n,o,a)=>s.drawArraysInstancedANGLE(r,n,o,a)):this.hasInstance=!1}this.canUseUInt32ElementIndex=e.webGLVersion===2||!!e.extensions.uint32ElementIndex}bind(t,e){e=e||this.renderer.shader.shader;const{gl:s}=this;let r=t.glVertexArrayObjects[this.CONTEXT_UID],n=!1;r||(this.managedGeometries[t.id]=t,t.disposeRunner.add(this),t.glVertexArrayObjects[this.CONTEXT_UID]=r={},n=!0);const o=r[e.program.id]||this.initGeometryVao(t,e,n);this._activeGeometry=t,this._activeVao!==o&&(this._activeVao=o,this.hasVao?s.bindVertexArray(o):this.activateVao(t,e.program)),this.updateBuffers()}reset(){this.unbind()}updateBuffers(){const t=this._activeGeometry,e=this.renderer.buffer;for(let s=0;s<t.buffers.length;s++){const r=t.buffers[s];e.update(r)}}checkCompatibility(t,e){const s=t.attributes,r=e.attributeData;for(const n in r)if(!s[n])throw new Error(`shader and geometry incompatible, geometry missing the "${n}" attribute`)}getSignature(t,e){const s=t.attributes,r=e.attributeData,n=["g",t.id];for(const o in s)r[o]&&n.push(o,r[o].location);return n.join("-")}initGeometryVao(t,e,s=!0){const r=this.gl,n=this.CONTEXT_UID,o=this.renderer.buffer,a=e.program;a.glPrograms[n]||this.renderer.shader.generateProgram(e),this.checkCompatibility(t,a);const h=this.getSignature(t,a),l=t.glVertexArrayObjects[this.CONTEXT_UID];let c=l[h];if(c)return l[a.id]=c,c;const u=t.buffers,d=t.attributes,f={},p={};for(const g in u)f[g]=0,p[g]=0;for(const g in d)!d[g].size&&a.attributeData[g]?d[g].size=a.attributeData[g].size:d[g].size||console.warn(`PIXI Geometry attribute '${g}' size cannot be determined (likely the bound shader does not have the attribute)`),f[d[g].buffer]+=d[g].size*xr[d[g].type];for(const g in d){const m=d[g],x=m.size;m.stride===void 0&&(f[m.buffer]===x*xr[m.type]?m.stride=0:m.stride=f[m.buffer]),m.start===void 0&&(m.start=p[m.buffer],p[m.buffer]+=x*xr[m.type])}c=r.createVertexArray(),r.bindVertexArray(c);for(let g=0;g<u.length;g++){const m=u[g];o.bind(m),s&&m._glBuffers[n].refCount++}return this.activateVao(t,a),l[a.id]=c,l[h]=c,r.bindVertexArray(null),o.unbind(Xt.ARRAY_BUFFER),c}disposeGeometry(t,e){var a;if(!this.managedGeometries[t.id])return;delete this.managedGeometries[t.id];const s=t.glVertexArrayObjects[this.CONTEXT_UID],r=this.gl,n=t.buffers,o=(a=this.renderer)==null?void 0:a.buffer;if(t.disposeRunner.remove(this),!!s){if(o)for(let h=0;h<n.length;h++){const l=n[h]._glBuffers[this.CONTEXT_UID];l&&(l.refCount--,l.refCount===0&&!e&&o.dispose(n[h],e))}if(!e){for(const h in s)if(h[0]==="g"){const l=s[h];this._activeVao===l&&this.unbind(),r.deleteVertexArray(l)}}delete t.glVertexArrayObjects[this.CONTEXT_UID]}}disposeAll(t){const e=Object.keys(this.managedGeometries);for(let s=0;s<e.length;s++)this.disposeGeometry(this.managedGeometries[e[s]],t)}activateVao(t,e){const s=this.gl,r=this.CONTEXT_UID,n=this.renderer.buffer,o=t.buffers,a=t.attributes;t.indexBuffer&&n.bind(t.indexBuffer);let h=null;for(const l in a){const c=a[l],u=o[c.buffer],d=u._glBuffers[r];if(e.attributeData[l]){h!==d&&(n.bind(u),h=d);const f=e.attributeData[l].location;if(s.enableVertexAttribArray(f),s.vertexAttribPointer(f,c.size,c.type||s.FLOAT,c.normalized,c.stride,c.start),c.instance)if(this.hasInstance)s.vertexAttribDivisor(f,1);else throw new Error("geometry error, GPU Instancing is not supported on this device")}}}draw(t,e,s,r){const{gl:n}=this,o=this._activeGeometry;if(o.indexBuffer){const a=o.indexBuffer.data.BYTES_PER_ELEMENT,h=a===2?n.UNSIGNED_SHORT:n.UNSIGNED_INT;a===2||a===4&&this.canUseUInt32ElementIndex?o.instanced?n.drawElementsInstanced(t,e||o.indexBuffer.data.length,h,(s||0)*a,r||1):n.drawElements(t,e||o.indexBuffer.data.length,h,(s||0)*a):console.warn("unsupported index buffer type: uint32")}else o.instanced?n.drawArraysInstanced(t,s,e||o.getSize(),r||1):n.drawArrays(t,s,e||o.getSize());return this}unbind(){this.gl.bindVertexArray(null),this._activeVao=null,this._activeGeometry=null}destroy(){this.renderer=null}}Tr.extension={type:F.RendererSystem,name:"geometry"},U.add(Tr);class Lo{constructor(t=null){this.type=mt.NONE,this.autoDetect=!0,this.maskObject=t||null,this.pooled=!1,this.isMaskData=!0,this.resolution=null,this.multisample=P.FILTER_MULTISAMPLE,this.enabled=!0,this.colorMask=15,this._filters=null,this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null,this._scissorRectLocal=null,this._colorMask=15,this._target=null}get filter(){return this._filters?this._filters[0]:null}set filter(t){t?this._filters?this._filters[0]=t:this._filters=[t]:this._filters=null}reset(){this.pooled&&(this.maskObject=null,this.type=mt.NONE,this.autoDetect=!0),this._target=null,this._scissorRectLocal=null}copyCountersOrReset(t){t?(this._stencilCounter=t._stencilCounter,this._scissorCounter=t._scissorCounter,this._scissorRect=t._scissorRect):(this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null)}}function Uo(i,t,e){const s=i.createShader(t);return i.shaderSource(s,e),i.compileShader(s),s}function ko(i,t){const e=i.getShaderSource(t).split(`
`).map((l,c)=>`${c}: ${l}`),s=i.getShaderInfoLog(t),r=s.split(`
`),n={},o=r.map(l=>parseFloat(l.replace(/^ERROR\: 0\:([\d]+)\:.*$/,"$1"))).filter(l=>l&&!n[l]?(n[l]=!0,!0):!1),a=[""];o.forEach(l=>{e[l-1]=`%c${e[l-1]}%c`,a.push("background: #FF0000; color:#FFFFFF; font-size: 10px","font-size: 10px")});const h=e.join(`
`);a[0]=h,console.error(s),console.groupCollapsed("click to view full shader code"),console.warn(...a),console.groupEnd()}function Sd(i,t,e,s){i.getProgramParameter(t,i.LINK_STATUS)||(i.getShaderParameter(e,i.COMPILE_STATUS)||ko(i,e),i.getShaderParameter(s,i.COMPILE_STATUS)||ko(i,s),console.error("PixiJS Error: Could not initialize shader."),i.getProgramInfoLog(t)!==""&&console.warn("PixiJS Warning: gl.getProgramInfoLog()",i.getProgramInfoLog(t)))}function br(i){const t=new Array(i);for(let e=0;e<t.length;e++)t[e]=!1;return t}function Go(i,t){switch(i){case"float":return 0;case"vec2":return new Float32Array(2*t);case"vec3":return new Float32Array(3*t);case"vec4":return new Float32Array(4*t);case"int":case"uint":case"sampler2D":case"sampler2DArray":return 0;case"ivec2":return new Int32Array(2*t);case"ivec3":return new Int32Array(3*t);case"ivec4":return new Int32Array(4*t);case"uvec2":return new Uint32Array(2*t);case"uvec3":return new Uint32Array(3*t);case"uvec4":return new Uint32Array(4*t);case"bool":return!1;case"bvec2":return br(2*t);case"bvec3":return br(3*t);case"bvec4":return br(4*t);case"mat2":return new Float32Array([1,0,0,1]);case"mat3":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const Ho={};let ze=Ho;function Xo(){if(ze===Ho||(ze==null?void 0:ze.isContextLost())){const i=P.ADAPTER.createCanvas();let t;P.PREFER_ENV>=lt.WEBGL2&&(t=i.getContext("webgl2",{})),t||(t=i.getContext("webgl",{})||i.getContext("experimental-webgl",{}),t?t.getExtension("WEBGL_draw_buffers"):t=null),ze=t}return ze}let ns;function Ad(){if(!ns){ns=Rt.MEDIUM;const i=Xo();i&&i.getShaderPrecisionFormat&&(ns=i.getShaderPrecisionFormat(i.FRAGMENT_SHADER,i.HIGH_FLOAT).precision?Rt.HIGH:Rt.MEDIUM)}return ns}function Vo(i,t,e){if(i.substring(0,9)!=="precision"){let s=t;return t===Rt.HIGH&&e!==Rt.HIGH&&(s=Rt.MEDIUM),`precision ${s} float;
${i}`}else if(e!==Rt.HIGH&&i.substring(0,15)==="precision highp")return i.replace("precision highp","precision mediump");return i}const Cd={float:1,vec2:2,vec3:3,vec4:4,int:1,ivec2:2,ivec3:3,ivec4:4,uint:1,uvec2:2,uvec3:3,uvec4:4,bool:1,bvec2:2,bvec3:3,bvec4:4,mat2:4,mat3:9,mat4:16,sampler2D:1};function zo(i){return Cd[i]}let os=null;const Wo={FLOAT:"float",FLOAT_VEC2:"vec2",FLOAT_VEC3:"vec3",FLOAT_VEC4:"vec4",INT:"int",INT_VEC2:"ivec2",INT_VEC3:"ivec3",INT_VEC4:"ivec4",UNSIGNED_INT:"uint",UNSIGNED_INT_VEC2:"uvec2",UNSIGNED_INT_VEC3:"uvec3",UNSIGNED_INT_VEC4:"uvec4",BOOL:"bool",BOOL_VEC2:"bvec2",BOOL_VEC3:"bvec3",BOOL_VEC4:"bvec4",FLOAT_MAT2:"mat2",FLOAT_MAT3:"mat3",FLOAT_MAT4:"mat4",SAMPLER_2D:"sampler2D",INT_SAMPLER_2D:"sampler2D",UNSIGNED_INT_SAMPLER_2D:"sampler2D",SAMPLER_CUBE:"samplerCube",INT_SAMPLER_CUBE:"samplerCube",UNSIGNED_INT_SAMPLER_CUBE:"samplerCube",SAMPLER_2D_ARRAY:"sampler2DArray",INT_SAMPLER_2D_ARRAY:"sampler2DArray",UNSIGNED_INT_SAMPLER_2D_ARRAY:"sampler2DArray"};function jo(i,t){if(!os){const e=Object.keys(Wo);os={};for(let s=0;s<e.length;++s){const r=e[s];os[i[r]]=Wo[r]}}return os[t]}const Ie=[{test:i=>i.type==="float"&&i.size===1&&!i.isArray,code:i=>`
            if(uv["${i}"] !== ud["${i}"].value)
            {
                ud["${i}"].value = uv["${i}"]
                gl.uniform1f(ud["${i}"].location, uv["${i}"])
            }
            `},{test:(i,t)=>(i.type==="sampler2D"||i.type==="samplerCube"||i.type==="sampler2DArray")&&i.size===1&&!i.isArray&&(t==null||t.castToBaseTexture!==void 0),code:i=>`t = syncData.textureCount++;

            renderer.texture.bind(uv["${i}"], t);

            if(ud["${i}"].value !== t)
            {
                ud["${i}"].value = t;
                gl.uniform1i(ud["${i}"].location, t);
; // eslint-disable-line max-len
            }`},{test:(i,t)=>i.type==="mat3"&&i.size===1&&!i.isArray&&t.a!==void 0,code:i=>`
            gl.uniformMatrix3fv(ud["${i}"].location, false, uv["${i}"].toArray(true));
            `,codeUbo:i=>`
                var ${i}_matrix = uv.${i}.toArray(true);

                data[offset] = ${i}_matrix[0];
                data[offset+1] = ${i}_matrix[1];
                data[offset+2] = ${i}_matrix[2];

                data[offset + 4] = ${i}_matrix[3];
                data[offset + 5] = ${i}_matrix[4];
                data[offset + 6] = ${i}_matrix[5];

                data[offset + 8] = ${i}_matrix[6];
                data[offset + 9] = ${i}_matrix[7];
                data[offset + 10] = ${i}_matrix[8];
            `},{test:(i,t)=>i.type==="vec2"&&i.size===1&&!i.isArray&&t.x!==void 0,code:i=>`
                cv = ud["${i}"].value;
                v = uv["${i}"];

                if(cv[0] !== v.x || cv[1] !== v.y)
                {
                    cv[0] = v.x;
                    cv[1] = v.y;
                    gl.uniform2f(ud["${i}"].location, v.x, v.y);
                }`,codeUbo:i=>`
                v = uv.${i};

                data[offset] = v.x;
                data[offset+1] = v.y;
            `},{test:i=>i.type==="vec2"&&i.size===1&&!i.isArray,code:i=>`
                cv = ud["${i}"].value;
                v = uv["${i}"];

                if(cv[0] !== v[0] || cv[1] !== v[1])
                {
                    cv[0] = v[0];
                    cv[1] = v[1];
                    gl.uniform2f(ud["${i}"].location, v[0], v[1]);
                }
            `},{test:(i,t)=>i.type==="vec4"&&i.size===1&&!i.isArray&&t.width!==void 0,code:i=>`
                cv = ud["${i}"].value;
                v = uv["${i}"];

                if(cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height)
                {
                    cv[0] = v.x;
                    cv[1] = v.y;
                    cv[2] = v.width;
                    cv[3] = v.height;
                    gl.uniform4f(ud["${i}"].location, v.x, v.y, v.width, v.height)
                }`,codeUbo:i=>`
                    v = uv.${i};

                    data[offset] = v.x;
                    data[offset+1] = v.y;
                    data[offset+2] = v.width;
                    data[offset+3] = v.height;
                `},{test:i=>i.type==="vec4"&&i.size===1&&!i.isArray,code:i=>`
                cv = ud["${i}"].value;
                v = uv["${i}"];

                if(cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
                {
                    cv[0] = v[0];
                    cv[1] = v[1];
                    cv[2] = v[2];
                    cv[3] = v[3];

                    gl.uniform4f(ud["${i}"].location, v[0], v[1], v[2], v[3])
                }`}],Rd={float:`
    if (cv !== v)
    {
        cu.value = v;
        gl.uniform1f(location, v);
    }`,vec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2f(location, v[0], v[1])
    }`,vec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3f(location, v[0], v[1], v[2])
    }`,vec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4f(location, v[0], v[1], v[2], v[3]);
    }`,int:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,ivec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2i(location, v[0], v[1]);
    }`,ivec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3i(location, v[0], v[1], v[2]);
    }`,ivec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4i(location, v[0], v[1], v[2], v[3]);
    }`,uint:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1ui(location, v);
    }`,uvec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2ui(location, v[0], v[1]);
    }`,uvec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3ui(location, v[0], v[1], v[2]);
    }`,uvec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4ui(location, v[0], v[1], v[2], v[3]);
    }`,bool:`
    if (cv !== v)
    {
        cu.value = v;
        gl.uniform1i(location, v);
    }`,bvec2:`
    if (cv[0] != v[0] || cv[1] != v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2i(location, v[0], v[1]);
    }`,bvec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3i(location, v[0], v[1], v[2]);
    }`,bvec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4i(location, v[0], v[1], v[2], v[3]);
    }`,mat2:"gl.uniformMatrix2fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",sampler2D:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,samplerCube:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,sampler2DArray:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`},Id={float:"gl.uniform1fv(location, v)",vec2:"gl.uniform2fv(location, v)",vec3:"gl.uniform3fv(location, v)",vec4:"gl.uniform4fv(location, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat2:"gl.uniformMatrix2fv(location, false, v)",int:"gl.uniform1iv(location, v)",ivec2:"gl.uniform2iv(location, v)",ivec3:"gl.uniform3iv(location, v)",ivec4:"gl.uniform4iv(location, v)",uint:"gl.uniform1uiv(location, v)",uvec2:"gl.uniform2uiv(location, v)",uvec3:"gl.uniform3uiv(location, v)",uvec4:"gl.uniform4uiv(location, v)",bool:"gl.uniform1iv(location, v)",bvec2:"gl.uniform2iv(location, v)",bvec3:"gl.uniform3iv(location, v)",bvec4:"gl.uniform4iv(location, v)",sampler2D:"gl.uniform1iv(location, v)",samplerCube:"gl.uniform1iv(location, v)",sampler2DArray:"gl.uniform1iv(location, v)"};function Pd(i,t){var s;const e=[`
        var v = null;
        var cv = null;
        var cu = null;
        var t = 0;
        var gl = renderer.gl;
    `];for(const r in i.uniforms){const n=t[r];if(!n){(s=i.uniforms[r])!=null&&s.group&&(i.uniforms[r].ubo?e.push(`
                        renderer.shader.syncUniformBufferGroup(uv.${r}, '${r}');
                    `):e.push(`
                        renderer.shader.syncUniformGroup(uv.${r}, syncData);
                    `));continue}const o=i.uniforms[r];let a=!1;for(let h=0;h<Ie.length;h++)if(Ie[h].test(n,o)){e.push(Ie[h].code(r,o)),a=!0;break}if(!a){const l=(n.size===1&&!n.isArray?Rd:Id)[n.type].replace("location",`ud["${r}"].location`);e.push(`
            cu = ud["${r}"];
            cv = cu.value;
            v = uv["${r}"];
            ${l};`)}}return new Function("ud","uv","renderer","syncData",e.join(`
`))}const Md=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function Bd(i){let t="";for(let e=0;e<i;++e)e>0&&(t+=`
else `),e<i-1&&(t+=`if(test == ${e}.0){}`);return t}function Yo(i,t){if(i===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const e=t.createShader(t.FRAGMENT_SHADER);for(;;){const s=Md.replace(/%forloop%/gi,Bd(i));if(t.shaderSource(e,s),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))i=i/2|0;else break}return i}let hi;function Dd(){if(typeof hi=="boolean")return hi;try{hi=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch(i){hi=!1}return hi}var Nd=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;

void main(void){
   gl_FragColor *= texture2D(uSampler, vTextureCoord);
}`,Fd=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

void main(void){
   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
   vTextureCoord = aTextureCoord;
}
`;let Od=0;const as={};class Qt{constructor(t,e,s="pixi-shader",r={}){this.extra={},this.id=Od++,this.vertexSrc=t||Qt.defaultVertexSrc,this.fragmentSrc=e||Qt.defaultFragmentSrc,this.vertexSrc=this.vertexSrc.trim(),this.fragmentSrc=this.fragmentSrc.trim(),this.extra=r,this.vertexSrc.substring(0,8)!=="#version"&&(s=s.replace(/\s+/g,"-"),as[s]?(as[s]++,s+=`-${as[s]}`):as[s]=1,this.vertexSrc=`#define SHADER_NAME ${s}
${this.vertexSrc}`,this.fragmentSrc=`#define SHADER_NAME ${s}
${this.fragmentSrc}`,this.vertexSrc=Vo(this.vertexSrc,P.PRECISION_VERTEX,Rt.HIGH),this.fragmentSrc=Vo(this.fragmentSrc,P.PRECISION_FRAGMENT,Ad())),this.glPrograms={},this.syncUniforms=null}static get defaultVertexSrc(){return Fd}static get defaultFragmentSrc(){return Nd}static from(t,e,s){const r=t+e;let n=nr[r];return n||(nr[r]=n=new Qt(t,e,s)),n}}class Wt{constructor(t,e){this.uniformBindCount=0,this.program=t,e?e instanceof Lt?this.uniformGroup=e:this.uniformGroup=new Lt(e):this.uniformGroup=new Lt({}),this.disposeRunner=new It("disposeShader")}checkUniformExists(t,e){if(e.uniforms[t])return!0;for(const s in e.uniforms){const r=e.uniforms[s];if(r.group&&this.checkUniformExists(t,r))return!0}return!1}destroy(){this.uniformGroup=null,this.disposeRunner.emit(this),this.disposeRunner.destroy()}get uniforms(){return this.uniformGroup.uniforms}static from(t,e,s){const r=Qt.from(t,e);return new Wt(r,s)}}const Er=0,wr=1,Sr=2,Ar=3,Cr=4,Rr=5;class Jt{constructor(){this.data=0,this.blendMode=C.NORMAL,this.polygonOffset=0,this.blend=!0,this.depthMask=!0}get blend(){return!!(this.data&1<<Er)}set blend(t){!!(this.data&1<<Er)!==t&&(this.data^=1<<Er)}get offsets(){return!!(this.data&1<<wr)}set offsets(t){!!(this.data&1<<wr)!==t&&(this.data^=1<<wr)}get culling(){return!!(this.data&1<<Sr)}set culling(t){!!(this.data&1<<Sr)!==t&&(this.data^=1<<Sr)}get depthTest(){return!!(this.data&1<<Ar)}set depthTest(t){!!(this.data&1<<Ar)!==t&&(this.data^=1<<Ar)}get depthMask(){return!!(this.data&1<<Rr)}set depthMask(t){!!(this.data&1<<Rr)!==t&&(this.data^=1<<Rr)}get clockwiseFrontFace(){return!!(this.data&1<<Cr)}set clockwiseFrontFace(t){!!(this.data&1<<Cr)!==t&&(this.data^=1<<Cr)}get blendMode(){return this._blendMode}set blendMode(t){this.blend=t!==C.NONE,this._blendMode=t}get polygonOffset(){return this._polygonOffset}set polygonOffset(t){this.offsets=!!t,this._polygonOffset=t}toString(){return`[@pixi/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`}static for2d(){const t=new Jt;return t.depthTest=!1,t.blend=!0,t}}var Ld=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;

void main(void){
   gl_FragColor = texture2D(uSampler, vTextureCoord);
}
`,Ud=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`;class Ut extends Wt{constructor(t,e,s){const r=Qt.from(t||Ut.defaultVertexSrc,e||Ut.defaultFragmentSrc);super(r,s),this.padding=0,this.resolution=P.FILTER_RESOLUTION,this.multisample=P.FILTER_MULTISAMPLE,this.enabled=!0,this.autoFit=!0,this.state=new Jt}apply(t,e,s,r,n){t.applyFilter(this,e,s,r)}get blendMode(){return this.state.blendMode}set blendMode(t){this.state.blendMode=t}get resolution(){return this._resolution}set resolution(t){this._resolution=t}static get defaultVertexSrc(){return Ud}static get defaultFragmentSrc(){return Ld}}var kd=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 otherMatrix;

varying vec2 vMaskCoord;
varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vMaskCoord = ( otherMatrix * vec3( aTextureCoord, 1.0)  ).xy;
}
`,Gd=`varying vec2 vMaskCoord;
varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform sampler2D mask;
uniform float alpha;
uniform float npmAlpha;
uniform vec4 maskClamp;

void main(void)
{
    float clip = step(3.5,
        step(maskClamp.x, vMaskCoord.x) +
        step(maskClamp.y, vMaskCoord.y) +
        step(vMaskCoord.x, maskClamp.z) +
        step(vMaskCoord.y, maskClamp.w));

    vec4 original = texture2D(uSampler, vTextureCoord);
    vec4 masky = texture2D(mask, vMaskCoord);
    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);

    original *= (alphaMul * masky.r * alpha * clip);

    gl_FragColor = original;
}
`;const $o=new Z;class hs{constructor(t,e){this._texture=t,this.mapCoord=new Z,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,this.clampMargin=typeof e=="undefined"?.5:e,this.isSimple=!1}get texture(){return this._texture}set texture(t){this._texture=t,this._textureID=-1}multiplyUvs(t,e){e===void 0&&(e=t);const s=this.mapCoord;for(let r=0;r<t.length;r+=2){const n=t[r],o=t[r+1];e[r]=n*s.a+o*s.c+s.tx,e[r+1]=n*s.b+o*s.d+s.ty}return e}update(t){const e=this._texture;if(!e||!e.valid||!t&&this._textureID===e._updateID)return!1;this._textureID=e._updateID,this._updateID++;const s=e._uvs;this.mapCoord.set(s.x1-s.x0,s.y1-s.y0,s.x3-s.x0,s.y3-s.y0,s.x0,s.y0);const r=e.orig,n=e.trim;n&&($o.set(r.width/n.width,0,0,r.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append($o));const o=e.baseTexture,a=this.uClampFrame,h=this.clampMargin/o.resolution,l=this.clampOffset;return a[0]=(e._frame.x+h+l)/o.width,a[1]=(e._frame.y+h+l)/o.height,a[2]=(e._frame.x+e._frame.width-h+l)/o.width,a[3]=(e._frame.y+e._frame.height-h+l)/o.height,this.uClampOffset[0]=l/o.realWidth,this.uClampOffset[1]=l/o.realHeight,this.isSimple=e._frame.width===o.width&&e._frame.height===o.height&&e.rotate===0,!0}}class qo extends Ut{constructor(t,e,s){let r=null;typeof t!="string"&&e===void 0&&s===void 0&&(r=t,t=void 0,e=void 0,s=void 0),super(t||kd,e||Gd,s),this.maskSprite=r,this.maskMatrix=new Z}get maskSprite(){return this._maskSprite}set maskSprite(t){this._maskSprite=t,this._maskSprite&&(this._maskSprite.renderable=!1)}apply(t,e,s,r){const n=this._maskSprite,o=n._texture;!o.valid||(o.uvMatrix||(o.uvMatrix=new hs(o,0)),o.uvMatrix.update(),this.uniforms.npmAlpha=o.baseTexture.alphaMode?0:1,this.uniforms.mask=o,this.uniforms.otherMatrix=t.calculateSpriteMatrix(this.maskMatrix,n).prepend(o.uvMatrix.mapCoord),this.uniforms.alpha=n.worldAlpha,this.uniforms.maskClamp=o.uvMatrix.uClampFrame,t.applyFilter(this,e,s,r))}}class Ir{constructor(t){this.renderer=t,this.enableScissor=!0,this.alphaMaskPool=[],this.maskDataPool=[],this.maskStack=[],this.alphaMaskIndex=0}setMaskStack(t){this.maskStack=t,this.renderer.scissor.setMaskStack(t),this.renderer.stencil.setMaskStack(t)}push(t,e){let s=e;if(!s.isMaskData){const n=this.maskDataPool.pop()||new Lo;n.pooled=!0,n.maskObject=e,s=n}const r=this.maskStack.length!==0?this.maskStack[this.maskStack.length-1]:null;if(s.copyCountersOrReset(r),s._colorMask=r?r._colorMask:15,s.autoDetect&&this.detect(s),s._target=t,s.type!==mt.SPRITE&&this.maskStack.push(s),s.enabled)switch(s.type){case mt.SCISSOR:this.renderer.scissor.push(s);break;case mt.STENCIL:this.renderer.stencil.push(s);break;case mt.SPRITE:s.copyCountersOrReset(null),this.pushSpriteMask(s);break;case mt.COLOR:this.pushColorMask(s);break;default:break}s.type===mt.SPRITE&&this.maskStack.push(s)}pop(t){const e=this.maskStack.pop();if(!(!e||e._target!==t)){if(e.enabled)switch(e.type){case mt.SCISSOR:this.renderer.scissor.pop(e);break;case mt.STENCIL:this.renderer.stencil.pop(e.maskObject);break;case mt.SPRITE:this.popSpriteMask(e);break;case mt.COLOR:this.popColorMask(e);break;default:break}if(e.reset(),e.pooled&&this.maskDataPool.push(e),this.maskStack.length!==0){const s=this.maskStack[this.maskStack.length-1];s.type===mt.SPRITE&&s._filters&&(s._filters[0].maskSprite=s.maskObject)}}}detect(t){const e=t.maskObject;e?e.isSprite?t.type=mt.SPRITE:this.enableScissor&&this.renderer.scissor.testScissor(t)?t.type=mt.SCISSOR:t.type=mt.STENCIL:t.type=mt.COLOR}pushSpriteMask(t){var c,u;const{maskObject:e}=t,s=t._target;let r=t._filters;r||(r=this.alphaMaskPool[this.alphaMaskIndex],r||(r=this.alphaMaskPool[this.alphaMaskIndex]=[new qo]));const n=this.renderer,o=n.renderTexture;let a,h;if(o.current){const d=o.current;a=t.resolution||d.resolution,h=(c=t.multisample)!=null?c:d.multisample}else a=t.resolution||n.resolution,h=(u=t.multisample)!=null?u:n.multisample;r[0].resolution=a,r[0].multisample=h,r[0].maskSprite=e;const l=s.filterArea;s.filterArea=e.getBounds(!0),n.filter.push(s,r),s.filterArea=l,t._filters||this.alphaMaskIndex++}popSpriteMask(t){this.renderer.filter.pop(),t._filters?t._filters[0].maskSprite=null:(this.alphaMaskIndex--,this.alphaMaskPool[this.alphaMaskIndex][0].maskSprite=null)}pushColorMask(t){const e=t._colorMask,s=t._colorMask=e&t.colorMask;s!==e&&this.renderer.gl.colorMask((s&1)!==0,(s&2)!==0,(s&4)!==0,(s&8)!==0)}popColorMask(t){const e=t._colorMask,s=this.maskStack.length>0?this.maskStack[this.maskStack.length-1]._colorMask:15;s!==e&&this.renderer.gl.colorMask((s&1)!==0,(s&2)!==0,(s&4)!==0,(s&8)!==0)}destroy(){this.renderer=null}}Ir.extension={type:F.RendererSystem,name:"mask"},U.add(Ir);class Ko{constructor(t){this.renderer=t,this.maskStack=[],this.glConst=0}getStackLength(){return this.maskStack.length}setMaskStack(t){const{gl:e}=this.renderer,s=this.getStackLength();this.maskStack=t;const r=this.getStackLength();r!==s&&(r===0?e.disable(this.glConst):(e.enable(this.glConst),this._useCurrent()))}_useCurrent(){}destroy(){this.renderer=null,this.maskStack=null}}const Zo=new Z,Qo=[],ls=class extends Ko{constructor(i){super(i),this.glConst=P.ADAPTER.getWebGLRenderingContext().SCISSOR_TEST}getStackLength(){const i=this.maskStack[this.maskStack.length-1];return i?i._scissorCounter:0}calcScissorRect(i){var o;if(i._scissorRectLocal)return;const t=i._scissorRect,{maskObject:e}=i,{renderer:s}=this,r=s.renderTexture,n=e.getBounds(!0,(o=Qo.pop())!=null?o:new j);this.roundFrameToPixels(n,r.current?r.current.resolution:s.resolution,r.sourceFrame,r.destinationFrame,s.projection.transform),t&&n.fit(t),i._scissorRectLocal=n}static isMatrixRotated(i){if(!i)return!1;const{a:t,b:e,c:s,d:r}=i;return(Math.abs(e)>1e-4||Math.abs(s)>1e-4)&&(Math.abs(t)>1e-4||Math.abs(r)>1e-4)}testScissor(i){const{maskObject:t}=i;if(!t.isFastRect||!t.isFastRect()||ls.isMatrixRotated(t.worldTransform)||ls.isMatrixRotated(this.renderer.projection.transform))return!1;this.calcScissorRect(i);const e=i._scissorRectLocal;return e.width>0&&e.height>0}roundFrameToPixels(i,t,e,s,r){ls.isMatrixRotated(r)||(r=r?Zo.copyFrom(r):Zo.identity(),r.translate(-e.x,-e.y).scale(s.width/e.width,s.height/e.height).translate(s.x,s.y),this.renderer.filter.transformAABB(r,i),i.fit(s),i.x=Math.round(i.x*t),i.y=Math.round(i.y*t),i.width=Math.round(i.width*t),i.height=Math.round(i.height*t))}push(i){i._scissorRectLocal||this.calcScissorRect(i);const{gl:t}=this.renderer;i._scissorRect||t.enable(t.SCISSOR_TEST),i._scissorCounter++,i._scissorRect=i._scissorRectLocal,this._useCurrent()}pop(i){const{gl:t}=this.renderer;i&&Qo.push(i._scissorRectLocal),this.getStackLength()>0?this._useCurrent():t.disable(t.SCISSOR_TEST)}_useCurrent(){const i=this.maskStack[this.maskStack.length-1]._scissorRect;let t;this.renderer.renderTexture.current?t=i.y:t=this.renderer.height-i.height-i.y,this.renderer.gl.scissor(i.x,t,i.width,i.height)}};let Pr=ls;Pr.extension={type:F.RendererSystem,name:"scissor"},U.add(Pr);class Mr extends Ko{constructor(t){super(t),this.glConst=P.ADAPTER.getWebGLRenderingContext().STENCIL_TEST}getStackLength(){const t=this.maskStack[this.maskStack.length-1];return t?t._stencilCounter:0}push(t){const e=t.maskObject,{gl:s}=this.renderer,r=t._stencilCounter;r===0&&(this.renderer.framebuffer.forceStencil(),s.clearStencil(0),s.clear(s.STENCIL_BUFFER_BIT),s.enable(s.STENCIL_TEST)),t._stencilCounter++;const n=t._colorMask;n!==0&&(t._colorMask=0,s.colorMask(!1,!1,!1,!1)),s.stencilFunc(s.EQUAL,r,4294967295),s.stencilOp(s.KEEP,s.KEEP,s.INCR),e.renderable=!0,e.render(this.renderer),this.renderer.batch.flush(),e.renderable=!1,n!==0&&(t._colorMask=n,s.colorMask((n&1)!==0,(n&2)!==0,(n&4)!==0,(n&8)!==0)),this._useCurrent()}pop(t){const e=this.renderer.gl;if(this.getStackLength()===0)e.disable(e.STENCIL_TEST);else{const s=this.maskStack.length!==0?this.maskStack[this.maskStack.length-1]:null,r=s?s._colorMask:15;r!==0&&(s._colorMask=0,e.colorMask(!1,!1,!1,!1)),e.stencilOp(e.KEEP,e.KEEP,e.DECR),t.renderable=!0,t.render(this.renderer),this.renderer.batch.flush(),t.renderable=!1,r!==0&&(s._colorMask=r,e.colorMask((r&1)!==0,(r&2)!==0,(r&4)!==0,(r&8)!==0)),this._useCurrent()}}_useCurrent(){const t=this.renderer.gl;t.stencilFunc(t.EQUAL,this.getStackLength(),4294967295),t.stencilOp(t.KEEP,t.KEEP,t.KEEP)}}Mr.extension={type:F.RendererSystem,name:"stencil"},U.add(Mr);class Br{constructor(t){this.renderer=t,this.destinationFrame=null,this.sourceFrame=null,this.defaultFrame=null,this.projectionMatrix=new Z,this.transform=null}update(t,e,s,r){this.destinationFrame=t||this.destinationFrame||this.defaultFrame,this.sourceFrame=e||this.sourceFrame||t,this.calculateProjection(this.destinationFrame,this.sourceFrame,s,r),this.transform&&this.projectionMatrix.append(this.transform);const n=this.renderer;n.globalUniforms.uniforms.projectionMatrix=this.projectionMatrix,n.globalUniforms.update(),n.shader.shader&&n.shader.syncUniformGroup(n.shader.shader.uniforms.globals)}calculateProjection(t,e,s,r){const n=this.projectionMatrix,o=r?-1:1;n.identity(),n.a=1/e.width*2,n.d=o*(1/e.height*2),n.tx=-1-e.x*n.a,n.ty=-o-e.y*n.d}setTransform(t){}destroy(){this.renderer=null}}Br.extension={type:F.RendererSystem,name:"projection"},U.add(Br);const Pe=new j,li=new j;class Dr{constructor(t){this.renderer=t,this.defaultMaskStack=[],this.current=null,this.sourceFrame=new j,this.destinationFrame=new j,this.viewportFrame=new j}bind(t=null,e,s){const r=this.renderer;this.current=t;let n,o,a;t?(n=t.baseTexture,a=n.resolution,e||(Pe.width=t.frame.width,Pe.height=t.frame.height,e=Pe),s||(li.x=t.frame.x,li.y=t.frame.y,li.width=e.width,li.height=e.height,s=li),o=n.framebuffer):(a=r.resolution,e||(Pe.width=r._view.screen.width,Pe.height=r._view.screen.height,e=Pe),s||(s=Pe,s.width=e.width,s.height=e.height));const h=this.viewportFrame;h.x=s.x*a,h.y=s.y*a,h.width=s.width*a,h.height=s.height*a,t||(h.y=r.view.height-(h.y+h.height)),h.ceil(),this.renderer.framebuffer.bind(o,h),this.renderer.projection.update(s,e,a,!o),t?this.renderer.mask.setMaskStack(n.maskStack):this.renderer.mask.setMaskStack(this.defaultMaskStack),this.sourceFrame.copyFrom(e),this.destinationFrame.copyFrom(s)}clear(t,e){this.current?t=t||this.current.baseTexture.clearColor:t=t||this.renderer.background.colorRgba;const s=this.destinationFrame,r=this.current?this.current.baseTexture:this.renderer._view.screen,n=s.width!==r.width||s.height!==r.height;if(n){let{x:o,y:a,width:h,height:l}=this.viewportFrame;o=Math.round(o),a=Math.round(a),h=Math.round(h),l=Math.round(l),this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST),this.renderer.gl.scissor(o,a,h,l)}this.renderer.framebuffer.clear(t[0],t[1],t[2],t[3],e),n&&this.renderer.scissor.pop()}resize(){this.bind(null)}reset(){this.bind(null)}destroy(){this.renderer=null}}Dr.extension={type:F.RendererSystem,name:"renderTexture"},U.add(Dr);function Hd(i,t,e,s,r){e.buffer.update(r)}const Xd={float:`
        data[offset] = v;
    `,vec2:`
        data[offset] = v[0];
        data[offset+1] = v[1];
    `,vec3:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];

    `,vec4:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];
        data[offset+3] = v[3];
    `,mat2:`
        data[offset] = v[0];
        data[offset+1] = v[1];

        data[offset+4] = v[2];
        data[offset+5] = v[3];
    `,mat3:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];

        data[offset + 4] = v[3];
        data[offset + 5] = v[4];
        data[offset + 6] = v[5];

        data[offset + 8] = v[6];
        data[offset + 9] = v[7];
        data[offset + 10] = v[8];
    `,mat4:`
        for(var i = 0; i < 16; i++)
        {
            data[offset + i] = v[i];
        }
    `},Jo={float:4,vec2:8,vec3:12,vec4:16,int:4,ivec2:8,ivec3:12,ivec4:16,uint:4,uvec2:8,uvec3:12,uvec4:16,bool:4,bvec2:8,bvec3:12,bvec4:16,mat2:16*2,mat3:16*3,mat4:16*4};function ta(i){const t=i.map(n=>({data:n,offset:0,dataLen:0,dirty:0}));let e=0,s=0,r=0;for(let n=0;n<t.length;n++){const o=t[n];if(e=Jo[o.data.type],o.data.size>1&&(e=Math.max(e,16)*o.data.size),o.dataLen=e,s%e!==0&&s<16){const a=s%e%16;s+=a,r+=a}s+e>16?(r=Math.ceil(r/16)*16,o.offset=r,r+=e,s=e):(o.offset=r,s+=e,r+=e)}return r=Math.ceil(r/16)*16,{uboElements:t,size:r}}function ea(i,t){const e=[];for(const s in i)t[s]&&e.push(t[s]);return e.sort((s,r)=>s.index-r.index),e}function ia(i,t){if(!i.autoManage)return{size:0,syncFunc:Hd};const e=ea(i.uniforms,t),{uboElements:s,size:r}=ta(e),n=[`
    var v = null;
    var v2 = null;
    var cv = null;
    var t = 0;
    var gl = renderer.gl
    var index = 0;
    var data = buffer.data;
    `];for(let o=0;o<s.length;o++){const a=s[o],h=i.uniforms[a.data.name],l=a.data.name;let c=!1;for(let u=0;u<Ie.length;u++){const d=Ie[u];if(d.codeUbo&&d.test(a.data,h)){n.push(`offset = ${a.offset/4};`,Ie[u].codeUbo(a.data.name,h)),c=!0;break}}if(!c)if(a.data.size>1){const u=zo(a.data.type),d=Math.max(Jo[a.data.type]/16,1),f=u/d,p=(4-f%4)%4;n.push(`
                cv = ud.${l}.value;
                v = uv.${l};
                offset = ${a.offset/4};

                t = 0;

                for(var i=0; i < ${a.data.size*d}; i++)
                {
                    for(var j = 0; j < ${f}; j++)
                    {
                        data[offset++] = v[t++];
                    }
                    offset += ${p};
                }

                `)}else{const u=Xd[a.data.type];n.push(`
                cv = ud.${l}.value;
                v = uv.${l};
                offset = ${a.offset/4};
                ${u};
                `)}}return n.push(`
       renderer.buffer.update(buffer);
    `),{size:r,syncFunc:new Function("ud","uv","renderer","syncData","buffer",n.join(`
`))}}class Vd{}class sa{constructor(t,e){this.program=t,this.uniformData=e,this.uniformGroups={},this.uniformDirtyGroups={},this.uniformBufferBindings={}}destroy(){this.uniformData=null,this.uniformGroups=null,this.uniformDirtyGroups=null,this.uniformBufferBindings=null,this.program=null}}function zd(i,t){const e={},s=t.getProgramParameter(i,t.ACTIVE_ATTRIBUTES);for(let r=0;r<s;r++){const n=t.getActiveAttrib(i,r);if(n.name.startsWith("gl_"))continue;const o=jo(t,n.type),a={type:o,name:n.name,size:zo(o),location:t.getAttribLocation(i,n.name)};e[n.name]=a}return e}function Wd(i,t){const e={},s=t.getProgramParameter(i,t.ACTIVE_UNIFORMS);for(let r=0;r<s;r++){const n=t.getActiveUniform(i,r),o=n.name.replace(/\[.*?\]$/,""),a=!!n.name.match(/\[.*?\]$/),h=jo(t,n.type);e[o]={name:o,index:r,type:h,size:n.size,isArray:a,value:Go(h,n.size)}}return e}function ra(i,t){var h;const e=Uo(i,i.VERTEX_SHADER,t.vertexSrc),s=Uo(i,i.FRAGMENT_SHADER,t.fragmentSrc),r=i.createProgram();i.attachShader(r,e),i.attachShader(r,s);const n=(h=t.extra)==null?void 0:h.transformFeedbackVaryings;if(n&&(typeof i.transformFeedbackVaryings!="function"?console.warn("TransformFeedback is not supported but TransformFeedbackVaryings are given."):i.transformFeedbackVaryings(r,n.names,n.bufferMode==="separate"?i.SEPARATE_ATTRIBS:i.INTERLEAVED_ATTRIBS)),i.linkProgram(r),i.getProgramParameter(r,i.LINK_STATUS)||Sd(i,r,e,s),t.attributeData=zd(r,i),t.uniformData=Wd(r,i),!/^[ \t]*#[ \t]*version[ \t]+300[ \t]+es[ \t]*$/m.test(t.vertexSrc)){const l=Object.keys(t.attributeData);l.sort((c,u)=>c>u?1:-1);for(let c=0;c<l.length;c++)t.attributeData[l[c]].location=c,i.bindAttribLocation(r,c,l[c]);i.linkProgram(r)}i.deleteShader(e),i.deleteShader(s);const o={};for(const l in t.uniformData){const c=t.uniformData[l];o[l]={location:i.getUniformLocation(r,l),value:Go(c.type,c.size)}}return new sa(r,o)}let jd=0;const cs={textureCount:0,uboCount:0};class Nr{constructor(t){this.destroyed=!1,this.renderer=t,this.systemCheck(),this.gl=null,this.shader=null,this.program=null,this.cache={},this._uboCache={},this.id=jd++}systemCheck(){if(!Dd())throw new Error("Current environment does not allow unsafe-eval, please use @pixi/unsafe-eval module to enable support.")}contextChange(t){this.gl=t,this.reset()}bind(t,e){t.disposeRunner.add(this),t.uniforms.globals=this.renderer.globalUniforms;const s=t.program,r=s.glPrograms[this.renderer.CONTEXT_UID]||this.generateProgram(t);return this.shader=t,this.program!==s&&(this.program=s,this.gl.useProgram(r.program)),e||(cs.textureCount=0,cs.uboCount=0,this.syncUniformGroup(t.uniformGroup,cs)),r}setUniforms(t){const e=this.shader.program,s=e.glPrograms[this.renderer.CONTEXT_UID];e.syncUniforms(s.uniformData,t,this.renderer)}syncUniformGroup(t,e){const s=this.getGlProgram();(!t.static||t.dirtyId!==s.uniformDirtyGroups[t.id])&&(s.uniformDirtyGroups[t.id]=t.dirtyId,this.syncUniforms(t,s,e))}syncUniforms(t,e,s){(t.syncUniforms[this.shader.program.id]||this.createSyncGroups(t))(e.uniformData,t.uniforms,this.renderer,s)}createSyncGroups(t){const e=this.getSignature(t,this.shader.program.uniformData,"u");return this.cache[e]||(this.cache[e]=Pd(t,this.shader.program.uniformData)),t.syncUniforms[this.shader.program.id]=this.cache[e],t.syncUniforms[this.shader.program.id]}syncUniformBufferGroup(t,e){const s=this.getGlProgram();if(!t.static||t.dirtyId!==0||!s.uniformGroups[t.id]){t.dirtyId=0;const r=s.uniformGroups[t.id]||this.createSyncBufferGroup(t,s,e);t.buffer.update(),r(s.uniformData,t.uniforms,this.renderer,cs,t.buffer)}this.renderer.buffer.bindBufferBase(t.buffer,s.uniformBufferBindings[e])}createSyncBufferGroup(t,e,s){const{gl:r}=this.renderer;this.renderer.buffer.bind(t.buffer);const n=this.gl.getUniformBlockIndex(e.program,s);e.uniformBufferBindings[s]=this.shader.uniformBindCount,r.uniformBlockBinding(e.program,n,this.shader.uniformBindCount),this.shader.uniformBindCount++;const o=this.getSignature(t,this.shader.program.uniformData,"ubo");let a=this._uboCache[o];if(a||(a=this._uboCache[o]=ia(t,this.shader.program.uniformData)),t.autoManage){const h=new Float32Array(a.size/4);t.buffer.update(h)}return e.uniformGroups[t.id]=a.syncFunc,e.uniformGroups[t.id]}getSignature(t,e,s){const r=t.uniforms,n=[`${s}-`];for(const o in r)n.push(o),e[o]&&n.push(e[o].type);return n.join("-")}getGlProgram(){return this.shader?this.shader.program.glPrograms[this.renderer.CONTEXT_UID]:null}generateProgram(t){const e=this.gl,s=t.program,r=ra(e,s);return s.glPrograms[this.renderer.CONTEXT_UID]=r,r}reset(){this.program=null,this.shader=null}disposeShader(t){this.shader===t&&(this.shader=null)}destroy(){this.renderer=null,this.destroyed=!0}}Nr.extension={type:F.RendererSystem,name:"shader"},U.add(Nr);function Yd(i,t=[]){return t[C.NORMAL]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.ADD]=[i.ONE,i.ONE],t[C.MULTIPLY]=[i.DST_COLOR,i.ONE_MINUS_SRC_ALPHA,i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.SCREEN]=[i.ONE,i.ONE_MINUS_SRC_COLOR,i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.OVERLAY]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.DARKEN]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.LIGHTEN]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.COLOR_DODGE]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.COLOR_BURN]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.HARD_LIGHT]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.SOFT_LIGHT]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.DIFFERENCE]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.EXCLUSION]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.HUE]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.SATURATION]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.COLOR]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.LUMINOSITY]=[i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.NONE]=[0,0],t[C.NORMAL_NPM]=[i.SRC_ALPHA,i.ONE_MINUS_SRC_ALPHA,i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.ADD_NPM]=[i.SRC_ALPHA,i.ONE,i.ONE,i.ONE],t[C.SCREEN_NPM]=[i.SRC_ALPHA,i.ONE_MINUS_SRC_COLOR,i.ONE,i.ONE_MINUS_SRC_ALPHA],t[C.SRC_IN]=[i.DST_ALPHA,i.ZERO],t[C.SRC_OUT]=[i.ONE_MINUS_DST_ALPHA,i.ZERO],t[C.SRC_ATOP]=[i.DST_ALPHA,i.ONE_MINUS_SRC_ALPHA],t[C.DST_OVER]=[i.ONE_MINUS_DST_ALPHA,i.ONE],t[C.DST_IN]=[i.ZERO,i.SRC_ALPHA],t[C.DST_OUT]=[i.ZERO,i.ONE_MINUS_SRC_ALPHA],t[C.DST_ATOP]=[i.ONE_MINUS_DST_ALPHA,i.SRC_ALPHA],t[C.XOR]=[i.ONE_MINUS_DST_ALPHA,i.ONE_MINUS_SRC_ALPHA],t[C.SUBTRACT]=[i.ONE,i.ONE,i.ONE,i.ONE,i.FUNC_REVERSE_SUBTRACT,i.FUNC_ADD],t}const $d=0,qd=1,Kd=2,Zd=3,Qd=4,Jd=5,Fr=class{constructor(){this.gl=null,this.stateId=0,this.polygonOffset=0,this.blendMode=C.NONE,this._blendEq=!1,this.map=[],this.map[$d]=this.setBlend,this.map[qd]=this.setOffset,this.map[Kd]=this.setCullFace,this.map[Zd]=this.setDepthTest,this.map[Qd]=this.setFrontFace,this.map[Jd]=this.setDepthMask,this.checks=[],this.defaultState=new Jt,this.defaultState.blend=!0}contextChange(i){this.gl=i,this.blendModes=Yd(i),this.set(this.defaultState),this.reset()}set(i){if(i=i||this.defaultState,this.stateId!==i.data){let t=this.stateId^i.data,e=0;for(;t;)t&1&&this.map[e].call(this,!!(i.data&1<<e)),t=t>>1,e++;this.stateId=i.data}for(let t=0;t<this.checks.length;t++)this.checks[t](this,i)}forceState(i){i=i||this.defaultState;for(let t=0;t<this.map.length;t++)this.map[t].call(this,!!(i.data&1<<t));for(let t=0;t<this.checks.length;t++)this.checks[t](this,i);this.stateId=i.data}setBlend(i){this.updateCheck(Fr.checkBlendMode,i),this.gl[i?"enable":"disable"](this.gl.BLEND)}setOffset(i){this.updateCheck(Fr.checkPolygonOffset,i),this.gl[i?"enable":"disable"](this.gl.POLYGON_OFFSET_FILL)}setDepthTest(i){this.gl[i?"enable":"disable"](this.gl.DEPTH_TEST)}setDepthMask(i){this.gl.depthMask(i)}setCullFace(i){this.gl[i?"enable":"disable"](this.gl.CULL_FACE)}setFrontFace(i){this.gl.frontFace(this.gl[i?"CW":"CCW"])}setBlendMode(i){if(i===this.blendMode)return;this.blendMode=i;const t=this.blendModes[i],e=this.gl;t.length===2?e.blendFunc(t[0],t[1]):e.blendFuncSeparate(t[0],t[1],t[2],t[3]),t.length===6?(this._blendEq=!0,e.blendEquationSeparate(t[4],t[5])):this._blendEq&&(this._blendEq=!1,e.blendEquationSeparate(e.FUNC_ADD,e.FUNC_ADD))}setPolygonOffset(i,t){this.gl.polygonOffset(i,t)}reset(){this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,!1),this.forceState(this.defaultState),this._blendEq=!0,this.blendMode=-1,this.setBlendMode(0)}updateCheck(i,t){const e=this.checks.indexOf(i);t&&e===-1?this.checks.push(i):!t&&e!==-1&&this.checks.splice(e,1)}static checkBlendMode(i,t){i.setBlendMode(t.blendMode)}static checkPolygonOffset(i,t){i.setPolygonOffset(1,t.polygonOffset)}destroy(){this.gl=null}};let Or=Fr;Or.extension={type:F.RendererSystem,name:"state"},U.add(Or);class Lr{constructor(t){this.renderer=t,this.count=0,this.checkCount=0,this.maxIdle=P.GC_MAX_IDLE,this.checkCountMax=P.GC_MAX_CHECK_COUNT,this.mode=P.GC_MODE}postrender(){!this.renderer.objectRenderer.renderingToScreen||(this.count++,this.mode!==Oi.MANUAL&&(this.checkCount++,this.checkCount>this.checkCountMax&&(this.checkCount=0,this.run())))}run(){const t=this.renderer.texture,e=t.managedTextures;let s=!1;for(let r=0;r<e.length;r++){const n=e[r];!n.framebuffer&&this.count-n.touched>this.maxIdle&&(t.destroyTexture(n,!0),e[r]=null,s=!0)}if(s){let r=0;for(let n=0;n<e.length;n++)e[n]!==null&&(e[r++]=e[n]);e.length=r}}unload(t){const e=this.renderer.texture,s=t._texture;s&&!s.framebuffer&&e.destroyTexture(s);for(let r=t.children.length-1;r>=0;r--)this.unload(t.children[r])}destroy(){this.renderer=null}}Lr.extension={type:F.RendererSystem,name:"textureGC"},U.add(Lr);function tf(i){let t;return"WebGL2RenderingContext"in globalThis&&i instanceof globalThis.WebGL2RenderingContext?t={[H.UNSIGNED_BYTE]:{[N.RGBA]:i.RGBA8,[N.RGB]:i.RGB8,[N.RG]:i.RG8,[N.RED]:i.R8,[N.RGBA_INTEGER]:i.RGBA8UI,[N.RGB_INTEGER]:i.RGB8UI,[N.RG_INTEGER]:i.RG8UI,[N.RED_INTEGER]:i.R8UI,[N.ALPHA]:i.ALPHA,[N.LUMINANCE]:i.LUMINANCE,[N.LUMINANCE_ALPHA]:i.LUMINANCE_ALPHA},[H.BYTE]:{[N.RGBA]:i.RGBA8_SNORM,[N.RGB]:i.RGB8_SNORM,[N.RG]:i.RG8_SNORM,[N.RED]:i.R8_SNORM,[N.RGBA_INTEGER]:i.RGBA8I,[N.RGB_INTEGER]:i.RGB8I,[N.RG_INTEGER]:i.RG8I,[N.RED_INTEGER]:i.R8I},[H.UNSIGNED_SHORT]:{[N.RGBA_INTEGER]:i.RGBA16UI,[N.RGB_INTEGER]:i.RGB16UI,[N.RG_INTEGER]:i.RG16UI,[N.RED_INTEGER]:i.R16UI,[N.DEPTH_COMPONENT]:i.DEPTH_COMPONENT16},[H.SHORT]:{[N.RGBA_INTEGER]:i.RGBA16I,[N.RGB_INTEGER]:i.RGB16I,[N.RG_INTEGER]:i.RG16I,[N.RED_INTEGER]:i.R16I},[H.UNSIGNED_INT]:{[N.RGBA_INTEGER]:i.RGBA32UI,[N.RGB_INTEGER]:i.RGB32UI,[N.RG_INTEGER]:i.RG32UI,[N.RED_INTEGER]:i.R32UI,[N.DEPTH_COMPONENT]:i.DEPTH_COMPONENT24},[H.INT]:{[N.RGBA_INTEGER]:i.RGBA32I,[N.RGB_INTEGER]:i.RGB32I,[N.RG_INTEGER]:i.RG32I,[N.RED_INTEGER]:i.R32I},[H.FLOAT]:{[N.RGBA]:i.RGBA32F,[N.RGB]:i.RGB32F,[N.RG]:i.RG32F,[N.RED]:i.R32F,[N.DEPTH_COMPONENT]:i.DEPTH_COMPONENT32F},[H.HALF_FLOAT]:{[N.RGBA]:i.RGBA16F,[N.RGB]:i.RGB16F,[N.RG]:i.RG16F,[N.RED]:i.R16F},[H.UNSIGNED_SHORT_5_6_5]:{[N.RGB]:i.RGB565},[H.UNSIGNED_SHORT_4_4_4_4]:{[N.RGBA]:i.RGBA4},[H.UNSIGNED_SHORT_5_5_5_1]:{[N.RGBA]:i.RGB5_A1},[H.UNSIGNED_INT_2_10_10_10_REV]:{[N.RGBA]:i.RGB10_A2,[N.RGBA_INTEGER]:i.RGB10_A2UI},[H.UNSIGNED_INT_10F_11F_11F_REV]:{[N.RGB]:i.R11F_G11F_B10F},[H.UNSIGNED_INT_5_9_9_9_REV]:{[N.RGB]:i.RGB9_E5},[H.UNSIGNED_INT_24_8]:{[N.DEPTH_STENCIL]:i.DEPTH24_STENCIL8},[H.FLOAT_32_UNSIGNED_INT_24_8_REV]:{[N.DEPTH_STENCIL]:i.DEPTH32F_STENCIL8}}:t={[H.UNSIGNED_BYTE]:{[N.RGBA]:i.RGBA,[N.RGB]:i.RGB,[N.ALPHA]:i.ALPHA,[N.LUMINANCE]:i.LUMINANCE,[N.LUMINANCE_ALPHA]:i.LUMINANCE_ALPHA},[H.UNSIGNED_SHORT_5_6_5]:{[N.RGB]:i.RGB},[H.UNSIGNED_SHORT_4_4_4_4]:{[N.RGBA]:i.RGBA},[H.UNSIGNED_SHORT_5_5_5_1]:{[N.RGBA]:i.RGBA}},t}class us{constructor(t){this.texture=t,this.width=-1,this.height=-1,this.dirtyId=-1,this.dirtyStyleId=-1,this.mipmap=!1,this.wrapMode=33071,this.type=H.UNSIGNED_BYTE,this.internalFormat=N.RGBA,this.samplerType=0}}class Ur{constructor(t){this.renderer=t,this.boundTextures=[],this.currentLocation=-1,this.managedTextures=[],this._unknownBoundTextures=!1,this.unknownTexture=new $,this.hasIntegerTextures=!1}contextChange(){const t=this.gl=this.renderer.gl;this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.webGLVersion=this.renderer.context.webGLVersion,this.internalFormats=tf(t);const e=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);this.boundTextures.length=e;for(let r=0;r<e;r++)this.boundTextures[r]=null;this.emptyTextures={};const s=new us(t.createTexture());t.bindTexture(t.TEXTURE_2D,s.texture),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,1,1,0,t.RGBA,t.UNSIGNED_BYTE,new Uint8Array(4)),this.emptyTextures[t.TEXTURE_2D]=s,this.emptyTextures[t.TEXTURE_CUBE_MAP]=new us(t.createTexture()),t.bindTexture(t.TEXTURE_CUBE_MAP,this.emptyTextures[t.TEXTURE_CUBE_MAP].texture);for(let r=0;r<6;r++)t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+r,0,t.RGBA,1,1,0,t.RGBA,t.UNSIGNED_BYTE,null);t.texParameteri(t.TEXTURE_CUBE_MAP,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_CUBE_MAP,t.TEXTURE_MIN_FILTER,t.LINEAR);for(let r=0;r<this.boundTextures.length;r++)this.bind(null,r)}bind(t,e=0){const{gl:s}=this;if(t=t==null?void 0:t.castToBaseTexture(),(t==null?void 0:t.valid)&&!t.parentTextureArray){t.touched=this.renderer.textureGC.count;const r=t._glTextures[this.CONTEXT_UID]||this.initTexture(t);this.boundTextures[e]!==t&&(this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),s.bindTexture(t.target,r.texture)),r.dirtyId!==t.dirtyId?(this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),this.updateTexture(t)):r.dirtyStyleId!==t.dirtyStyleId&&this.updateTextureStyle(t),this.boundTextures[e]=t}else this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),s.bindTexture(s.TEXTURE_2D,this.emptyTextures[s.TEXTURE_2D].texture),this.boundTextures[e]=null}reset(){this._unknownBoundTextures=!0,this.hasIntegerTextures=!1,this.currentLocation=-1;for(let t=0;t<this.boundTextures.length;t++)this.boundTextures[t]=this.unknownTexture}unbind(t){const{gl:e,boundTextures:s}=this;if(this._unknownBoundTextures){this._unknownBoundTextures=!1;for(let r=0;r<s.length;r++)s[r]===this.unknownTexture&&this.bind(null,r)}for(let r=0;r<s.length;r++)s[r]===t&&(this.currentLocation!==r&&(e.activeTexture(e.TEXTURE0+r),this.currentLocation=r),e.bindTexture(t.target,this.emptyTextures[t.target].texture),s[r]=null)}ensureSamplerType(t){const{boundTextures:e,hasIntegerTextures:s,CONTEXT_UID:r}=this;if(!!s)for(let n=t-1;n>=0;--n){const o=e[n];o&&o._glTextures[r].samplerType!==Fi.FLOAT&&this.renderer.texture.unbind(o)}}initTexture(t){const e=new us(this.gl.createTexture());return e.dirtyId=-1,t._glTextures[this.CONTEXT_UID]=e,this.managedTextures.push(t),t.on("dispose",this.destroyTexture,this),e}initTextureType(t,e){var s,r;e.internalFormat=(r=(s=this.internalFormats[t.type])==null?void 0:s[t.format])!=null?r:t.format,this.webGLVersion===2&&t.type===H.HALF_FLOAT?e.type=this.gl.HALF_FLOAT:e.type=t.type}updateTexture(t){var r;const e=t._glTextures[this.CONTEXT_UID];if(!e)return;const s=this.renderer;if(this.initTextureType(t,e),(r=t.resource)!=null&&r.upload(s,t,e))e.samplerType!==Fi.FLOAT&&(this.hasIntegerTextures=!0);else{const n=t.realWidth,o=t.realHeight,a=s.gl;(e.width!==n||e.height!==o||e.dirtyId<0)&&(e.width=n,e.height=o,a.texImage2D(t.target,0,e.internalFormat,n,o,0,t.format,e.type,null))}t.dirtyStyleId!==e.dirtyStyleId&&this.updateTextureStyle(t),e.dirtyId=t.dirtyId}destroyTexture(t,e){const{gl:s}=this;if(t=t.castToBaseTexture(),t._glTextures[this.CONTEXT_UID]&&(this.unbind(t),s.deleteTexture(t._glTextures[this.CONTEXT_UID].texture),t.off("dispose",this.destroyTexture,this),delete t._glTextures[this.CONTEXT_UID],!e)){const r=this.managedTextures.indexOf(t);r!==-1&&Ae(this.managedTextures,r,1)}}updateTextureStyle(t){var s;const e=t._glTextures[this.CONTEXT_UID];!e||((t.mipmap===Gt.POW2||this.webGLVersion!==2)&&!t.isPowerOfTwo?e.mipmap=!1:e.mipmap=t.mipmap>=1,this.webGLVersion!==2&&!t.isPowerOfTwo?e.wrapMode=Kt.CLAMP:e.wrapMode=t.wrapMode,(s=t.resource)!=null&&s.style(this.renderer,t,e)||this.setStyle(t,e),e.dirtyStyleId=t.dirtyStyleId)}setStyle(t,e){const s=this.gl;if(e.mipmap&&t.mipmap!==Gt.ON_MANUAL&&s.generateMipmap(t.target),s.texParameteri(t.target,s.TEXTURE_WRAP_S,e.wrapMode),s.texParameteri(t.target,s.TEXTURE_WRAP_T,e.wrapMode),e.mipmap){s.texParameteri(t.target,s.TEXTURE_MIN_FILTER,t.scaleMode===Dt.LINEAR?s.LINEAR_MIPMAP_LINEAR:s.NEAREST_MIPMAP_NEAREST);const r=this.renderer.context.extensions.anisotropicFiltering;if(r&&t.anisotropicLevel>0&&t.scaleMode===Dt.LINEAR){const n=Math.min(t.anisotropicLevel,s.getParameter(r.MAX_TEXTURE_MAX_ANISOTROPY_EXT));s.texParameterf(t.target,r.TEXTURE_MAX_ANISOTROPY_EXT,n)}}else s.texParameteri(t.target,s.TEXTURE_MIN_FILTER,t.scaleMode===Dt.LINEAR?s.LINEAR:s.NEAREST);s.texParameteri(t.target,s.TEXTURE_MAG_FILTER,t.scaleMode===Dt.LINEAR?s.LINEAR:s.NEAREST)}destroy(){this.renderer=null}}Ur.extension={type:F.RendererSystem,name:"texture"},U.add(Ur);const ef=new Ze;class kr{constructor(t){this.renderer=t,this._tempMatrix=new Z}generateTexture(t,e){const h=e||{},{region:s}=h,r=Xn(h,["region"]),n=s||t.getLocalBounds(null,!0);n.width===0&&(n.width=1),n.height===0&&(n.height=1);const o=Ot.create(re({width:n.width,height:n.height},r));this._tempMatrix.tx=-n.x,this._tempMatrix.ty=-n.y;const a=t.transform;return t.transform=ef,this.renderer.render(t,{renderTexture:o,transform:this._tempMatrix,skipUpdateTransform:!!t.parent,blit:!0}),t.transform=a,o}destroy(){}}kr.extension={type:[F.RendererSystem,F.CanvasRendererSystem],name:"textureGenerator"},U.add(kr);class Gr{constructor(){this.clearBeforeRender=!0,this._backgroundColor=0,this._backgroundColorRgba=[0,0,0,1],this._backgroundColorString="#000000",this.color=this._backgroundColor,this.alpha=1}init(t){this.clearBeforeRender=t.clearBeforeRender,t.color&&(this.color=typeof t.color=="string"?$i(t.color):t.color),this.alpha=t.alpha}get color(){return this._backgroundColor}set color(t){this._backgroundColor=t,this._backgroundColorString=Yi(t),ae(t,this._backgroundColorRgba)}get alpha(){return this._backgroundColorRgba[3]}set alpha(t){this._backgroundColorRgba[3]=t}get colorRgba(){return this._backgroundColorRgba}get colorString(){return this._backgroundColorString}destroy(){}}Gr.extension={type:[F.RendererSystem,F.CanvasRendererSystem],name:"background"},U.add(Gr);class Hr{constructor(t){this.renderer=t}init(t){this.screen=new j(0,0,t.width,t.height),this.element=t.view||P.ADAPTER.createCanvas(),this.resolution=t.resolution||P.RESOLUTION,this.autoDensity=!!t.autoDensity}resizeView(t,e){this.element.width=Math.round(t*this.resolution),this.element.height=Math.round(e*this.resolution);const s=this.element.width/this.resolution,r=this.element.height/this.resolution;this.screen.width=s,this.screen.height=r,this.autoDensity&&(this.element.style.width=`${s}px`,this.element.style.height=`${r}px`),this.renderer.emit("resize",s,r),this.renderer.runners.resize.emit(this.screen.width,this.screen.height)}destroy(t){var e;t&&((e=this.element.parentNode)==null||e.removeChild(this.element)),this.renderer=null,this.element=null,this.screen=null}}Hr.extension={type:[F.RendererSystem,F.CanvasRendererSystem],name:"_view"},U.add(Hr);class Xr{constructor(t){this.renderer=t,this.plugins={},Object.defineProperties(this.plugins,{extract:{enumerable:!1,get(){return bt("7.0.0","renderer.plugins.extract has moved to renderer.extract"),t.extract}},prepare:{enumerable:!1,get(){return bt("7.0.0","renderer.plugins.prepare has moved to renderer.prepare"),t.prepare}},interaction:{enumerable:!1,get(){return bt("7.0.0","renderer.plugins.interaction has been deprecated, use renderer.events"),t.events}}})}init(t){for(const e in t)this.plugins[e]=new t[e](this.renderer)}destroy(){for(const t in this.plugins)this.plugins[t].destroy(),this.plugins[t]=null}}Xr.extension={type:[F.RendererSystem,F.CanvasRendererSystem],name:"_plugin"},U.add(Xr);class Vr extends Le{constructor(){super(...arguments),this.runners={},this._systemsHash={}}setup(t){var r;this.addRunners(...t.runners);const e=((r=t.priority)!=null?r:[]).filter(n=>t.systems[n]),s=[...e,...Object.keys(t.systems).filter(n=>!e.includes(n))];for(const n of s)this.addSystem(t.systems[n],n)}addRunners(...t){t.forEach(e=>{this.runners[e]=new It(e)})}addSystem(t,e){const s=new t(this);if(this[e])throw new Error(`Whoops! The name "${e}" is already in use`);this[e]=s,this._systemsHash[e]=s;for(const r in this.runners)this.runners[r].add(s);return this}emitWithCustomOptions(t,e){const s=Object.keys(this._systemsHash);t.items.forEach(r=>{const n=s.find(o=>this._systemsHash[o]===r);r[t.name](e[n])})}destroy(){Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash={}}}class zr{constructor(t){this.renderer=t}run(t){const e=this.renderer;e.emitWithCustomOptions(e.runners.init,t),t.hello&&console.log(`PixiJS 7.0.5 - ${e.rendererLogId} - https://pixijs.com`),e.resize(this.renderer.screen.width,this.renderer.screen.height)}destroy(){}}zr.extension={type:[F.RendererSystem,F.CanvasRendererSystem],name:"startup"},U.add(zr);class Wr{constructor(t){this.renderer=t}contextChange(){this.gl=this.renderer.gl,this.CONTEXT_UID=this.renderer.CONTEXT_UID}bind(t){const{gl:e,CONTEXT_UID:s}=this,r=t._glTransformFeedbacks[s]||this.createGLTransformFeedback(t);e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,r)}unbind(){const{gl:t}=this;t.bindTransformFeedback(t.TRANSFORM_FEEDBACK,null)}beginTransformFeedback(t,e){const{gl:s,renderer:r}=this;e&&r.shader.bind(e),s.beginTransformFeedback(t)}endTransformFeedback(){const{gl:t}=this;t.endTransformFeedback()}createGLTransformFeedback(t){const{gl:e,renderer:s,CONTEXT_UID:r}=this,n=e.createTransformFeedback();t._glTransformFeedbacks[r]=n,e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,n);for(let o=0;o<t.buffers.length;o++){const a=t.buffers[o];!a||(s.buffer.update(a),a._glBuffers[r].refCount++,e.bindBufferBase(e.TRANSFORM_FEEDBACK_BUFFER,o,a._glBuffers[r].buffer||null))}return e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,null),t.disposeRunner.add(this),n}disposeTransformFeedback(t,e){const s=t._glTransformFeedbacks[this.CONTEXT_UID],r=this.gl;t.disposeRunner.remove(this);const n=this.renderer.buffer;if(n)for(let o=0;o<t.buffers.length;o++){const a=t.buffers[o];if(!a)continue;const h=a._glBuffers[this.CONTEXT_UID];h&&(h.refCount--,h.refCount===0&&!e&&n.dispose(a,e))}!s||(e||r.deleteTransformFeedback(s),delete t._glTransformFeedbacks[this.CONTEXT_UID])}destroy(){this.renderer=null}}Wr.extension={type:F.RendererSystem,name:"transformFeedback"},U.add(Wr);const na=[];U.handleByList(F.Renderer,na);function oa(i){for(const t of na)if(t.test(i))return new t(i);throw new Error("Unable to auto-detect a suitable renderer.")}var sf=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
    vTextureCoord = aTextureCoord;
}`,rf=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`;const aa=sf,jr=rf,Yr=class extends Vr{constructor(i){var s;super(),i=Object.assign({},P.RENDER_OPTIONS,i),this.gl=null,this.CONTEXT_UID=0,this.globalUniforms=new Lt({projectionMatrix:new Z},!0);const t={runners:["init","destroy","contextChange","resolutionChange","reset","update","postrender","prerender","resize"],systems:Yr.__systems,priority:["_view","textureGenerator","background","_plugin","startup","context","state","texture","buffer","geometry","framebuffer","transformFeedback","mask","scissor","stencil","projection","textureGC","filter","renderTexture","batch","objectRenderer","_multisample"]};this.setup(t),"useContextAlpha"in i&&(bt("7.0.0","options.useContextAlpha is deprecated, use options.premultipliedAlpha and options.backgroundAlpha instead"),i.premultipliedAlpha=i.useContextAlpha&&i.useContextAlpha!=="notMultiplied",i.backgroundAlpha=i.useContextAlpha===!1?1:i.backgroundAlpha);const e={hello:i.hello,_plugin:Yr.__plugins,background:{alpha:i.backgroundAlpha,color:(s=i.background)!=null?s:i.backgroundColor,clearBeforeRender:i.clearBeforeRender},_view:{height:i.height,width:i.width,autoDensity:i.autoDensity,resolution:i.resolution,view:i.view},context:{antialias:i.antialias,context:i.context,powerPreference:i.powerPreference,premultipliedAlpha:i.premultipliedAlpha,preserveDrawingBuffer:i.preserveDrawingBuffer}};this.startup.run(e)}static test(i){return i!=null&&i.forceCanvas?!1:To()}render(i,t){this.objectRenderer.render(i,t)}resize(i,t){this._view.resizeView(i,t)}reset(){return this.runners.reset.emit(),this}clear(){this.renderTexture.bind(),this.renderTexture.clear()}destroy(i=!1){this.runners.destroy.items.reverse(),this.emitWithCustomOptions(this.runners.destroy,{_view:i}),super.destroy()}get plugins(){return this._plugin.plugins}get multisample(){return this._multisample.multisample}get width(){return this._view.element.width}get height(){return this._view.element.height}get resolution(){return this._view.resolution}set resolution(i){this._view.resolution=i,this.runners.resolutionChange.emit(i)}get autoDensity(){return this._view.autoDensity}get view(){return this._view.element}get screen(){return this._view.screen}get lastObjectRendered(){return this.objectRenderer.lastObjectRendered}get renderingToScreen(){return this.objectRenderer.renderingToScreen}get rendererLogId(){return`WebGL ${this.context.webGLVersion}`}get clearBeforeRender(){return bt("7.0.0","renderer.clearBeforeRender has been deprecated, please use renderer.background.clearBeforeRender instead."),this.background.clearBeforeRender}get useContextAlpha(){return bt("7.0.0","renderer.useContextAlpha has been deprecated, please use renderer.context.premultipliedAlpha instead."),this.context.useContextAlpha}get preserveDrawingBuffer(){return bt("7.0.0","renderer.preserveDrawingBuffer has been deprecated, we cannot truly know this unless pixi created the context"),this.context.preserveDrawingBuffer}get backgroundColor(){return bt("7.0.0","renderer.backgroundColor has been deprecated, use renderer.background.color instead."),this.background.color}set backgroundColor(i){bt("7.0.0","renderer.backgroundColor has been deprecated, use renderer.background.color instead."),this.background.color=i}get backgroundAlpha(){return bt("7.0.0","renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead."),this.background.color}set backgroundAlpha(i){bt("7.0.0","renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead."),this.background.alpha=i}get powerPreference(){return bt("7.0.0","renderer.powerPreference has been deprecated, we can only know this if pixi creates the context"),this.context.powerPreference}generateTexture(i,t){return this.textureGenerator.generateTexture(i,t)}};let Me=Yr;Me.extension={type:F.Renderer,priority:1},Me.__plugins={},Me.__systems={},U.handleByMap(F.RendererPlugin,Me.__plugins),U.handleByMap(F.RendererSystem,Me.__systems),U.add(Me);class ds{constructor(){this.texArray=null,this.blend=0,this.type=Bt.TRIANGLES,this.start=0,this.size=0,this.data=null}}class fs{constructor(){this.elements=[],this.ids=[],this.count=0}clear(){for(let t=0;t<this.count;t++)this.elements[t]=null;this.count=0}}class ps{constructor(t){typeof t=="number"?this.rawBinaryData=new ArrayBuffer(t):t instanceof Uint8Array?this.rawBinaryData=t.buffer:this.rawBinaryData=t,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData)}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get uint16View(){return this._uint16View||(this._uint16View=new Uint16Array(this.rawBinaryData)),this._uint16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}view(t){return this[`${t}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this._uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(t){switch(t){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${t} isn't a valid view type`)}}}class ha{constructor(t,e){if(this.vertexSrc=t,this.fragTemplate=e,this.programCache={},this.defaultGroupCache={},!e.includes("%count%"))throw new Error('Fragment template must contain "%count%".');if(!e.includes("%forloop%"))throw new Error('Fragment template must contain "%forloop%".')}generateShader(t){if(!this.programCache[t]){const s=new Int32Array(t);for(let n=0;n<t;n++)s[n]=n;this.defaultGroupCache[t]=Lt.from({uSamplers:s},!0);let r=this.fragTemplate;r=r.replace(/%count%/gi,`${t}`),r=r.replace(/%forloop%/gi,this.generateSampleSrc(t)),this.programCache[t]=new Qt(this.vertexSrc,r)}const e={tint:new Float32Array([1,1,1,1]),translationMatrix:new Z,default:this.defaultGroupCache[t]};return new Wt(this.programCache[t],e)}generateSampleSrc(t){let e="";e+=`
`,e+=`
`;for(let s=0;s<t;s++)s>0&&(e+=`
else `),s<t-1&&(e+=`if(vTextureId < ${s}.5)`),e+=`
{`,e+=`
	color = texture2D(uSamplers[${s}], vTextureCoord);`,e+=`
}`;return e+=`
`,e+=`
`,e}}class $r extends ce{constructor(t=!1){super(),this._buffer=new dt(null,t,!1),this._indexBuffer=new dt(null,t,!0),this.addAttribute("aVertexPosition",this._buffer,2,!1,H.FLOAT).addAttribute("aTextureCoord",this._buffer,2,!1,H.FLOAT).addAttribute("aColor",this._buffer,4,!0,H.UNSIGNED_BYTE).addAttribute("aTextureId",this._buffer,1,!0,H.FLOAT).addIndex(this._indexBuffer)}}var nf=`precision highp float;
attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;
attribute vec4 aColor;
attribute float aTextureId;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform vec4 tint;

varying vec2 vTextureCoord;
varying vec4 vColor;
varying float vTextureId;

void main(void){
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vTextureId = aTextureId;
    vColor = aColor * tint;
}
`,of=`varying vec2 vTextureCoord;
varying vec4 vColor;
varying float vTextureId;
uniform sampler2D uSamplers[%count%];

void main(void){
    vec4 color;
    %forloop%
    gl_FragColor = color * vColor;
}
`;const Be=class extends ai{constructor(i){super(i),this.setShaderGenerator(),this.geometryClass=$r,this.vertexSize=6,this.state=Jt.for2d(),this.size=P.SPRITE_BATCH_SIZE*4,this._vertexCount=0,this._indexCount=0,this._bufferedElements=[],this._bufferedTextures=[],this._bufferSize=0,this._shader=null,this._packedGeometries=[],this._packedGeometryPoolSize=2,this._flushId=0,this._aBuffers={},this._iBuffers={},this.MAX_TEXTURES=1,this.renderer.on("prerender",this.onPrerender,this),i.runners.contextChange.add(this),this._dcIndex=0,this._aIndex=0,this._iIndex=0,this._attributeBuffer=null,this._indexBuffer=null,this._tempBoundTextures=[]}static get defaultVertexSrc(){return nf}static get defaultFragmentTemplate(){return of}setShaderGenerator({vertex:i=Be.defaultVertexSrc,fragment:t=Be.defaultFragmentTemplate}={}){this.shaderGenerator=new ha(i,t)}contextChange(){const i=this.renderer.gl;P.PREFER_ENV===lt.WEBGL_LEGACY?this.MAX_TEXTURES=1:(this.MAX_TEXTURES=Math.min(i.getParameter(i.MAX_TEXTURE_IMAGE_UNITS),P.SPRITE_MAX_TEXTURES),this.MAX_TEXTURES=Yo(this.MAX_TEXTURES,i)),this._shader=this.shaderGenerator.generateShader(this.MAX_TEXTURES);for(let t=0;t<this._packedGeometryPoolSize;t++)this._packedGeometries[t]=new this.geometryClass;this.initFlushBuffers()}initFlushBuffers(){const{_drawCallPool:i,_textureArrayPool:t}=Be,e=this.size/4,s=Math.floor(e/this.MAX_TEXTURES)+1;for(;i.length<e;)i.push(new ds);for(;t.length<s;)t.push(new fs);for(let r=0;r<this.MAX_TEXTURES;r++)this._tempBoundTextures[r]=null}onPrerender(){this._flushId=0}render(i){!i._texture.valid||(this._vertexCount+i.vertexData.length/2>this.size&&this.flush(),this._vertexCount+=i.vertexData.length/2,this._indexCount+=i.indices.length,this._bufferedTextures[this._bufferSize]=i._texture.baseTexture,this._bufferedElements[this._bufferSize++]=i)}buildTexturesAndDrawCalls(){const{_bufferedTextures:i,MAX_TEXTURES:t}=this,e=Be._textureArrayPool,s=this.renderer.batch,r=this._tempBoundTextures,n=this.renderer.textureGC.count;let o=++$._globalBatch,a=0,h=e[0],l=0;s.copyBoundTextures(r,t);for(let c=0;c<this._bufferSize;++c){const u=i[c];i[c]=null,u._batchEnabled!==o&&(h.count>=t&&(s.boundArray(h,r,o,t),this.buildDrawCalls(h,l,c),l=c,h=e[++a],++o),u._batchEnabled=o,u.touched=n,h.elements[h.count++]=u)}h.count>0&&(s.boundArray(h,r,o,t),this.buildDrawCalls(h,l,this._bufferSize),++a,++o);for(let c=0;c<r.length;c++)r[c]=null;$._globalBatch=o}buildDrawCalls(i,t,e){const{_bufferedElements:s,_attributeBuffer:r,_indexBuffer:n,vertexSize:o}=this,a=Be._drawCallPool;let h=this._dcIndex,l=this._aIndex,c=this._iIndex,u=a[h];u.start=this._iIndex,u.texArray=i;for(let d=t;d<e;++d){const f=s[d],p=f._texture.baseTexture,g=tr[p.alphaMode?1:0][f.blendMode];s[d]=null,t<d&&u.blend!==g&&(u.size=c-u.start,t=d,u=a[++h],u.texArray=i,u.start=c),this.packInterleavedGeometry(f,r,n,l,c),l+=f.vertexData.length/2*o,c+=f.indices.length,u.blend=g}t<e&&(u.size=c-u.start,++h),this._dcIndex=h,this._aIndex=l,this._iIndex=c}bindAndClearTexArray(i){const t=this.renderer.texture;for(let e=0;e<i.count;e++)t.bind(i.elements[e],i.ids[e]),i.elements[e]=null;i.count=0}updateGeometry(){const{_packedGeometries:i,_attributeBuffer:t,_indexBuffer:e}=this;P.CAN_UPLOAD_SAME_BUFFER?(i[this._flushId]._buffer.update(t.rawBinaryData),i[this._flushId]._indexBuffer.update(e),this.renderer.geometry.updateBuffers()):(this._packedGeometryPoolSize<=this._flushId&&(this._packedGeometryPoolSize++,i[this._flushId]=new this.geometryClass),i[this._flushId]._buffer.update(t.rawBinaryData),i[this._flushId]._indexBuffer.update(e),this.renderer.geometry.bind(i[this._flushId]),this.renderer.geometry.updateBuffers(),this._flushId++)}drawBatches(){const i=this._dcIndex,{gl:t,state:e}=this.renderer,s=Be._drawCallPool;let r=null;for(let n=0;n<i;n++){const{texArray:o,type:a,size:h,start:l,blend:c}=s[n];r!==o&&(r=o,this.bindAndClearTexArray(o)),this.state.blendMode=c,e.set(this.state),t.drawElements(a,h,t.UNSIGNED_SHORT,l*2)}}flush(){this._vertexCount!==0&&(this._attributeBuffer=this.getAttributeBuffer(this._vertexCount),this._indexBuffer=this.getIndexBuffer(this._indexCount),this._aIndex=0,this._iIndex=0,this._dcIndex=0,this.buildTexturesAndDrawCalls(),this.updateGeometry(),this.drawBatches(),this._bufferSize=0,this._vertexCount=0,this._indexCount=0)}start(){this.renderer.state.set(this.state),this.renderer.texture.ensureSamplerType(this.MAX_TEXTURES),this.renderer.shader.bind(this._shader),P.CAN_UPLOAD_SAME_BUFFER&&this.renderer.geometry.bind(this._packedGeometries[this._flushId])}stop(){this.flush()}destroy(){for(let i=0;i<this._packedGeometryPoolSize;i++)this._packedGeometries[i]&&this._packedGeometries[i].destroy();this.renderer.off("prerender",this.onPrerender,this),this._aBuffers=null,this._iBuffers=null,this._packedGeometries=null,this._attributeBuffer=null,this._indexBuffer=null,this._shader&&(this._shader.destroy(),this._shader=null),super.destroy()}getAttributeBuffer(i){const t=si(Math.ceil(i/8)),e=rr(t),s=t*8;this._aBuffers.length<=e&&(this._iBuffers.length=e+1);let r=this._aBuffers[s];return r||(this._aBuffers[s]=r=new ps(s*this.vertexSize*4)),r}getIndexBuffer(i){const t=si(Math.ceil(i/12)),e=rr(t),s=t*12;this._iBuffers.length<=e&&(this._iBuffers.length=e+1);let r=this._iBuffers[e];return r||(this._iBuffers[e]=r=new Uint16Array(s)),r}packInterleavedGeometry(i,t,e,s,r){const{uint32View:n,float32View:o}=t,a=s/this.vertexSize,h=i.uvs,l=i.indices,c=i.vertexData,u=i._texture.baseTexture._batchLocation,d=Math.min(i.worldAlpha,1),f=d<1&&i._texture.baseTexture.alphaMode?qi(i._tintRGB,d):i._tintRGB+(d*255<<24);for(let p=0;p<c.length;p+=2)o[s++]=c[p],o[s++]=c[p+1],o[s++]=h[p],o[s++]=h[p+1],n[s++]=f,o[s++]=u;for(let p=0;p<l.length;p++)e[r++]=a+l[p]}};let ci=Be;ci.extension={name:"batch",type:F.RendererPlugin},ci._drawCallPool=[],ci._textureArrayPool=[],U.add(ci);class af{constructor(){this._glTransformFeedbacks={},this.buffers=[],this.disposeRunner=new It("disposeTransformFeedback")}bindBuffer(t,e){this.buffers[t]=e}destroy(){this.disposeRunner.emit(this,!1)}}class hf{constructor(t){this.buffer=t||null,this.updateID=-1,this.byteLength=-1,this.refCount=0}}class qr{constructor(t){this.renderer=t,this.managedBuffers={},this.boundBufferBases={}}destroy(){this.renderer=null}contextChange(){this.disposeAll(!0),this.gl=this.renderer.gl,this.CONTEXT_UID=this.renderer.CONTEXT_UID}bind(t){const{gl:e,CONTEXT_UID:s}=this,r=t._glBuffers[s]||this.createGLBuffer(t);e.bindBuffer(t.type,r.buffer)}unbind(t){const{gl:e}=this;e.bindBuffer(t,null)}bindBufferBase(t,e){const{gl:s,CONTEXT_UID:r}=this;if(this.boundBufferBases[e]!==t){const n=t._glBuffers[r]||this.createGLBuffer(t);this.boundBufferBases[e]=t,s.bindBufferBase(s.UNIFORM_BUFFER,e,n.buffer)}}bindBufferRange(t,e,s){const{gl:r,CONTEXT_UID:n}=this;s=s||0;const o=t._glBuffers[n]||this.createGLBuffer(t);r.bindBufferRange(r.UNIFORM_BUFFER,e||0,o.buffer,s*256,256)}update(t){const{gl:e,CONTEXT_UID:s}=this,r=t._glBuffers[s]||this.createGLBuffer(t);if(t._updateID!==r.updateID)if(r.updateID=t._updateID,e.bindBuffer(t.type,r.buffer),r.byteLength>=t.data.byteLength)e.bufferSubData(t.type,0,t.data);else{const n=t.static?e.STATIC_DRAW:e.DYNAMIC_DRAW;r.byteLength=t.data.byteLength,e.bufferData(t.type,t.data,n)}}dispose(t,e){if(!this.managedBuffers[t.id])return;delete this.managedBuffers[t.id];const s=t._glBuffers[this.CONTEXT_UID],r=this.gl;t.disposeRunner.remove(this),s&&(e||r.deleteBuffer(s.buffer),delete t._glBuffers[this.CONTEXT_UID])}disposeAll(t){const e=Object.keys(this.managedBuffers);for(let s=0;s<e.length;s++)this.dispose(this.managedBuffers[e[s]],t)}createGLBuffer(t){const{CONTEXT_UID:e,gl:s}=this;return t._glBuffers[e]=new hf(s.createBuffer()),this.managedBuffers[t.id]=t,t.disposeRunner.add(this),t._glBuffers[e]}}qr.extension={type:F.RendererSystem,name:"buffer"},U.add(qr);class Kr{constructor(t){this.renderer=t}contextChange(t){let e;if(this.renderer.context.webGLVersion===1){const s=t.getParameter(t.FRAMEBUFFER_BINDING);t.bindFramebuffer(t.FRAMEBUFFER,null),e=t.getParameter(t.SAMPLES),t.bindFramebuffer(t.FRAMEBUFFER,s)}else{const s=t.getParameter(t.DRAW_FRAMEBUFFER_BINDING);t.bindFramebuffer(t.DRAW_FRAMEBUFFER,null),e=t.getParameter(t.SAMPLES),t.bindFramebuffer(t.DRAW_FRAMEBUFFER,s)}e>=ft.HIGH?this.multisample=ft.HIGH:e>=ft.MEDIUM?this.multisample=ft.MEDIUM:e>=ft.LOW?this.multisample=ft.LOW:this.multisample=ft.NONE}destroy(){}}Kr.extension={type:F.RendererSystem,name:"_multisample"},U.add(Kr);class Zr{constructor(t){this.renderer=t}render(t,e){const s=this.renderer;let r,n,o,a;if(e&&(r=e.renderTexture,n=e.clear,o=e.transform,a=e.skipUpdateTransform),this.renderingToScreen=!r,s.runners.prerender.emit(),s.emit("prerender"),s.projection.transform=o,!s.context.isLost){if(r||(this.lastObjectRendered=t),!a){const h=t.enableTempParent();t.updateTransform(),t.disableTempParent(h)}s.renderTexture.bind(r),s.batch.currentRenderer.start(),(n!=null?n:s.background.clearBeforeRender)&&s.renderTexture.clear(),t.render(s),s.batch.currentRenderer.flush(),r&&(e.blit&&s.framebuffer.blit(),r.baseTexture.update()),s.runners.postrender.emit(),s.projection.transform=null,s.emit("postrender")}}destroy(){this.renderer=null,this.lastObjectRendered=null}}Zr.extension={type:F.RendererSystem,name:"objectRenderer"},U.add(Zr);const lf="7.0.5";P.SORTABLE_CHILDREN=!1;class ui{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.rect=null,this.updateID=-1}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}clear(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}getRectangle(t){return this.minX>this.maxX||this.minY>this.maxY?j.EMPTY:(t=t||new j(0,0,1,1),t.x=this.minX,t.y=this.minY,t.width=this.maxX-this.minX,t.height=this.maxY-this.minY,t)}addPoint(t){this.minX=Math.min(this.minX,t.x),this.maxX=Math.max(this.maxX,t.x),this.minY=Math.min(this.minY,t.y),this.maxY=Math.max(this.maxY,t.y)}addPointMatrix(t,e){const{a:s,b:r,c:n,d:o,tx:a,ty:h}=t,l=s*e.x+n*e.y+a,c=r*e.x+o*e.y+h;this.minX=Math.min(this.minX,l),this.maxX=Math.max(this.maxX,l),this.minY=Math.min(this.minY,c),this.maxY=Math.max(this.maxY,c)}addQuad(t){let e=this.minX,s=this.minY,r=this.maxX,n=this.maxY,o=t[0],a=t[1];e=o<e?o:e,s=a<s?a:s,r=o>r?o:r,n=a>n?a:n,o=t[2],a=t[3],e=o<e?o:e,s=a<s?a:s,r=o>r?o:r,n=a>n?a:n,o=t[4],a=t[5],e=o<e?o:e,s=a<s?a:s,r=o>r?o:r,n=a>n?a:n,o=t[6],a=t[7],e=o<e?o:e,s=a<s?a:s,r=o>r?o:r,n=a>n?a:n,this.minX=e,this.minY=s,this.maxX=r,this.maxY=n}addFrame(t,e,s,r,n){this.addFrameMatrix(t.worldTransform,e,s,r,n)}addFrameMatrix(t,e,s,r,n){const o=t.a,a=t.b,h=t.c,l=t.d,c=t.tx,u=t.ty;let d=this.minX,f=this.minY,p=this.maxX,g=this.maxY,m=o*e+h*s+c,x=a*e+l*s+u;d=m<d?m:d,f=x<f?x:f,p=m>p?m:p,g=x>g?x:g,m=o*r+h*s+c,x=a*r+l*s+u,d=m<d?m:d,f=x<f?x:f,p=m>p?m:p,g=x>g?x:g,m=o*e+h*n+c,x=a*e+l*n+u,d=m<d?m:d,f=x<f?x:f,p=m>p?m:p,g=x>g?x:g,m=o*r+h*n+c,x=a*r+l*n+u,d=m<d?m:d,f=x<f?x:f,p=m>p?m:p,g=x>g?x:g,this.minX=d,this.minY=f,this.maxX=p,this.maxY=g}addVertexData(t,e,s){let r=this.minX,n=this.minY,o=this.maxX,a=this.maxY;for(let h=e;h<s;h+=2){const l=t[h],c=t[h+1];r=l<r?l:r,n=c<n?c:n,o=l>o?l:o,a=c>a?c:a}this.minX=r,this.minY=n,this.maxX=o,this.maxY=a}addVertices(t,e,s,r){this.addVerticesMatrix(t.worldTransform,e,s,r)}addVerticesMatrix(t,e,s,r,n=0,o=n){const a=t.a,h=t.b,l=t.c,c=t.d,u=t.tx,d=t.ty;let f=this.minX,p=this.minY,g=this.maxX,m=this.maxY;for(let x=s;x<r;x+=2){const T=e[x],_=e[x+1],y=a*T+l*_+u,E=c*_+h*T+d;f=Math.min(f,y-n),g=Math.max(g,y+n),p=Math.min(p,E-o),m=Math.max(m,E+o)}this.minX=f,this.minY=p,this.maxX=g,this.maxY=m}addBounds(t){const e=this.minX,s=this.minY,r=this.maxX,n=this.maxY;this.minX=t.minX<e?t.minX:e,this.minY=t.minY<s?t.minY:s,this.maxX=t.maxX>r?t.maxX:r,this.maxY=t.maxY>n?t.maxY:n}addBoundsMask(t,e){const s=t.minX>e.minX?t.minX:e.minX,r=t.minY>e.minY?t.minY:e.minY,n=t.maxX<e.maxX?t.maxX:e.maxX,o=t.maxY<e.maxY?t.maxY:e.maxY;if(s<=n&&r<=o){const a=this.minX,h=this.minY,l=this.maxX,c=this.maxY;this.minX=s<a?s:a,this.minY=r<h?r:h,this.maxX=n>l?n:l,this.maxY=o>c?o:c}}addBoundsMatrix(t,e){this.addFrameMatrix(e,t.minX,t.minY,t.maxX,t.maxY)}addBoundsArea(t,e){const s=t.minX>e.x?t.minX:e.x,r=t.minY>e.y?t.minY:e.y,n=t.maxX<e.x+e.width?t.maxX:e.x+e.width,o=t.maxY<e.y+e.height?t.maxY:e.y+e.height;if(s<=n&&r<=o){const a=this.minX,h=this.minY,l=this.maxX,c=this.maxY;this.minX=s<a?s:a,this.minY=r<h?r:h,this.maxX=n>l?n:l,this.maxY=o>c?o:c}}pad(t=0,e=t){this.isEmpty()||(this.minX-=t,this.maxX+=t,this.minY-=e,this.maxY+=e)}addFramePad(t,e,s,r,n,o){t-=n,e-=o,s+=n,r+=o,this.minX=this.minX<t?this.minX:t,this.maxX=this.maxX>s?this.maxX:s,this.minY=this.minY<e?this.minY:e,this.maxY=this.maxY>r?this.maxY:r}}class at extends Le{constructor(){super(),this.tempDisplayObjectParent=null,this.transform=new Ze,this.alpha=1,this.visible=!0,this.renderable=!0,this.cullable=!1,this.cullArea=null,this.parent=null,this.worldAlpha=1,this._lastSortedIndex=0,this._zIndex=0,this.filterArea=null,this.filters=null,this._enabledFilters=null,this._bounds=new ui,this._localBounds=null,this._boundsID=0,this._boundsRect=null,this._localBoundsRect=null,this._mask=null,this._maskRefCount=0,this._destroyed=!1,this.isSprite=!1,this.isMask=!1}static mixin(t){const e=Object.keys(t);for(let s=0;s<e.length;++s){const r=e[s];Object.defineProperty(at.prototype,r,Object.getOwnPropertyDescriptor(t,r))}}get destroyed(){return this._destroyed}_recursivePostUpdateTransform(){this.parent?(this.parent._recursivePostUpdateTransform(),this.transform.updateTransform(this.parent.transform)):this.transform.updateTransform(this._tempDisplayObjectParent.transform)}updateTransform(){this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha}getBounds(t,e){return t||(this.parent?(this._recursivePostUpdateTransform(),this.updateTransform()):(this.parent=this._tempDisplayObjectParent,this.updateTransform(),this.parent=null)),this._bounds.updateID!==this._boundsID&&(this.calculateBounds(),this._bounds.updateID=this._boundsID),e||(this._boundsRect||(this._boundsRect=new j),e=this._boundsRect),this._bounds.getRectangle(e)}getLocalBounds(t){t||(this._localBoundsRect||(this._localBoundsRect=new j),t=this._localBoundsRect),this._localBounds||(this._localBounds=new ui);const e=this.transform,s=this.parent;this.parent=null,this.transform=this._tempDisplayObjectParent.transform;const r=this._bounds,n=this._boundsID;this._bounds=this._localBounds;const o=this.getBounds(!1,t);return this.parent=s,this.transform=e,this._bounds=r,this._bounds.updateID+=this._boundsID-n,o}toGlobal(t,e,s=!1){return s||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.apply(t,e)}toLocal(t,e,s,r){return e&&(t=e.toGlobal(t,s,r)),r||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.applyInverse(t,s)}setParent(t){if(!t||!t.addChild)throw new Error("setParent: Argument must be a Container");return t.addChild(this),t}removeFromParent(){var t;(t=this.parent)==null||t.removeChild(this)}setTransform(t=0,e=0,s=1,r=1,n=0,o=0,a=0,h=0,l=0){return this.position.x=t,this.position.y=e,this.scale.x=s||1,this.scale.y=r||1,this.rotation=n,this.skew.x=o,this.skew.y=a,this.pivot.x=h,this.pivot.y=l,this}destroy(t){this.removeFromParent(),this._destroyed=!0,this.transform=null,this.parent=null,this._bounds=null,this.mask=null,this.cullArea=null,this.filters=null,this.filterArea=null,this.hitArea=null,this.interactive=!1,this.interactiveChildren=!1,this.emit("destroyed"),this.removeAllListeners()}get _tempDisplayObjectParent(){return this.tempDisplayObjectParent===null&&(this.tempDisplayObjectParent=new la),this.tempDisplayObjectParent}enableTempParent(){const t=this.parent;return this.parent=this._tempDisplayObjectParent,t}disableTempParent(t){this.parent=t}get x(){return this.position.x}set x(t){this.transform.position.x=t}get y(){return this.position.y}set y(t){this.transform.position.y=t}get worldTransform(){return this.transform.worldTransform}get localTransform(){return this.transform.localTransform}get position(){return this.transform.position}set position(t){this.transform.position.copyFrom(t)}get scale(){return this.transform.scale}set scale(t){this.transform.scale.copyFrom(t)}get pivot(){return this.transform.pivot}set pivot(t){this.transform.pivot.copyFrom(t)}get skew(){return this.transform.skew}set skew(t){this.transform.skew.copyFrom(t)}get rotation(){return this.transform.rotation}set rotation(t){this.transform.rotation=t}get angle(){return this.transform.rotation*so}set angle(t){this.transform.rotation=t*ro}get zIndex(){return this._zIndex}set zIndex(t){this._zIndex=t,this.parent&&(this.parent.sortDirty=!0)}get worldVisible(){let t=this;do{if(!t.visible)return!1;t=t.parent}while(t);return!0}get mask(){return this._mask}set mask(t){if(this._mask!==t){if(this._mask){const e=this._mask.isMaskData?this._mask.maskObject:this._mask;e&&(e._maskRefCount--,e._maskRefCount===0&&(e.renderable=!0,e.isMask=!1))}if(this._mask=t,this._mask){const e=this._mask.isMaskData?this._mask.maskObject:this._mask;e&&(e._maskRefCount===0&&(e.renderable=!1,e.isMask=!0),e._maskRefCount++)}}}}class la extends at{constructor(){super(...arguments),this.sortDirty=null}}at.prototype.displayObjectUpdateTransform=at.prototype.updateTransform;const cf=new Z;function uf(i,t){return i.zIndex===t.zIndex?i._lastSortedIndex-t._lastSortedIndex:i.zIndex-t.zIndex}class wt extends at{constructor(){super(),this.children=[],this.sortableChildren=P.SORTABLE_CHILDREN,this.sortDirty=!1}onChildrenChange(t){}addChild(...t){if(t.length>1)for(let e=0;e<t.length;e++)this.addChild(t[e]);else{const e=t[0];e.parent&&e.parent.removeChild(e),e.parent=this,this.sortDirty=!0,e.transform._parentID=-1,this.children.push(e),this._boundsID++,this.onChildrenChange(this.children.length-1),this.emit("childAdded",e,this,this.children.length-1),e.emit("added",this)}return t[0]}addChildAt(t,e){if(e<0||e>this.children.length)throw new Error(`${t}addChildAt: The index ${e} supplied is out of bounds ${this.children.length}`);return t.parent&&t.parent.removeChild(t),t.parent=this,this.sortDirty=!0,t.transform._parentID=-1,this.children.splice(e,0,t),this._boundsID++,this.onChildrenChange(e),t.emit("added",this),this.emit("childAdded",t,this,e),t}swapChildren(t,e){if(t===e)return;const s=this.getChildIndex(t),r=this.getChildIndex(e);this.children[s]=e,this.children[r]=t,this.onChildrenChange(s<r?s:r)}getChildIndex(t){const e=this.children.indexOf(t);if(e===-1)throw new Error("The supplied DisplayObject must be a child of the caller");return e}setChildIndex(t,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);const s=this.getChildIndex(t);Ae(this.children,s,1),this.children.splice(e,0,t),this.onChildrenChange(e)}getChildAt(t){if(t<0||t>=this.children.length)throw new Error(`getChildAt: Index (${t}) does not exist.`);return this.children[t]}removeChild(...t){if(t.length>1)for(let e=0;e<t.length;e++)this.removeChild(t[e]);else{const e=t[0],s=this.children.indexOf(e);if(s===-1)return null;e.parent=null,e.transform._parentID=-1,Ae(this.children,s,1),this._boundsID++,this.onChildrenChange(s),e.emit("removed",this),this.emit("childRemoved",e,this,s)}return t[0]}removeChildAt(t){const e=this.getChildAt(t);return e.parent=null,e.transform._parentID=-1,Ae(this.children,t,1),this._boundsID++,this.onChildrenChange(t),e.emit("removed",this),this.emit("childRemoved",e,this,t),e}removeChildren(t=0,e=this.children.length){const s=t,r=e,n=r-s;let o;if(n>0&&n<=r){o=this.children.splice(s,n);for(let a=0;a<o.length;++a)o[a].parent=null,o[a].transform&&(o[a].transform._parentID=-1);this._boundsID++,this.onChildrenChange(t);for(let a=0;a<o.length;++a)o[a].emit("removed",this),this.emit("childRemoved",o[a],this,a);return o}else if(n===0&&this.children.length===0)return[];throw new RangeError("removeChildren: numeric values are outside the acceptable range.")}sortChildren(){let t=!1;for(let e=0,s=this.children.length;e<s;++e){const r=this.children[e];r._lastSortedIndex=e,!t&&r.zIndex!==0&&(t=!0)}t&&this.children.length>1&&this.children.sort(uf),this.sortDirty=!1}updateTransform(){this.sortableChildren&&this.sortDirty&&this.sortChildren(),this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha;for(let t=0,e=this.children.length;t<e;++t){const s=this.children[t];s.visible&&s.updateTransform()}}calculateBounds(){this._bounds.clear(),this._calculateBounds();for(let t=0;t<this.children.length;t++){const e=this.children[t];if(!(!e.visible||!e.renderable))if(e.calculateBounds(),e._mask){const s=e._mask.isMaskData?e._mask.maskObject:e._mask;s?(s.calculateBounds(),this._bounds.addBoundsMask(e._bounds,s._bounds)):this._bounds.addBounds(e._bounds)}else e.filterArea?this._bounds.addBoundsArea(e._bounds,e.filterArea):this._bounds.addBounds(e._bounds)}this._bounds.updateID=this._boundsID}getLocalBounds(t,e=!1){const s=super.getLocalBounds(t);if(!e)for(let r=0,n=this.children.length;r<n;++r){const o=this.children[r];o.visible&&o.updateTransform()}return s}_calculateBounds(){}_renderWithCulling(t){const e=t.renderTexture.sourceFrame;if(!(e.width>0&&e.height>0))return;let s,r;this.cullArea?(s=this.cullArea,r=this.worldTransform):this._render!==wt.prototype._render&&(s=this.getBounds(!0));const n=t.projection.transform;if(n&&(r?(r=cf.copyFrom(r),r.prepend(n)):r=n),s&&e.intersects(s,r))this._render(t);else if(this.cullArea)return;for(let o=0,a=this.children.length;o<a;++o){const h=this.children[o],l=h.cullable;h.cullable=l||!this.cullArea,h.render(t),h.cullable=l}}render(t){var e;if(!(!this.visible||this.worldAlpha<=0||!this.renderable))if(this._mask||((e=this.filters)==null?void 0:e.length))this.renderAdvanced(t);else if(this.cullable)this._renderWithCulling(t);else{this._render(t);for(let s=0,r=this.children.length;s<r;++s)this.children[s].render(t)}}renderAdvanced(t){var n,o,a;const e=this.filters,s=this._mask;if(e){this._enabledFilters||(this._enabledFilters=[]),this._enabledFilters.length=0;for(let h=0;h<e.length;h++)e[h].enabled&&this._enabledFilters.push(e[h])}const r=e&&((n=this._enabledFilters)==null?void 0:n.length)||s&&(!s.isMaskData||s.enabled&&(s.autoDetect||s.type!==mt.NONE));if(r&&t.batch.flush(),e&&((o=this._enabledFilters)==null?void 0:o.length)&&t.filter.push(this,this._enabledFilters),s&&t.mask.push(this,this._mask),this.cullable)this._renderWithCulling(t);else{this._render(t);for(let h=0,l=this.children.length;h<l;++h)this.children[h].render(t)}r&&t.batch.flush(),s&&t.mask.pop(this),e&&((a=this._enabledFilters)==null?void 0:a.length)&&t.filter.pop()}_render(t){}destroy(t){super.destroy(),this.sortDirty=!1;const e=typeof t=="boolean"?t:t==null?void 0:t.children,s=this.removeChildren(0,this.children.length);if(e)for(let r=0;r<s.length;++r)s[r].destroy(t)}get width(){return this.scale.x*this.getLocalBounds().width}set width(t){const e=this.getLocalBounds().width;e!==0?this.scale.x=t/e:this.scale.x=1,this._width=t}get height(){return this.scale.y*this.getLocalBounds().height}set height(t){const e=this.getLocalBounds().height;e!==0?this.scale.y=t/e:this.scale.y=1,this._height=t}}wt.prototype.containerUpdateTransform=wt.prototype.updateTransform;const di=new W,df=new Uint16Array([0,1,2,0,2,3]);class jt extends wt{constructor(t){super(),this._anchor=new oe(this._onAnchorUpdate,this,t?t.defaultAnchor.x:0,t?t.defaultAnchor.y:0),this._texture=null,this._width=0,this._height=0,this._tint=null,this._tintRGB=null,this.tint=16777215,this.blendMode=C.NORMAL,this._cachedTint=16777215,this.uvs=null,this.texture=t||L.EMPTY,this.vertexData=new Float32Array(8),this.vertexTrimmedData=null,this._transformID=-1,this._textureID=-1,this._transformTrimmedID=-1,this._textureTrimmedID=-1,this.indices=df,this.pluginName="batch",this.isSprite=!0,this._roundPixels=P.ROUND_PIXELS}_onTextureUpdate(){this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this._width&&(this.scale.x=Ce(this.scale.x)*this._width/this._texture.orig.width),this._height&&(this.scale.y=Ce(this.scale.y)*this._height/this._texture.orig.height)}_onAnchorUpdate(){this._transformID=-1,this._transformTrimmedID=-1}calculateVertices(){const t=this._texture;if(this._transformID===this.transform._worldID&&this._textureID===t._updateID)return;this._textureID!==t._updateID&&(this.uvs=this._texture._uvs.uvsFloat32),this._transformID=this.transform._worldID,this._textureID=t._updateID;const e=this.transform.worldTransform,s=e.a,r=e.b,n=e.c,o=e.d,a=e.tx,h=e.ty,l=this.vertexData,c=t.trim,u=t.orig,d=this._anchor;let f=0,p=0,g=0,m=0;if(c?(p=c.x-d._x*u.width,f=p+c.width,m=c.y-d._y*u.height,g=m+c.height):(p=-d._x*u.width,f=p+u.width,m=-d._y*u.height,g=m+u.height),l[0]=s*p+n*m+a,l[1]=o*m+r*p+h,l[2]=s*f+n*m+a,l[3]=o*m+r*f+h,l[4]=s*f+n*g+a,l[5]=o*g+r*f+h,l[6]=s*p+n*g+a,l[7]=o*g+r*p+h,this._roundPixels){const x=P.RESOLUTION;for(let T=0;T<l.length;++T)l[T]=Math.round(l[T]*x)/x}}calculateTrimmedVertices(){if(!this.vertexTrimmedData)this.vertexTrimmedData=new Float32Array(8);else if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID)return;this._transformTrimmedID=this.transform._worldID,this._textureTrimmedID=this._texture._updateID;const t=this._texture,e=this.vertexTrimmedData,s=t.orig,r=this._anchor,n=this.transform.worldTransform,o=n.a,a=n.b,h=n.c,l=n.d,c=n.tx,u=n.ty,d=-r._x*s.width,f=d+s.width,p=-r._y*s.height,g=p+s.height;e[0]=o*d+h*p+c,e[1]=l*p+a*d+u,e[2]=o*f+h*p+c,e[3]=l*p+a*f+u,e[4]=o*f+h*g+c,e[5]=l*g+a*f+u,e[6]=o*d+h*g+c,e[7]=l*g+a*d+u}_render(t){this.calculateVertices(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this)}_calculateBounds(){const t=this._texture.trim,e=this._texture.orig;!t||t.width===e.width&&t.height===e.height?(this.calculateVertices(),this._bounds.addQuad(this.vertexData)):(this.calculateTrimmedVertices(),this._bounds.addQuad(this.vertexTrimmedData))}getLocalBounds(t){return this.children.length===0?(this._localBounds||(this._localBounds=new ui),this._localBounds.minX=this._texture.orig.width*-this._anchor._x,this._localBounds.minY=this._texture.orig.height*-this._anchor._y,this._localBounds.maxX=this._texture.orig.width*(1-this._anchor._x),this._localBounds.maxY=this._texture.orig.height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new j),t=this._localBoundsRect),this._localBounds.getRectangle(t)):super.getLocalBounds.call(this,t)}containsPoint(t){this.worldTransform.applyInverse(t,di);const e=this._texture.orig.width,s=this._texture.orig.height,r=-e*this.anchor.x;let n=0;return di.x>=r&&di.x<r+e&&(n=-s*this.anchor.y,di.y>=n&&di.y<n+s)}destroy(t){if(super.destroy(t),this._texture.off("update",this._onTextureUpdate,this),this._anchor=null,typeof t=="boolean"?t:t==null?void 0:t.texture){const s=typeof t=="boolean"?t:t==null?void 0:t.baseTexture;this._texture.destroy(!!s)}this._texture=null}static from(t,e){const s=t instanceof L?t:L.from(t,e);return new jt(s)}set roundPixels(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t}get roundPixels(){return this._roundPixels}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(t){const e=Ce(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(t){const e=Ce(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t}get anchor(){return this._anchor}set anchor(t){this._anchor.copyFrom(t)}get tint(){return this._tint}set tint(t){this._tint=t,this._tintRGB=(t>>16)+(t&65280)+((t&255)<<16)}get texture(){return this._texture}set texture(t){this._texture!==t&&(this._texture&&this._texture.off("update",this._onTextureUpdate,this),this._texture=t||L.EMPTY,this._cachedTint=16777215,this._textureID=-1,this._textureTrimmedID=-1,t&&(t.baseTexture.valid?this._onTextureUpdate():t.once("update",this._onTextureUpdate,this)))}}const fi=new W;class ms extends jt{constructor(t,e=100,s=100){super(t),this.tileTransform=new Ze,this._width=e,this._height=s,this.uvMatrix=this.texture.uvMatrix||new hs(t),this.pluginName="tilingSprite",this.uvRespectAnchor=!1}get clampMargin(){return this.uvMatrix.clampMargin}set clampMargin(t){this.uvMatrix.clampMargin=t,this.uvMatrix.update(!0)}get tileScale(){return this.tileTransform.scale}set tileScale(t){this.tileTransform.scale.copyFrom(t)}get tilePosition(){return this.tileTransform.position}set tilePosition(t){this.tileTransform.position.copyFrom(t)}_onTextureUpdate(){this.uvMatrix&&(this.uvMatrix.texture=this._texture),this._cachedTint=16777215}_render(t){const e=this._texture;!e||!e.valid||(this.tileTransform.updateLocalTransform(),this.uvMatrix.update(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this))}_calculateBounds(){const t=this._width*-this._anchor._x,e=this._height*-this._anchor._y,s=this._width*(1-this._anchor._x),r=this._height*(1-this._anchor._y);this._bounds.addFrame(this.transform,t,e,s,r)}getLocalBounds(t){return this.children.length===0?(this._bounds.minX=this._width*-this._anchor._x,this._bounds.minY=this._height*-this._anchor._y,this._bounds.maxX=this._width*(1-this._anchor._x),this._bounds.maxY=this._height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new j),t=this._localBoundsRect),this._bounds.getRectangle(t)):super.getLocalBounds.call(this,t)}containsPoint(t){this.worldTransform.applyInverse(t,fi);const e=this._width,s=this._height,r=-e*this.anchor._x;if(fi.x>=r&&fi.x<r+e){const n=-s*this.anchor._y;if(fi.y>=n&&fi.y<n+s)return!0}return!1}destroy(t){super.destroy(t),this.tileTransform=null,this.uvMatrix=null}static from(t,e){const s=t instanceof L?t:L.from(t,e);return new ms(s,e.width,e.height)}get width(){return this._width}set width(t){this._width=t}get height(){return this._height}set height(t){this._height=t}}var ff=`#version 100
#define SHADER_NAME Tiling-Sprite-Simple-100

precision lowp float;

varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform vec4 uColor;

void main(void)
{
    vec4 texSample = texture2D(uSampler, vTextureCoord);
    gl_FragColor = texSample * uColor;
}
`,ca=`#version 100
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTransform;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;
}
`,pf=`#version 100
#ifdef GL_EXT_shader_texture_lod
    #extension GL_EXT_shader_texture_lod : enable
#endif
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform vec4 uColor;
uniform mat3 uMapCoord;
uniform vec4 uClampFrame;
uniform vec2 uClampOffset;

void main(void)
{
    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);
    coord = (uMapCoord * vec3(coord, 1.0)).xy;
    vec2 unclamped = coord;
    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

    #ifdef GL_EXT_shader_texture_lod
        vec4 texSample = unclamped == coord
            ? texture2D(uSampler, coord)
            : texture2DLodEXT(uSampler, coord, 0);
    #else
        vec4 texSample = texture2D(uSampler, coord);
    #endif

    gl_FragColor = texSample * uColor;
}
`,mf=`#version 300 es
#define SHADER_NAME Tiling-Sprite-300

precision lowp float;

in vec2 aVertexPosition;
in vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTransform;

out vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;
}
`,gf=`#version 300 es
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

in vec2 vTextureCoord;

out vec4 fragmentColor;

uniform sampler2D uSampler;
uniform vec4 uColor;
uniform mat3 uMapCoord;
uniform vec4 uClampFrame;
uniform vec2 uClampOffset;

void main(void)
{
    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);
    coord = (uMapCoord * vec3(coord, 1.0)).xy;
    vec2 unclamped = coord;
    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

    vec4 texSample = texture(uSampler, coord, unclamped == coord ? 0.0f : -32.0f);// lod-bias very negative to force lod 0

    fragmentColor = texSample * uColor;
}
`;const gs=new Z;class Qr extends ai{constructor(t){super(t),t.runners.contextChange.add(this),this.quad=new pr,this.state=Jt.for2d()}contextChange(){const t=this.renderer,e={globals:t.globalUniforms};this.simpleShader=Wt.from(ca,ff,e),this.shader=t.context.webGLVersion>1?Wt.from(mf,gf,e):Wt.from(ca,pf,e)}render(t){const e=this.renderer,s=this.quad;let r=s.vertices;r[0]=r[6]=t._width*-t.anchor.x,r[1]=r[3]=t._height*-t.anchor.y,r[2]=r[4]=t._width*(1-t.anchor.x),r[5]=r[7]=t._height*(1-t.anchor.y);const n=t.uvRespectAnchor?t.anchor.x:0,o=t.uvRespectAnchor?t.anchor.y:0;r=s.uvs,r[0]=r[6]=-n,r[1]=r[3]=-o,r[2]=r[4]=1-n,r[5]=r[7]=1-o,s.invalidate();const a=t._texture,h=a.baseTexture,l=h.alphaMode>0,c=t.tileTransform.localTransform,u=t.uvMatrix;let d=h.isPowerOfTwo&&a.frame.width===h.width&&a.frame.height===h.height;d&&(h._glTextures[e.CONTEXT_UID]?d=h.wrapMode!==Kt.CLAMP:h.wrapMode===Kt.CLAMP&&(h.wrapMode=Kt.REPEAT));const f=d?this.simpleShader:this.shader,p=a.width,g=a.height,m=t._width,x=t._height;gs.set(c.a*p/m,c.b*p/x,c.c*g/m,c.d*g/x,c.tx/m,c.ty/x),gs.invert(),d?gs.prepend(u.mapCoord):(f.uniforms.uMapCoord=u.mapCoord.toArray(!0),f.uniforms.uClampFrame=u.uClampFrame,f.uniforms.uClampOffset=u.uClampOffset),f.uniforms.uTransform=gs.toArray(!0),f.uniforms.uColor=ir(t.tint,t.worldAlpha,f.uniforms.uColor,l),f.uniforms.translationMatrix=t.transform.worldTransform.toArray(!0),f.uniforms.uSampler=a,e.shader.bind(f),e.geometry.bind(s),this.state.blendMode=er(t.blendMode,l),e.state.set(this.state),e.geometry.draw(this.renderer.gl.TRIANGLES,6,0)}}Qr.extension={name:"tilingSprite",type:F.RendererPlugin},U.add(Qr);const{deprecation:Tt}=Io,Jr=class extends Vr{constructor(i){var s;super(),this.rendererLogId="Canvas",i=Object.assign({},P.RENDER_OPTIONS,i);const t={runners:["init","destroy","contextChange","resolutionChange","reset","update","postrender","prerender","resize"],systems:Jr.__systems,priority:["textureGenerator","background","_view","_plugin","startup","mask","canvasContext","objectRenderer"]};this.setup(t),"useContextAlpha"in i&&(Tt("7.0.0","options.useContextAlpha is deprecated, use options.backgroundAlpha instead"),i.backgroundAlpha=i.useContextAlpha===!1?1:i.backgroundAlpha);const e={hello:i.hello,_plugin:Jr.__plugins,background:{alpha:i.backgroundAlpha,color:(s=i.background)!=null?s:i.backgroundColor,clearBeforeRender:i.clearBeforeRender},_view:{height:i.height,width:i.width,autoDensity:i.autoDensity,resolution:i.resolution,view:i.view}};this.startup.run(e)}static test(){return!0}generateTexture(i,t){return this.textureGenerator.generateTexture(i,t)}reset(){}render(i,t){this.objectRenderer.render(i,t)}clear(){this.canvasContext.clear()}destroy(i){this.runners.destroy.items.reverse(),this.emitWithCustomOptions(this.runners.destroy,{_view:i}),super.destroy()}get plugins(){return this._plugin.plugins}resize(i,t){this._view.resizeView(i,t)}get width(){return this._view.element.width}get height(){return this._view.element.height}get resolution(){return this._view.resolution}set resolution(i){this._view.resolution=i,this.runners.resolutionChange.emit(i)}get autoDensity(){return this._view.autoDensity}get view(){return this._view.element}get screen(){return this._view.screen}get lastObjectRendered(){return this.objectRenderer.lastObjectRendered}get renderingToScreen(){return this.objectRenderer.renderingToScreen}get clearBeforeRender(){return this.background.clearBeforeRender}get blendModes(){return Tt("7.0.0","renderer.blendModes has been deprecated, please use renderer.canvasContext.blendModes instead"),this.canvasContext.blendModes}get maskManager(){return Tt("7.0.0","renderer.maskManager has been deprecated, please use renderer.mask instead"),this.mask}get refresh(){return Tt("7.0.0","renderer.refresh has been deprecated"),!0}get rootContext(){return Tt("7.0.0","renderer.rootContext has been deprecated, please use renderer.canvasContext.rootContext instead"),this.canvasContext.rootContext}get context(){return Tt("7.0.0","renderer.context has been deprecated, please use renderer.canvasContext.activeContext instead"),this.canvasContext.activeContext}get smoothProperty(){return Tt("7.0.0","renderer.smoothProperty has been deprecated, please use renderer.canvasContext.smoothProperty instead"),this.canvasContext.smoothProperty}setBlendMode(i,t){Tt("7.0.0","renderer.setBlendMode has been deprecated, use renderer.canvasContext.setBlendMode instead"),this.canvasContext.setBlendMode(i,t)}invalidateBlendMode(){Tt("7.0.0","renderer.invalidateBlendMode has been deprecated, use renderer.canvasContext.invalidateBlendMode instead"),this.canvasContext.invalidateBlendMode()}setContextTransform(i,t,e){Tt("7.0.0","renderer.setContextTransform has been deprecated, use renderer.canvasContext.setContextTransform instead"),this.canvasContext.setContextTransform(i,t,e)}get backgroundColor(){return Tt("7.0.0","renderer.backgroundColor has been deprecated, use renderer.background.color instead."),this.background.color}set backgroundColor(i){Tt("7.0.0","renderer.backgroundColor has been deprecated, use renderer.background.color instead."),this.background.color=i}get backgroundAlpha(){return Tt("7.0.0","renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead."),this.background.color}set backgroundAlpha(i){Tt("7.0.0","renderer.backgroundAlpha has been deprecated, use renderer.background.alpha instead."),this.background.alpha=i}get preserveDrawingBuffer(){return Tt("7.0.0","renderer.preserveDrawingBuffer has been deprecated"),!1}get useContextAlpha(){return Tt("7.0.0","renderer.useContextAlpha has been deprecated"),!1}};let ge=Jr;ge.extension={type:F.Renderer,priority:0},ge.__plugins={},ge.__systems={},U.handleByMap(F.CanvasRendererPlugin,ge.__plugins),U.handleByMap(F.CanvasRendererSystem,ge.__systems),U.add(ge);class tn{constructor(t){this._foundShapes=[],this.renderer=t}pushMask(t){const e=this.renderer,s=t.maskObject||t;e.canvasContext.activeContext.save();const r=this._foundShapes;if(this.recursiveFindShapes(s,r),r.length>0){const n=e.canvasContext.activeContext;n.beginPath();for(let o=0;o<r.length;o++){const a=r[o],h=a.transform.worldTransform;this.renderer.canvasContext.setContextTransform(h),this.renderGraphicsShape(a)}r.length=0,n.clip()}}recursiveFindShapes(t,e){t.geometry&&t.geometry.graphicsData&&e.push(t);const{children:s}=t;if(s)for(let r=0;r<s.length;r++)this.recursiveFindShapes(s[r],e)}renderGraphicsShape(t){t.finishPoly();const e=this.renderer.canvasContext.activeContext,s=t.geometry.graphicsData,r=s.length;if(r!==0)for(let n=0;n<r;n++){const o=s[n],a=o.shape;if(a.type===et.POLY){let h=a.points;const l=o.holes;let c,u,d,f;e.moveTo(h[0],h[1]);for(let p=1;p<h.length/2;p++)e.lineTo(h[p*2],h[p*2+1]);if(l.length>0){c=0,d=h[0],f=h[1];for(let p=2;p+2<h.length;p+=2)c+=(h[p]-d)*(h[p+3]-f)-(h[p+2]-d)*(h[p+1]-f);for(let p=0;p<l.length;p++)if(h=l[p].shape.points,!!h){u=0,d=h[0],f=h[1];for(let g=2;g+2<h.length;g+=2)u+=(h[g]-d)*(h[g+3]-f)-(h[g+2]-d)*(h[g+1]-f);if(u*c<0){e.moveTo(h[0],h[1]);for(let g=2;g<h.length;g+=2)e.lineTo(h[g],h[g+1])}else{e.moveTo(h[h.length-2],h[h.length-1]);for(let g=h.length-4;g>=0;g-=2)e.lineTo(h[g],h[g+1])}l[p].shape.closeStroke&&e.closePath()}}h[0]===h[h.length-2]&&h[1]===h[h.length-1]&&e.closePath()}else if(a.type===et.RECT)e.rect(a.x,a.y,a.width,a.height),e.closePath();else if(a.type===et.CIRC)e.arc(a.x,a.y,a.radius,0,2*Math.PI),e.closePath();else if(a.type===et.ELIP){const h=a.width*2,l=a.height*2,c=a.x-h/2,u=a.y-l/2,d=.5522848,f=h/2*d,p=l/2*d,g=c+h,m=u+l,x=c+h/2,T=u+l/2;e.moveTo(c,T),e.bezierCurveTo(c,T-p,x-f,u,x,u),e.bezierCurveTo(x+f,u,g,T-p,g,T),e.bezierCurveTo(g,T+p,x+f,m,x,m),e.bezierCurveTo(x-f,m,c,T+p,c,T),e.closePath()}else if(a.type===et.RREC){const h=a.x,l=a.y,c=a.width,u=a.height;let d=a.radius;const f=Math.min(c,u)/2;d=d>f?f:d,e.moveTo(h,l+d),e.lineTo(h,l+u-d),e.quadraticCurveTo(h,l+u,h+d,l+u),e.lineTo(h+c-d,l+u),e.quadraticCurveTo(h+c,l+u,h+c,l+u-d),e.lineTo(h+c,l+d),e.quadraticCurveTo(h+c,l,h+c-d,l),e.lineTo(h+d,l),e.quadraticCurveTo(h,l,h,l+d),e.closePath()}}}popMask(t){t.canvasContext.activeContext.restore(),t.canvasContext.invalidateBlendMode()}destroy(){}}tn.extension={type:F.CanvasRendererSystem,name:"mask"},U.add(tn);class en{constructor(t){this.renderer=t}render(t,e){var d;const s=this.renderer;if(!s.view)return;const r=s.canvasContext;let n,o,a,h;e&&(n=e.renderTexture,o=e.clear,a=e.transform,h=e.skipUpdateTransform),this.renderingToScreen=!n,s.emit("prerender");const l=s.resolution;n?(n=n.castToBaseTexture(),n._canvasRenderTarget||(n._canvasRenderTarget=new Ge(n.width,n.height,n.resolution),n.resource=new hr(n._canvasRenderTarget.canvas),n.valid=!0),r.activeContext=n._canvasRenderTarget.context,s.canvasContext.activeResolution=n._canvasRenderTarget.resolution):(r.activeContext=r.rootContext,r.activeResolution=l);const c=r.activeContext;if(r._projTransform=a||null,n||(this.lastObjectRendered=t),!h){const f=t.enableTempParent();t.updateTransform(),t.disableTempParent(f)}if(c.save(),c.setTransform(1,0,0,1,0,0),c.globalAlpha=1,r._activeBlendMode=C.NORMAL,r._outerBlend=!1,c.globalCompositeOperation=r.blendModes[C.NORMAL],o!=null?o:s.background.clearBeforeRender)if(this.renderingToScreen){c.clearRect(0,0,s.width,s.height);const f=s.background;f.alpha>0&&(c.globalAlpha=f.alpha,c.fillStyle=f.colorString,c.fillRect(0,0,s.width,s.height),c.globalAlpha=1)}else{n=n,n._canvasRenderTarget.clear();const f=n.clearColor;f[3]>0&&(c.globalAlpha=(d=f[3])!=null?d:1,c.fillStyle=Yi(Js(f)),c.fillRect(0,0,n.realWidth,n.realHeight),c.globalAlpha=1)}const u=r.activeContext;r.activeContext=c,t.renderCanvas(s),r.activeContext=u,c.restore(),r.activeResolution=l,r._projTransform=null,s.emit("postrender")}destroy(){this.lastObjectRendered=null,this.render=null}}en.extension={type:F.CanvasRendererSystem,name:"objectRenderer"},U.add(en);function ua(i){const t=P.ADAPTER.createCanvas(6,1),e=t.getContext("2d");return e.fillStyle=i,e.fillRect(0,0,6,1),t}function sn(){if(typeof document=="undefined")return!1;const i=ua("#ff00ff"),t=ua("#ffff00"),s=P.ADAPTER.createCanvas(6,1).getContext("2d");s.globalCompositeOperation="multiply",s.drawImage(i,0,0),s.drawImage(t,2,0);const r=s.getImageData(2,0,1,1);if(!r)return!1;const n=r.data;return n[0]===255&&n[1]===0&&n[2]===0}function _f(i=[]){return sn()?(i[C.NORMAL]="source-over",i[C.ADD]="lighter",i[C.MULTIPLY]="multiply",i[C.SCREEN]="screen",i[C.OVERLAY]="overlay",i[C.DARKEN]="darken",i[C.LIGHTEN]="lighten",i[C.COLOR_DODGE]="color-dodge",i[C.COLOR_BURN]="color-burn",i[C.HARD_LIGHT]="hard-light",i[C.SOFT_LIGHT]="soft-light",i[C.DIFFERENCE]="difference",i[C.EXCLUSION]="exclusion",i[C.HUE]="hue",i[C.SATURATION]="saturation",i[C.COLOR]="color",i[C.LUMINOSITY]="luminosity"):(i[C.NORMAL]="source-over",i[C.ADD]="lighter",i[C.MULTIPLY]="source-over",i[C.SCREEN]="source-over",i[C.OVERLAY]="source-over",i[C.DARKEN]="source-over",i[C.LIGHTEN]="source-over",i[C.COLOR_DODGE]="source-over",i[C.COLOR_BURN]="source-over",i[C.HARD_LIGHT]="source-over",i[C.SOFT_LIGHT]="source-over",i[C.DIFFERENCE]="source-over",i[C.EXCLUSION]="source-over",i[C.HUE]="source-over",i[C.SATURATION]="source-over",i[C.COLOR]="source-over",i[C.LUMINOSITY]="source-over"),i[C.NORMAL_NPM]=i[C.NORMAL],i[C.ADD_NPM]=i[C.ADD],i[C.SCREEN_NPM]=i[C.SCREEN],i[C.SRC_IN]="source-in",i[C.SRC_OUT]="source-out",i[C.SRC_ATOP]="source-atop",i[C.DST_OVER]="destination-over",i[C.DST_IN]="destination-in",i[C.DST_OUT]="destination-out",i[C.DST_ATOP]="destination-atop",i[C.XOR]="xor",i[C.SUBTRACT]="source-over",i}const vf=new Z;class rn{constructor(t){this.activeResolution=1,this.smoothProperty="imageSmoothingEnabled",this.blendModes=_f(),this._activeBlendMode=null,this._projTransform=null,this._outerBlend=!1,this.renderer=t}init(){const t=this.renderer.background.alpha<1;if(this.rootContext=this.renderer.view.getContext("2d",{alpha:t}),this.activeContext=this.rootContext,!this.rootContext.imageSmoothingEnabled){const e=this.rootContext;e.webkitImageSmoothingEnabled?this.smoothProperty="webkitImageSmoothingEnabled":e.mozImageSmoothingEnabled?this.smoothProperty="mozImageSmoothingEnabled":e.oImageSmoothingEnabled?this.smoothProperty="oImageSmoothingEnabled":e.msImageSmoothingEnabled&&(this.smoothProperty="msImageSmoothingEnabled")}}setContextTransform(t,e,s){let r=t;const n=this._projTransform,o=this.activeResolution;s=s||o,n&&(r=vf,r.copyFrom(t),r.prepend(n)),e?this.activeContext.setTransform(r.a*s,r.b*s,r.c*s,r.d*s,r.tx*o|0,r.ty*o|0):this.activeContext.setTransform(r.a*s,r.b*s,r.c*s,r.d*s,r.tx*o,r.ty*o)}clear(t,e){const{activeContext:s,renderer:r}=this;t=t!=null?t:this.renderer.background.colorString,s.clearRect(0,0,r.width,r.height),t&&(s.globalAlpha=e!=null?e:this.renderer.background.alpha,s.fillStyle=t,s.fillRect(0,0,r.width,r.height),s.globalAlpha=1)}setBlendMode(t,e){const s=t===C.SRC_IN||t===C.SRC_OUT||t===C.DST_IN||t===C.DST_ATOP;!e&&s&&(t=C.NORMAL),this._activeBlendMode!==t&&(this._activeBlendMode=t,this._outerBlend=s,this.activeContext.globalCompositeOperation=this.blendModes[t])}resize(){this.smoothProperty&&(this.rootContext[this.smoothProperty]=P.SCALE_MODE===Dt.LINEAR)}invalidateBlendMode(){this._activeBlendMode=this.blendModes.indexOf(this.activeContext.globalCompositeOperation)}destroy(){this.renderer=null,this.rootContext=null,this.activeContext=null,this.smoothProperty=null}}rn.extension={type:F.CanvasRendererSystem,name:"canvasContext"},U.add(rn);const pt={canvas:null,getTintedCanvas:(i,t)=>{const e=i.texture;t=pt.roundColor(t);const s=`#${`00000${(t|0).toString(16)}`.slice(-6)}`;e.tintCache=e.tintCache||{};const r=e.tintCache[s];let n;if(r){if(r.tintId===e._updateID)return e.tintCache[s];n=e.tintCache[s]}else n=P.ADAPTER.createCanvas();if(pt.tintMethod(e,t,n),n.tintId=e._updateID,pt.convertTintToImage&&n.toDataURL!==void 0){const o=new Image;o.src=n.toDataURL(),e.tintCache[s]=o}else e.tintCache[s]=n;return n},getTintedPattern:(i,t)=>{t=pt.roundColor(t);const e=`#${`00000${(t|0).toString(16)}`.slice(-6)}`;i.patternCache=i.patternCache||{};let s=i.patternCache[e];return(s==null?void 0:s.tintId)===i._updateID||(pt.canvas||(pt.canvas=P.ADAPTER.createCanvas()),pt.tintMethod(i,t,pt.canvas),s=pt.canvas.getContext("2d").createPattern(pt.canvas,"repeat"),s.tintId=i._updateID,i.patternCache[e]=s),s},tintWithMultiply:(i,t,e)=>{const s=e.getContext("2d"),r=i._frame.clone(),n=i.baseTexture.resolution;r.x*=n,r.y*=n,r.width*=n,r.height*=n,e.width=Math.ceil(r.width),e.height=Math.ceil(r.height),s.save(),s.fillStyle=`#${`00000${(t|0).toString(16)}`.slice(-6)}`,s.fillRect(0,0,r.width,r.height),s.globalCompositeOperation="multiply";const o=i.baseTexture.getDrawableSource();s.drawImage(o,r.x,r.y,r.width,r.height,0,0,r.width,r.height),s.globalCompositeOperation="destination-atop",s.drawImage(o,r.x,r.y,r.width,r.height,0,0,r.width,r.height),s.restore()},tintWithOverlay:(i,t,e)=>{const s=e.getContext("2d"),r=i._frame.clone(),n=i.baseTexture.resolution;r.x*=n,r.y*=n,r.width*=n,r.height*=n,e.width=Math.ceil(r.width),e.height=Math.ceil(r.height),s.save(),s.globalCompositeOperation="copy",s.fillStyle=`#${`00000${(t|0).toString(16)}`.slice(-6)}`,s.fillRect(0,0,r.width,r.height),s.globalCompositeOperation="destination-atop",s.drawImage(i.baseTexture.getDrawableSource(),r.x,r.y,r.width,r.height,0,0,r.width,r.height),s.restore()},tintWithPerPixel:(i,t,e)=>{const s=e.getContext("2d"),r=i._frame.clone(),n=i.baseTexture.resolution;r.x*=n,r.y*=n,r.width*=n,r.height*=n,e.width=Math.ceil(r.width),e.height=Math.ceil(r.height),s.save(),s.globalCompositeOperation="copy",s.drawImage(i.baseTexture.getDrawableSource(),r.x,r.y,r.width,r.height,0,0,r.width,r.height),s.restore();const o=ae(t),a=o[0],h=o[1],l=o[2],c=s.getImageData(0,0,r.width,r.height),u=c.data;for(let d=0;d<u.length;d+=4)u[d+0]*=a,u[d+1]*=h,u[d+2]*=l;s.putImageData(c,0,0)},roundColor:i=>{const t=pt.cacheStepsPerColorChannel,e=ae(i);return e[0]=Math.min(255,e[0]/t*t),e[1]=Math.min(255,e[1]/t*t),e[2]=Math.min(255,e[2]/t*t),Js(e)},cacheStepsPerColorChannel:8,convertTintToImage:!1,canUseMultiply:sn(),tintMethod:null};pt.tintMethod=pt.canUseMultiply?pt.tintWithMultiply:pt.tintWithPerPixel,$.prototype.getDrawableSource=function(){const t=this.resource;return t?t.bitmap||t.source:null},is.prototype._canvasRenderTarget=null,L.prototype.patternCache=null,L.prototype.tintCache=null;const _s=new Z,pi=new Z,te=[new W,new W,new W,new W];ms.prototype._renderCanvas=function(t){const e=this._texture;if(!e.baseTexture.valid)return;const s=t.canvasContext.activeContext,r=this.worldTransform,n=e.baseTexture,o=n.getDrawableSource(),a=n.resolution;if(this._textureID!==this._texture._updateID||this._cachedTint!==this.tint){this._textureID=this._texture._updateID;const f=new Ge(e._frame.width,e._frame.height,a);this.tint!==16777215?(this._tintedCanvas=pt.getTintedCanvas(this,this.tint),f.context.drawImage(this._tintedCanvas,0,0)):f.context.drawImage(o,-e._frame.x*a,-e._frame.y*a),this._cachedTint=this.tint,this._canvasPattern=f.context.createPattern(f.canvas,"repeat")}s.globalAlpha=this.worldAlpha,t.canvasContext.setBlendMode(this.blendMode),this.tileTransform.updateLocalTransform();const h=this.tileTransform.localTransform,l=this._width,c=this._height;_s.identity(),pi.copyFrom(h),this.uvRespectAnchor||pi.translate(-this.anchor.x*l,-this.anchor.y*c),pi.scale(this.tileScale.x/a,this.tileScale.y/a),_s.prepend(pi),_s.prepend(r),t.canvasContext.setContextTransform(_s),s.fillStyle=this._canvasPattern;const u=this.anchor.x*-l,d=this.anchor.y*-c;te[0].set(u,d),te[1].set(u+l,d),te[2].set(u+l,d+c),te[3].set(u,d+c);for(let f=0;f<4;f++)pi.applyInverse(te[f],te[f]);s.beginPath(),s.moveTo(te[0].x,te[0].y);for(let f=1;f<4;f++)s.lineTo(te[f].x,te[f].y);s.closePath(),s.fill()};class da extends wt{constructor(t=1500,e,s=16384,r=!1){super();const n=16384;s>n&&(s=n),this._properties=[!1,!0,!1,!1,!1],this._maxSize=t,this._batchSize=s,this._buffers=null,this._bufferUpdateIDs=[],this._updateID=0,this.interactiveChildren=!1,this.blendMode=C.NORMAL,this.autoResize=r,this.roundPixels=!0,this.baseTexture=null,this.setProperties(e),this._tint=0,this.tintRgb=new Float32Array(4),this.tint=16777215}setProperties(t){t&&(this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0],this._properties[1]="position"in t?!!t.position:this._properties[1],this._properties[2]="rotation"in t?!!t.rotation:this._properties[2],this._properties[3]="uvs"in t?!!t.uvs:this._properties[3],this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4])}updateTransform(){this.displayObjectUpdateTransform()}get tint(){return this._tint}set tint(t){this._tint=t,ae(t,this.tintRgb)}render(t){!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable||(this.baseTexture||(this.baseTexture=this.children[0]._texture.baseTexture,this.baseTexture.valid||this.baseTexture.once("update",()=>this.onChildrenChange(0))),t.batch.setObjectRenderer(t.plugins.particle),t.plugins.particle.render(this))}onChildrenChange(t){const e=Math.floor(t/this._batchSize);for(;this._bufferUpdateIDs.length<e;)this._bufferUpdateIDs.push(0);this._bufferUpdateIDs[e]=++this._updateID}dispose(){if(this._buffers){for(let t=0;t<this._buffers.length;++t)this._buffers[t].destroy();this._buffers=null}}destroy(t){super.destroy(t),this.dispose(),this._properties=null,this._buffers=null,this._bufferUpdateIDs=null}}class fa{constructor(t,e,s){this.geometry=new ce,this.indexBuffer=null,this.size=s,this.dynamicProperties=[],this.staticProperties=[];for(let r=0;r<t.length;++r){let n=t[r];n={attributeName:n.attributeName,size:n.size,uploadFunction:n.uploadFunction,type:n.type||H.FLOAT,offset:n.offset},e[r]?this.dynamicProperties.push(n):this.staticProperties.push(n)}this.staticStride=0,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.dynamicStride=0,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this._updateID=0,this.initBuffers()}initBuffers(){const t=this.geometry;let e=0;this.indexBuffer=new dt(Eo(this.size),!0,!0),t.addIndex(this.indexBuffer),this.dynamicStride=0;for(let o=0;o<this.dynamicProperties.length;++o){const a=this.dynamicProperties[o];a.offset=e,e+=a.size,this.dynamicStride+=a.size}const s=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(s),this.dynamicDataUint32=new Uint32Array(s),this.dynamicBuffer=new dt(this.dynamicData,!1,!1);let r=0;this.staticStride=0;for(let o=0;o<this.staticProperties.length;++o){const a=this.staticProperties[o];a.offset=r,r+=a.size,this.staticStride+=a.size}const n=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(n),this.staticDataUint32=new Uint32Array(n),this.staticBuffer=new dt(this.staticData,!0,!1);for(let o=0;o<this.dynamicProperties.length;++o){const a=this.dynamicProperties[o];t.addAttribute(a.attributeName,this.dynamicBuffer,0,a.type===H.UNSIGNED_BYTE,a.type,this.dynamicStride*4,a.offset*4)}for(let o=0;o<this.staticProperties.length;++o){const a=this.staticProperties[o];t.addAttribute(a.attributeName,this.staticBuffer,0,a.type===H.UNSIGNED_BYTE,a.type,this.staticStride*4,a.offset*4)}}uploadDynamic(t,e,s){for(let r=0;r<this.dynamicProperties.length;r++){const n=this.dynamicProperties[r];n.uploadFunction(t,e,s,n.type===H.UNSIGNED_BYTE?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,n.offset)}this.dynamicBuffer._updateID++}uploadStatic(t,e,s){for(let r=0;r<this.staticProperties.length;r++){const n=this.staticProperties[r];n.uploadFunction(t,e,s,n.type===H.UNSIGNED_BYTE?this.staticDataUint32:this.staticData,this.staticStride,n.offset)}this.staticBuffer._updateID++}destroy(){this.indexBuffer=null,this.dynamicProperties=null,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this.staticProperties=null,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.geometry.destroy()}}var yf=`varying vec2 vTextureCoord;
varying vec4 vColor;

uniform sampler2D uSampler;

void main(void){
    vec4 color = texture2D(uSampler, vTextureCoord) * vColor;
    gl_FragColor = color;
}`,xf=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;
attribute vec4 aColor;

attribute vec2 aPositionCoord;
attribute float aRotation;

uniform mat3 translationMatrix;
uniform vec4 uColor;

varying vec2 vTextureCoord;
varying vec4 vColor;

void main(void){
    float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);
    float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);

    vec2 v = vec2(x, y);
    v = v + aPositionCoord;

    gl_Position = vec4((translationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vColor = aColor * uColor;
}
`;class nn extends ai{constructor(t){super(t),this.shader=null,this.properties=null,this.tempMatrix=new Z,this.properties=[{attributeName:"aVertexPosition",size:2,uploadFunction:this.uploadVertices,offset:0},{attributeName:"aPositionCoord",size:2,uploadFunction:this.uploadPosition,offset:0},{attributeName:"aRotation",size:1,uploadFunction:this.uploadRotation,offset:0},{attributeName:"aTextureCoord",size:2,uploadFunction:this.uploadUvs,offset:0},{attributeName:"aColor",size:1,type:H.UNSIGNED_BYTE,uploadFunction:this.uploadTint,offset:0}],this.shader=Wt.from(xf,yf,{}),this.state=Jt.for2d()}render(t){const e=t.children,s=t._maxSize,r=t._batchSize,n=this.renderer;let o=e.length;if(o===0)return;o>s&&!t.autoResize&&(o=s);let a=t._buffers;a||(a=t._buffers=this.generateBuffers(t));const h=e[0]._texture.baseTexture,l=h.alphaMode>0;this.state.blendMode=er(t.blendMode,l),n.state.set(this.state);const c=n.gl,u=t.worldTransform.copyTo(this.tempMatrix);u.prepend(n.globalUniforms.uniforms.projectionMatrix),this.shader.uniforms.translationMatrix=u.toArray(!0),this.shader.uniforms.uColor=bo(t.tintRgb,t.worldAlpha,this.shader.uniforms.uColor,l),this.shader.uniforms.uSampler=h,this.renderer.shader.bind(this.shader);let d=!1;for(let f=0,p=0;f<o;f+=r,p+=1){let g=o-f;g>r&&(g=r),p>=a.length&&a.push(this._generateOneMoreBuffer(t));const m=a[p];m.uploadDynamic(e,f,g);const x=t._bufferUpdateIDs[p]||0;d=d||m._updateID<x,d&&(m._updateID=t._updateID,m.uploadStatic(e,f,g)),n.geometry.bind(m.geometry),c.drawElements(c.TRIANGLES,g*6,c.UNSIGNED_SHORT,0)}}generateBuffers(t){const e=[],s=t._maxSize,r=t._batchSize,n=t._properties;for(let o=0;o<s;o+=r)e.push(new fa(this.properties,n,r));return e}_generateOneMoreBuffer(t){const e=t._batchSize,s=t._properties;return new fa(this.properties,s,e)}uploadVertices(t,e,s,r,n,o){let a=0,h=0,l=0,c=0;for(let u=0;u<s;++u){const d=t[e+u],f=d._texture,p=d.scale.x,g=d.scale.y,m=f.trim,x=f.orig;m?(h=m.x-d.anchor.x*x.width,a=h+m.width,c=m.y-d.anchor.y*x.height,l=c+m.height):(a=x.width*(1-d.anchor.x),h=x.width*-d.anchor.x,l=x.height*(1-d.anchor.y),c=x.height*-d.anchor.y),r[o]=h*p,r[o+1]=c*g,r[o+n]=a*p,r[o+n+1]=c*g,r[o+n*2]=a*p,r[o+n*2+1]=l*g,r[o+n*3]=h*p,r[o+n*3+1]=l*g,o+=n*4}}uploadPosition(t,e,s,r,n,o){for(let a=0;a<s;a++){const h=t[e+a].position;r[o]=h.x,r[o+1]=h.y,r[o+n]=h.x,r[o+n+1]=h.y,r[o+n*2]=h.x,r[o+n*2+1]=h.y,r[o+n*3]=h.x,r[o+n*3+1]=h.y,o+=n*4}}uploadRotation(t,e,s,r,n,o){for(let a=0;a<s;a++){const h=t[e+a].rotation;r[o]=h,r[o+n]=h,r[o+n*2]=h,r[o+n*3]=h,o+=n*4}}uploadUvs(t,e,s,r,n,o){for(let a=0;a<s;++a){const h=t[e+a]._texture._uvs;h?(r[o]=h.x0,r[o+1]=h.y0,r[o+n]=h.x1,r[o+n+1]=h.y1,r[o+n*2]=h.x2,r[o+n*2+1]=h.y2,r[o+n*3]=h.x3,r[o+n*3+1]=h.y3,o+=n*4):(r[o]=0,r[o+1]=0,r[o+n]=0,r[o+n+1]=0,r[o+n*2]=0,r[o+n*2+1]=0,r[o+n*3]=0,r[o+n*3+1]=0,o+=n*4)}}uploadTint(t,e,s,r,n,o){for(let a=0;a<s;++a){const h=t[e+a],l=h._texture.baseTexture.alphaMode>0,c=h.alpha,u=c<1&&l?qi(h._tintRGB,c):h._tintRGB+(c*255<<24);r[o]=u,r[o+n]=u,r[o+n*2]=u,r[o+n*3]=u,o+=n*4}}destroy(){super.destroy(),this.shader&&(this.shader.destroy(),this.shader=null),this.tempMatrix=null}}nn.extension={name:"particle",type:F.RendererPlugin},U.add(nn),da.prototype.renderCanvas=function(t){if(!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable)return;const e=t.canvasContext.activeContext,s=this.worldTransform;let r=!0,n=0,o=0,a=0,h=0;t.canvasContext.setBlendMode(this.blendMode),e.globalAlpha=this.worldAlpha,this.displayObjectUpdateTransform();for(let l=0;l<this.children.length;++l){const c=this.children[l];if(!c.visible||!c._texture.valid)continue;const u=c._texture.frame;if(e.globalAlpha=this.worldAlpha*c.alpha,c.rotation%(Math.PI*2)===0)r&&(t.canvasContext.setContextTransform(s,!1,1),r=!1),n=c.anchor.x*(-u.width*c.scale.x)+c.position.x+.5,o=c.anchor.y*(-u.height*c.scale.y)+c.position.y+.5,a=u.width*c.scale.x,h=u.height*c.scale.y;else{r||(r=!0),c.displayObjectUpdateTransform();const p=c.worldTransform;t.canvasContext.setContextTransform(p,this.roundPixels,1),n=c.anchor.x*-u.width+.5,o=c.anchor.y*-u.height+.5,a=u.width,h=u.height}const d=c._texture.baseTexture.resolution,f=t.canvasContext.activeResolution;e.drawImage(c._texture.baseTexture.getDrawableSource(),u.x*d,u.y*d,u.width*d,u.height*d,n*f,o*f,a*f,h*f)}},wt.prototype._renderCanvas=function(t){},wt.prototype.renderCanvas=function(t){if(!(!this.visible||this.worldAlpha<=0||!this.renderable)){this._mask&&t.mask.pushMask(this._mask),this._renderCanvas(t);for(let e=0,s=this.children.length;e<s;++e)this.children[e].renderCanvas(t);this._mask&&t.mask.popMask(t)}},at.prototype.renderCanvas=function(t){};var mi=(i=>(i[i.LINEAR_VERTICAL=0]="LINEAR_VERTICAL",i[i.LINEAR_HORIZONTAL=1]="LINEAR_HORIZONTAL",i))(mi||{});const on={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:mi.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100,leading:0},Tf=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];class ue{constructor(t){this.styleID=0,this.reset(),hn(this,t,t)}clone(){const t={};return hn(t,this,on),new ue(t)}reset(){hn(this,on,on)}get align(){return this._align}set align(t){this._align!==t&&(this._align=t,this.styleID++)}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords!==t&&(this._breakWords=t,this.styleID++)}get dropShadow(){return this._dropShadow}set dropShadow(t){this._dropShadow!==t&&(this._dropShadow=t,this.styleID++)}get dropShadowAlpha(){return this._dropShadowAlpha}set dropShadowAlpha(t){this._dropShadowAlpha!==t&&(this._dropShadowAlpha=t,this.styleID++)}get dropShadowAngle(){return this._dropShadowAngle}set dropShadowAngle(t){this._dropShadowAngle!==t&&(this._dropShadowAngle=t,this.styleID++)}get dropShadowBlur(){return this._dropShadowBlur}set dropShadowBlur(t){this._dropShadowBlur!==t&&(this._dropShadowBlur=t,this.styleID++)}get dropShadowColor(){return this._dropShadowColor}set dropShadowColor(t){const e=an(t);this._dropShadowColor!==e&&(this._dropShadowColor=e,this.styleID++)}get dropShadowDistance(){return this._dropShadowDistance}set dropShadowDistance(t){this._dropShadowDistance!==t&&(this._dropShadowDistance=t,this.styleID++)}get fill(){return this._fill}set fill(t){const e=an(t);this._fill!==e&&(this._fill=e,this.styleID++)}get fillGradientType(){return this._fillGradientType}set fillGradientType(t){this._fillGradientType!==t&&(this._fillGradientType=t,this.styleID++)}get fillGradientStops(){return this._fillGradientStops}set fillGradientStops(t){bf(this._fillGradientStops,t)||(this._fillGradientStops=t,this.styleID++)}get fontFamily(){return this._fontFamily}set fontFamily(t){this.fontFamily!==t&&(this._fontFamily=t,this.styleID++)}get fontSize(){return this._fontSize}set fontSize(t){this._fontSize!==t&&(this._fontSize=t,this.styleID++)}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle!==t&&(this._fontStyle=t,this.styleID++)}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant!==t&&(this._fontVariant=t,this.styleID++)}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight!==t&&(this._fontWeight=t,this.styleID++)}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.styleID++)}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight!==t&&(this._lineHeight=t,this.styleID++)}get leading(){return this._leading}set leading(t){this._leading!==t&&(this._leading=t,this.styleID++)}get lineJoin(){return this._lineJoin}set lineJoin(t){this._lineJoin!==t&&(this._lineJoin=t,this.styleID++)}get miterLimit(){return this._miterLimit}set miterLimit(t){this._miterLimit!==t&&(this._miterLimit=t,this.styleID++)}get padding(){return this._padding}set padding(t){this._padding!==t&&(this._padding=t,this.styleID++)}get stroke(){return this._stroke}set stroke(t){const e=an(t);this._stroke!==e&&(this._stroke=e,this.styleID++)}get strokeThickness(){return this._strokeThickness}set strokeThickness(t){this._strokeThickness!==t&&(this._strokeThickness=t,this.styleID++)}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline!==t&&(this._textBaseline=t,this.styleID++)}get trim(){return this._trim}set trim(t){this._trim!==t&&(this._trim=t,this.styleID++)}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace!==t&&(this._whiteSpace=t,this.styleID++)}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap!==t&&(this._wordWrap=t,this.styleID++)}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth!==t&&(this._wordWrapWidth=t,this.styleID++)}toFontString(){const t=typeof this.fontSize=="number"?`${this.fontSize}px`:this.fontSize;let e=this.fontFamily;Array.isArray(this.fontFamily)||(e=this.fontFamily.split(","));for(let s=e.length-1;s>=0;s--){let r=e[s].trim();!/([\"\'])[^\'\"]+\1/.test(r)&&!Tf.includes(r)&&(r=`"${r}"`),e[s]=r}return`${this.fontStyle} ${this.fontVariant} ${this.fontWeight} ${t} ${e.join(",")}`}}function pa(i){return typeof i=="number"?Yi(i):(typeof i=="string"&&i.startsWith("0x")&&(i=i.replace("0x","#")),i)}function an(i){if(Array.isArray(i)){for(let t=0;t<i.length;++t)i[t]=pa(i[t]);return i}else return pa(i)}function bf(i,t){if(!Array.isArray(i)||!Array.isArray(t)||i.length!==t.length)return!1;for(let e=0;e<i.length;++e)if(i[e]!==t[e])return!1;return!0}function hn(i,t,e){for(const s in e)Array.isArray(t[s])?i[s]=t[s].slice():i[s]=t[s]}const vs={willReadFrequently:!0};class G{constructor(t,e,s,r,n,o,a,h,l){this.text=t,this.style=e,this.width=s,this.height=r,this.lines=n,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=h,this.fontProperties=l}static measureText(t,e,s,r=G._canvas){s=s==null?e.wordWrap:s;const n=e.toFontString(),o=G.measureFont(n);o.fontSize===0&&(o.fontSize=e.fontSize,o.ascent=e.fontSize);const a=r.getContext("2d",vs);a.font=n;const l=(s?G.wordWrap(t,e,r):t).split(/(?:\r\n|\r|\n)/),c=new Array(l.length);let u=0;for(let g=0;g<l.length;g++){const m=a.measureText(l[g]).width+(l[g].length-1)*e.letterSpacing;c[g]=m,u=Math.max(u,m)}let d=u+e.strokeThickness;e.dropShadow&&(d+=e.dropShadowDistance);const f=e.lineHeight||o.fontSize+e.strokeThickness;let p=Math.max(f,o.fontSize+e.strokeThickness*2)+(l.length-1)*(f+e.leading);return e.dropShadow&&(p+=e.dropShadowDistance),new G(t,e,d,p,l,c,f+e.leading,u,o)}static wordWrap(t,e,s=G._canvas){const r=s.getContext("2d",vs);let n=0,o="",a="";const h=Object.create(null),{letterSpacing:l,whiteSpace:c}=e,u=G.collapseSpaces(c),d=G.collapseNewlines(c);let f=!u;const p=e.wordWrapWidth+l,g=G.tokenize(t);for(let m=0;m<g.length;m++){let x=g[m];if(G.isNewline(x)){if(!d){a+=G.addLine(o),f=!u,o="",n=0;continue}x=" "}if(u){const _=G.isBreakingSpace(x),y=G.isBreakingSpace(o[o.length-1]);if(_&&y)continue}const T=G.getFromCache(x,l,h,r);if(T>p)if(o!==""&&(a+=G.addLine(o),o="",n=0),G.canBreakWords(x,e.breakWords)){const _=G.wordWrapSplit(x);for(let y=0;y<_.length;y++){let E=_[y],B=1;for(;_[y+B];){const b=_[y+B],I=E[E.length-1];if(!G.canBreakChars(I,b,x,y,e.breakWords))E+=b;else break;B++}y+=E.length-1;const A=G.getFromCache(E,l,h,r);A+n>p&&(a+=G.addLine(o),f=!1,o="",n=0),o+=E,n+=A}}else{o.length>0&&(a+=G.addLine(o),o="",n=0);const _=m===g.length-1;a+=G.addLine(x,!_),f=!1,o="",n=0}else T+n>p&&(f=!1,a+=G.addLine(o),o="",n=0),(o.length>0||!G.isBreakingSpace(x)||f)&&(o+=x,n+=T)}return a+=G.addLine(o,!1),a}static addLine(t,e=!0){return t=G.trimRight(t),t=e?`${t}
`:t,t}static getFromCache(t,e,s,r){let n=s[t];if(typeof n!="number"){const o=t.length*e;n=r.measureText(t).width+o,s[t]=n}return n}static collapseSpaces(t){return t==="normal"||t==="pre-line"}static collapseNewlines(t){return t==="normal"}static trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const s=t[e];if(!G.isBreakingSpace(s))break;t=t.slice(0,-1)}return t}static isNewline(t){return typeof t!="string"?!1:G._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:G._breakingSpaces.includes(t.charCodeAt(0))}static tokenize(t){const e=[];let s="";if(typeof t!="string")return e;for(let r=0;r<t.length;r++){const n=t[r],o=t[r+1];if(G.isBreakingSpace(n,o)||G.isNewline(n)){s!==""&&(e.push(s),s=""),e.push(n);continue}s+=n}return s!==""&&e.push(s),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,s,r,n){return!0}static wordWrapSplit(t){return t.split("")}static measureFont(t){if(G._fonts[t])return G._fonts[t];const e={ascent:0,descent:0,fontSize:0},s=G._canvas,r=G._context;r.font=t;const n=G.METRICS_STRING+G.BASELINE_SYMBOL,o=Math.ceil(r.measureText(n).width);let a=Math.ceil(r.measureText(G.BASELINE_SYMBOL).width);const h=Math.ceil(G.HEIGHT_MULTIPLIER*a);a=a*G.BASELINE_MULTIPLIER|0,s.width=o,s.height=h,r.fillStyle="#f00",r.fillRect(0,0,o,h),r.font=t,r.textBaseline="alphabetic",r.fillStyle="#000",r.fillText(n,0,a);const l=r.getImageData(0,0,o,h).data,c=l.length,u=o*4;let d=0,f=0,p=!1;for(d=0;d<a;++d){for(let g=0;g<u;g+=4)if(l[f+g]!==255){p=!0;break}if(!p)f+=u;else break}for(e.ascent=a-d,f=c-u,p=!1,d=h;d>a;--d){for(let g=0;g<u;g+=4)if(l[f+g]!==255){p=!0;break}if(!p)f-=u;else break}return e.descent=d-a,e.fontSize=e.ascent+e.descent,G._fonts[t]=e,e}static clearMetrics(t=""){t?delete G._fonts[t]:G._fonts={}}static get _canvas(){if(!G.__canvas){let t;try{const e=new OffscreenCanvas(0,0),s=e.getContext("2d",vs);if(s!=null&&s.measureText)return G.__canvas=e,e;t=P.ADAPTER.createCanvas()}catch(e){t=P.ADAPTER.createCanvas()}t.width=t.height=10,G.__canvas=t}return G.__canvas}static get _context(){return G.__context||(G.__context=G._canvas.getContext("2d",vs)),G.__context}}G._fonts={},G.METRICS_STRING="|\xC9q\xC5",G.BASELINE_SYMBOL="M",G.BASELINE_MULTIPLIER=1.4,G.HEIGHT_MULTIPLIER=2,G._newlines=[10,13],G._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];const Ef={texture:!0,children:!1,baseTexture:!0},ma=class extends jt{constructor(i,t,e){let s=!1;e||(e=P.ADAPTER.createCanvas(),s=!0),e.width=3,e.height=3;const r=L.from(e);r.orig=new j,r.trim=new j,super(r),this._ownCanvas=s,this.canvas=e,this.context=e.getContext("2d",{willReadFrequently:!0}),this._resolution=P.RESOLUTION,this._autoResolution=!0,this._text=null,this._style=null,this._styleListener=null,this._font="",this.text=i,this.style=t,this.localStyleID=-1}updateText(i){const t=this._style;if(this.localStyleID!==t.styleID&&(this.dirty=!0,this.localStyleID=t.styleID),!this.dirty&&i)return;this._font=this._style.toFontString();const e=this.context,s=G.measureText(this._text||" ",this._style,this._style.wordWrap,this.canvas),r=s.width,n=s.height,o=s.lines,a=s.lineHeight,h=s.lineWidths,l=s.maxLineWidth,c=s.fontProperties;this.canvas.width=Math.ceil(Math.ceil(Math.max(1,r)+t.padding*2)*this._resolution),this.canvas.height=Math.ceil(Math.ceil(Math.max(1,n)+t.padding*2)*this._resolution),e.scale(this._resolution,this._resolution),e.clearRect(0,0,this.canvas.width,this.canvas.height),e.font=this._font,e.lineWidth=t.strokeThickness,e.textBaseline=t.textBaseline,e.lineJoin=t.lineJoin,e.miterLimit=t.miterLimit;let u,d;const f=t.dropShadow?2:1;for(let p=0;p<f;++p){const g=t.dropShadow&&p===0,m=g?Math.ceil(Math.max(1,n)+t.padding*2):0,x=m*this._resolution;if(g){e.fillStyle="black",e.strokeStyle="black";const _=t.dropShadowColor,y=ae(typeof _=="number"?_:$i(_)),E=t.dropShadowBlur*this._resolution,B=t.dropShadowDistance*this._resolution;e.shadowColor=`rgba(${y[0]*255},${y[1]*255},${y[2]*255},${t.dropShadowAlpha})`,e.shadowBlur=E,e.shadowOffsetX=Math.cos(t.dropShadowAngle)*B,e.shadowOffsetY=Math.sin(t.dropShadowAngle)*B+x}else e.fillStyle=this._generateFillStyle(t,o,s),e.strokeStyle=t.stroke,e.shadowColor="black",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0;let T=(a-c.fontSize)/2;a-c.fontSize<0&&(T=0);for(let _=0;_<o.length;_++)u=t.strokeThickness/2,d=t.strokeThickness/2+_*a+c.ascent+T,t.align==="right"?u+=l-h[_]:t.align==="center"&&(u+=(l-h[_])/2),t.stroke&&t.strokeThickness&&this.drawLetterSpacing(o[_],u+t.padding,d+t.padding-m,!0),t.fill&&this.drawLetterSpacing(o[_],u+t.padding,d+t.padding-m)}this.updateTexture()}drawLetterSpacing(i,t,e,s=!1){const n=this._style.letterSpacing,o=ma.experimentalLetterSpacing&&("letterSpacing"in CanvasRenderingContext2D.prototype||"textLetterSpacing"in CanvasRenderingContext2D.prototype);if(n===0||o){o&&(this.context.letterSpacing=n,this.context.textLetterSpacing=n),s?this.context.strokeText(i,t,e):this.context.fillText(i,t,e);return}let a=t;const h=Array.from?Array.from(i):i.split("");let l=this.context.measureText(i).width,c=0;for(let u=0;u<h.length;++u){const d=h[u];s?this.context.strokeText(d,a,e):this.context.fillText(d,a,e);let f="";for(let p=u+1;p<h.length;++p)f+=h[p];c=this.context.measureText(f).width,a+=l-c+n,l=c}}updateTexture(){const i=this.canvas;if(this._style.trim){const n=Ao(i);n.data&&(i.width=n.width,i.height=n.height,this.context.putImageData(n.data,0,0))}const t=this._texture,e=this._style,s=e.trim?0:e.padding,r=t.baseTexture;t.trim.width=t._frame.width=i.width/this._resolution,t.trim.height=t._frame.height=i.height/this._resolution,t.trim.x=-s,t.trim.y=-s,t.orig.width=t._frame.width-s*2,t.orig.height=t._frame.height-s*2,this._onTextureUpdate(),r.setRealSize(i.width,i.height,this._resolution),t.updateUvs(),this.dirty=!1}_render(i){this._autoResolution&&this._resolution!==i.resolution&&(this._resolution=i.resolution,this.dirty=!0),this.updateText(!0),super._render(i)}updateTransform(){this.updateText(!0),super.updateTransform()}getBounds(i,t){return this.updateText(!0),this._textureID===-1&&(i=!1),super.getBounds(i,t)}getLocalBounds(i){return this.updateText(!0),super.getLocalBounds.call(this,i)}_calculateBounds(){this.calculateVertices(),this._bounds.addQuad(this.vertexData)}_generateFillStyle(i,t,e){const s=i.fill;if(Array.isArray(s)){if(s.length===1)return s[0]}else return s;let r;const n=i.dropShadow?i.dropShadowDistance:0,o=i.padding||0,a=this.canvas.width/this._resolution-n-o*2,h=this.canvas.height/this._resolution-n-o*2,l=s.slice(),c=i.fillGradientStops.slice();if(!c.length){const u=l.length+1;for(let d=1;d<u;++d)c.push(d/u)}if(l.unshift(s[0]),c.unshift(0),l.push(s[s.length-1]),c.push(1),i.fillGradientType===mi.LINEAR_VERTICAL){r=this.context.createLinearGradient(a/2,o,a/2,h+o);const u=e.fontProperties.fontSize+i.strokeThickness;for(let d=0;d<t.length;d++){const f=e.lineHeight*(d-1)+u,p=e.lineHeight*d;let g=p;d>0&&f>p&&(g=(p+f)/2);const m=p+u,x=e.lineHeight*(d+1);let T=m;d+1<t.length&&x<m&&(T=(m+x)/2);const _=(T-g)/h;for(let y=0;y<l.length;y++){let E=0;typeof c[y]=="number"?E=c[y]:E=y/l.length;let B=Math.min(1,Math.max(0,g/h+E*_));B=Number(B.toFixed(5)),r.addColorStop(B,l[y])}}}else{r=this.context.createLinearGradient(o,h/2,a+o,h/2);const u=l.length+1;let d=1;for(let f=0;f<l.length;f++){let p;typeof c[f]=="number"?p=c[f]:p=d/u,r.addColorStop(p,l[f]),d++}}return r}destroy(i){typeof i=="boolean"&&(i={children:i}),i=Object.assign({},Ef,i),super.destroy(i),this._ownCanvas&&(this.canvas.height=this.canvas.width=0),this.context=null,this.canvas=null,this._style=null}get width(){return this.updateText(!0),Math.abs(this.scale.x)*this._texture.orig.width}set width(i){this.updateText(!0);const t=Ce(this.scale.x)||1;this.scale.x=t*i/this._texture.orig.width,this._width=i}get height(){return this.updateText(!0),Math.abs(this.scale.y)*this._texture.orig.height}set height(i){this.updateText(!0);const t=Ce(this.scale.y)||1;this.scale.y=t*i/this._texture.orig.height,this._height=i}get style(){return this._style}set style(i){i=i||{},i instanceof ue?this._style=i:this._style=new ue(i),this.localStyleID=-1,this.dirty=!0}get text(){return this._text}set text(i){i=String(i==null?"":i),this._text!==i&&(this._text=i,this.dirty=!0)}get resolution(){return this._resolution}set resolution(i){this._autoResolution=!1,this._resolution!==i&&(this._resolution=i,this.dirty=!0)}};let gi=ma;gi.experimentalLetterSpacing=!1,gi.prototype._renderCanvas=function(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0),this.updateText(!0),jt.prototype._renderCanvas.call(this,t)};var wf=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform float uAlpha;

void main(void)
{
   gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha;
}
`;class Sf extends Ut{constructor(t=1){super(aa,wf,{uAlpha:1}),this.alpha=t}get alpha(){return this.uniforms.uAlpha}set alpha(t){this.uniforms.uAlpha=t}}const Af=`
    attribute vec2 aVertexPosition;

    uniform mat3 projectionMatrix;

    uniform float strength;

    varying vec2 vBlurTexCoords[%size%];

    uniform vec4 inputSize;
    uniform vec4 outputFrame;

    vec4 filterVertexPosition( void )
    {
        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
    }

    vec2 filterTextureCoord( void )
    {
        return aVertexPosition * (outputFrame.zw * inputSize.zw);
    }

    void main(void)
    {
        gl_Position = filterVertexPosition();

        vec2 textureCoord = filterTextureCoord();
        %blur%
    }`;function Cf(i,t){const e=Math.ceil(i/2);let s=Af,r="",n;t?n="vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);":n="vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);";for(let o=0;o<i;o++){let a=n.replace("%index%",o.toString());a=a.replace("%sampleIndex%",`${o-(e-1)}.0`),r+=a,r+=`
`}return s=s.replace("%blur%",r),s=s.replace("%size%",i.toString()),s}const Rf={5:[.153388,.221461,.250301],7:[.071303,.131514,.189879,.214607],9:[.028532,.067234,.124009,.179044,.20236],11:[.0093,.028002,.065984,.121703,.175713,.198596],13:[.002406,.009255,.027867,.065666,.121117,.174868,.197641],15:[489e-6,.002403,.009246,.02784,.065602,.120999,.174697,.197448]},If=["varying vec2 vBlurTexCoords[%size%];","uniform sampler2D uSampler;","void main(void)","{","    gl_FragColor = vec4(0.0);","    %blur%","}"].join(`
`);function Pf(i){const t=Rf[i],e=t.length;let s=If,r="";const n="gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;";let o;for(let a=0;a<i;a++){let h=n.replace("%index%",a.toString());o=a,a>=e&&(o=i-a-1),h=h.replace("%value%",t[o].toString()),r+=h,r+=`
`}return s=s.replace("%blur%",r),s=s.replace("%size%",i.toString()),s}class ln extends Ut{constructor(t,e=8,s=4,r=P.FILTER_RESOLUTION,n=5){const o=Cf(n,t),a=Pf(n);super(o,a),this.horizontal=t,this.resolution=r,this._quality=0,this.quality=s,this.blur=e}apply(t,e,s,r){if(s?this.horizontal?this.uniforms.strength=1/s.width*(s.width/e.width):this.uniforms.strength=1/s.height*(s.height/e.height):this.horizontal?this.uniforms.strength=1/t.renderer.width*(t.renderer.width/e.width):this.uniforms.strength=1/t.renderer.height*(t.renderer.height/e.height),this.uniforms.strength*=this.strength,this.uniforms.strength/=this.passes,this.passes===1)t.applyFilter(this,e,s,r);else{const n=t.getFilterTexture(),o=t.renderer;let a=e,h=n;this.state.blend=!1,t.applyFilter(this,a,h,Ht.CLEAR);for(let l=1;l<this.passes-1;l++){t.bindAndClear(a,Ht.BLIT),this.uniforms.uSampler=h;const c=h;h=a,a=c,o.shader.bind(this),o.geometry.draw(5)}this.state.blend=!0,t.applyFilter(this,h,s,r),t.returnFilterTexture(n)}}get blur(){return this.strength}set blur(t){this.padding=1+Math.abs(t)*2,this.strength=t}get quality(){return this._quality}set quality(t){this._quality=t,this.passes=t}}class Mf extends Ut{constructor(t=8,e=4,s=P.FILTER_RESOLUTION,r=5){super(),this.blurXFilter=new ln(!0,t,e,s,r),this.blurYFilter=new ln(!1,t,e,s,r),this.resolution=s,this.quality=e,this.blur=t,this.repeatEdgePixels=!1}apply(t,e,s,r){const n=Math.abs(this.blurXFilter.strength),o=Math.abs(this.blurYFilter.strength);if(n&&o){const a=t.getFilterTexture();this.blurXFilter.apply(t,e,a,Ht.CLEAR),this.blurYFilter.apply(t,a,s,r),t.returnFilterTexture(a)}else o?this.blurYFilter.apply(t,e,s,r):this.blurXFilter.apply(t,e,s,r)}updatePadding(){this._repeatEdgePixels?this.padding=0:this.padding=Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))*2}get blur(){return this.blurXFilter.blur}set blur(t){this.blurXFilter.blur=this.blurYFilter.blur=t,this.updatePadding()}get quality(){return this.blurXFilter.quality}set quality(t){this.blurXFilter.quality=this.blurYFilter.quality=t}get blurX(){return this.blurXFilter.blur}set blurX(t){this.blurXFilter.blur=t,this.updatePadding()}get blurY(){return this.blurYFilter.blur}set blurY(t){this.blurYFilter.blur=t,this.updatePadding()}get blendMode(){return this.blurYFilter.blendMode}set blendMode(t){this.blurYFilter.blendMode=t}get repeatEdgePixels(){return this._repeatEdgePixels}set repeatEdgePixels(t){this._repeatEdgePixels=t,this.updatePadding()}}var Bf=`varying vec2 vTextureCoord;
uniform sampler2D uSampler;
uniform float m[20];
uniform float uAlpha;

void main(void)
{
    vec4 c = texture2D(uSampler, vTextureCoord);

    if (uAlpha == 0.0) {
        gl_FragColor = c;
        return;
    }

    // Un-premultiply alpha before applying the color matrix. See issue #3539.
    if (c.a > 0.0) {
      c.rgb /= c.a;
    }

    vec4 result;

    result.r = (m[0] * c.r);
        result.r += (m[1] * c.g);
        result.r += (m[2] * c.b);
        result.r += (m[3] * c.a);
        result.r += m[4];

    result.g = (m[5] * c.r);
        result.g += (m[6] * c.g);
        result.g += (m[7] * c.b);
        result.g += (m[8] * c.a);
        result.g += m[9];

    result.b = (m[10] * c.r);
       result.b += (m[11] * c.g);
       result.b += (m[12] * c.b);
       result.b += (m[13] * c.a);
       result.b += m[14];

    result.a = (m[15] * c.r);
       result.a += (m[16] * c.g);
       result.a += (m[17] * c.b);
       result.a += (m[18] * c.a);
       result.a += m[19];

    vec3 rgb = mix(c.rgb, result.rgb, uAlpha);

    // Premultiply alpha again.
    rgb *= result.a;

    gl_FragColor = vec4(rgb, result.a);
}
`;class cn extends Ut{constructor(){const t={m:new Float32Array([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0]),uAlpha:1};super(jr,Bf,t),this.alpha=1}_loadMatrix(t,e=!1){let s=t;e&&(this._multiply(s,this.uniforms.m,t),s=this._colorMatrix(s)),this.uniforms.m=s}_multiply(t,e,s){return t[0]=e[0]*s[0]+e[1]*s[5]+e[2]*s[10]+e[3]*s[15],t[1]=e[0]*s[1]+e[1]*s[6]+e[2]*s[11]+e[3]*s[16],t[2]=e[0]*s[2]+e[1]*s[7]+e[2]*s[12]+e[3]*s[17],t[3]=e[0]*s[3]+e[1]*s[8]+e[2]*s[13]+e[3]*s[18],t[4]=e[0]*s[4]+e[1]*s[9]+e[2]*s[14]+e[3]*s[19]+e[4],t[5]=e[5]*s[0]+e[6]*s[5]+e[7]*s[10]+e[8]*s[15],t[6]=e[5]*s[1]+e[6]*s[6]+e[7]*s[11]+e[8]*s[16],t[7]=e[5]*s[2]+e[6]*s[7]+e[7]*s[12]+e[8]*s[17],t[8]=e[5]*s[3]+e[6]*s[8]+e[7]*s[13]+e[8]*s[18],t[9]=e[5]*s[4]+e[6]*s[9]+e[7]*s[14]+e[8]*s[19]+e[9],t[10]=e[10]*s[0]+e[11]*s[5]+e[12]*s[10]+e[13]*s[15],t[11]=e[10]*s[1]+e[11]*s[6]+e[12]*s[11]+e[13]*s[16],t[12]=e[10]*s[2]+e[11]*s[7]+e[12]*s[12]+e[13]*s[17],t[13]=e[10]*s[3]+e[11]*s[8]+e[12]*s[13]+e[13]*s[18],t[14]=e[10]*s[4]+e[11]*s[9]+e[12]*s[14]+e[13]*s[19]+e[14],t[15]=e[15]*s[0]+e[16]*s[5]+e[17]*s[10]+e[18]*s[15],t[16]=e[15]*s[1]+e[16]*s[6]+e[17]*s[11]+e[18]*s[16],t[17]=e[15]*s[2]+e[16]*s[7]+e[17]*s[12]+e[18]*s[17],t[18]=e[15]*s[3]+e[16]*s[8]+e[17]*s[13]+e[18]*s[18],t[19]=e[15]*s[4]+e[16]*s[9]+e[17]*s[14]+e[18]*s[19]+e[19],t}_colorMatrix(t){const e=new Float32Array(t);return e[4]/=255,e[9]/=255,e[14]/=255,e[19]/=255,e}brightness(t,e){const s=[t,0,0,0,0,0,t,0,0,0,0,0,t,0,0,0,0,0,1,0];this._loadMatrix(s,e)}tint(t,e){const s=t>>16&255,r=t>>8&255,n=t&255,o=[s/255,0,0,0,0,0,r/255,0,0,0,0,0,n/255,0,0,0,0,0,1,0];this._loadMatrix(o,e)}greyscale(t,e){const s=[t,t,t,0,0,t,t,t,0,0,t,t,t,0,0,0,0,0,1,0];this._loadMatrix(s,e)}blackAndWhite(t){const e=[.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0];this._loadMatrix(e,t)}hue(t,e){t=(t||0)/180*Math.PI;const s=Math.cos(t),r=Math.sin(t),n=Math.sqrt,o=1/3,a=n(o),h=s+(1-s)*o,l=o*(1-s)-a*r,c=o*(1-s)+a*r,u=o*(1-s)+a*r,d=s+o*(1-s),f=o*(1-s)-a*r,p=o*(1-s)-a*r,g=o*(1-s)+a*r,m=s+o*(1-s),x=[h,l,c,0,0,u,d,f,0,0,p,g,m,0,0,0,0,0,1,0];this._loadMatrix(x,e)}contrast(t,e){const s=(t||0)+1,r=-.5*(s-1),n=[s,0,0,0,r,0,s,0,0,r,0,0,s,0,r,0,0,0,1,0];this._loadMatrix(n,e)}saturate(t=0,e){const s=t*2/3+1,r=(s-1)*-.5,n=[s,r,r,0,0,r,s,r,0,0,r,r,s,0,0,0,0,0,1,0];this._loadMatrix(n,e)}desaturate(){this.saturate(-1)}negative(t){const e=[-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0];this._loadMatrix(e,t)}sepia(t){const e=[.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0];this._loadMatrix(e,t)}technicolor(t){const e=[1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0];this._loadMatrix(e,t)}polaroid(t){const e=[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0];this._loadMatrix(e,t)}toBGR(t){const e=[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0];this._loadMatrix(e,t)}kodachrome(t){const e=[1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0];this._loadMatrix(e,t)}browni(t){const e=[.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0];this._loadMatrix(e,t)}vintage(t){const e=[.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0];this._loadMatrix(e,t)}colorTone(t,e,s,r,n){t=t||.2,e=e||.15,s=s||16770432,r=r||3375104;const o=(s>>16&255)/255,a=(s>>8&255)/255,h=(s&255)/255,l=(r>>16&255)/255,c=(r>>8&255)/255,u=(r&255)/255,d=[.3,.59,.11,0,0,o,a,h,t,0,l,c,u,e,0,o-l,a-c,h-u,0,0];this._loadMatrix(d,n)}night(t,e){t=t||.1;const s=[t*-2,-t,0,0,0,-t,0,t,0,0,0,t,t*2,0,0,0,0,0,1,0];this._loadMatrix(s,e)}predator(t,e){const s=[11.224130630493164*t,-4.794486999511719*t,-2.8746118545532227*t,0*t,.40342438220977783*t,-3.6330697536468506*t,9.193157196044922*t,-2.951810836791992*t,0*t,-1.316135048866272*t,-3.2184197902679443*t,-4.2375030517578125*t,7.476448059082031*t,0*t,.8044459223747253*t,0,0,0,1,0];this._loadMatrix(s,e)}lsd(t){const e=[2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0];this._loadMatrix(e,t)}reset(){const t=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0];this._loadMatrix(t,!1)}get matrix(){return this.uniforms.m}set matrix(t){this.uniforms.m=t}get alpha(){return this.uniforms.uAlpha}set alpha(t){this.uniforms.uAlpha=t}}cn.prototype.grayscale=cn.prototype.greyscale;var Df=`varying vec2 vFilterCoord;
varying vec2 vTextureCoord;

uniform vec2 scale;
uniform mat2 rotation;
uniform sampler2D uSampler;
uniform sampler2D mapSampler;

uniform highp vec4 inputSize;
uniform vec4 inputClamp;

void main(void)
{
  vec4 map =  texture2D(mapSampler, vFilterCoord);

  map -= 0.5;
  map.xy = scale * inputSize.zw * (rotation * map.xy);

  gl_FragColor = texture2D(uSampler, clamp(vec2(vTextureCoord.x + map.x, vTextureCoord.y + map.y), inputClamp.xy, inputClamp.zw));
}
`,Nf=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;
uniform mat3 filterMatrix;

varying vec2 vTextureCoord;
varying vec2 vFilterCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
	gl_Position = filterVertexPosition();
	vTextureCoord = filterTextureCoord();
	vFilterCoord = ( filterMatrix * vec3( vTextureCoord, 1.0)  ).xy;
}
`;class Ff extends Ut{constructor(t,e){const s=new Z;t.renderable=!1,super(Nf,Df,{mapSampler:t._texture,filterMatrix:s,scale:{x:1,y:1},rotation:new Float32Array([1,0,0,1])}),this.maskSprite=t,this.maskMatrix=s,e==null&&(e=20),this.scale=new W(e,e)}apply(t,e,s,r){this.uniforms.filterMatrix=t.calculateSpriteMatrix(this.maskMatrix,this.maskSprite),this.uniforms.scale.x=this.scale.x,this.uniforms.scale.y=this.scale.y;const n=this.maskSprite.worldTransform,o=Math.sqrt(n.a*n.a+n.b*n.b),a=Math.sqrt(n.c*n.c+n.d*n.d);o!==0&&a!==0&&(this.uniforms.rotation[0]=n.a/o,this.uniforms.rotation[1]=n.b/o,this.uniforms.rotation[2]=n.c/a,this.uniforms.rotation[3]=n.d/a),t.applyFilter(this,e,s,r)}get map(){return this.uniforms.mapSampler}set map(t){this.uniforms.mapSampler=t}}var Of=`
attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 v_rgbNW;
varying vec2 v_rgbNE;
varying vec2 v_rgbSW;
varying vec2 v_rgbSE;
varying vec2 v_rgbM;

varying vec2 vFragCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

void texcoords(vec2 fragCoord, vec2 inverseVP,
               out vec2 v_rgbNW, out vec2 v_rgbNE,
               out vec2 v_rgbSW, out vec2 v_rgbSE,
               out vec2 v_rgbM) {
    v_rgbNW = (fragCoord + vec2(-1.0, -1.0)) * inverseVP;
    v_rgbNE = (fragCoord + vec2(1.0, -1.0)) * inverseVP;
    v_rgbSW = (fragCoord + vec2(-1.0, 1.0)) * inverseVP;
    v_rgbSE = (fragCoord + vec2(1.0, 1.0)) * inverseVP;
    v_rgbM = vec2(fragCoord * inverseVP);
}

void main(void) {

   gl_Position = filterVertexPosition();

   vFragCoord = aVertexPosition * outputFrame.zw;

   texcoords(vFragCoord, inputSize.zw, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);
}
`,Lf=`varying vec2 v_rgbNW;
varying vec2 v_rgbNE;
varying vec2 v_rgbSW;
varying vec2 v_rgbSE;
varying vec2 v_rgbM;

varying vec2 vFragCoord;
uniform sampler2D uSampler;
uniform highp vec4 inputSize;


/**
 Basic FXAA implementation based on the code on geeks3d.com with the
 modification that the texture2DLod stuff was removed since it's
 unsupported by WebGL.

 --

 From:
 https://github.com/mitsuhiko/webgl-meincraft

 Copyright (c) 2011 by Armin Ronacher.

 Some rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are
 met:

 * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.

 * Redistributions in binary form must reproduce the above
 copyright notice, this list of conditions and the following
 disclaimer in the documentation and/or other materials provided
 with the distribution.

 * The names of the contributors may not be used to endorse or
 promote products derived from this software without specific
 prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef FXAA_REDUCE_MIN
#define FXAA_REDUCE_MIN   (1.0/ 128.0)
#endif
#ifndef FXAA_REDUCE_MUL
#define FXAA_REDUCE_MUL   (1.0 / 8.0)
#endif
#ifndef FXAA_SPAN_MAX
#define FXAA_SPAN_MAX     8.0
#endif

//optimized version for mobile, where dependent
//texture reads can be a bottleneck
vec4 fxaa(sampler2D tex, vec2 fragCoord, vec2 inverseVP,
          vec2 v_rgbNW, vec2 v_rgbNE,
          vec2 v_rgbSW, vec2 v_rgbSE,
          vec2 v_rgbM) {
    vec4 color;
    vec3 rgbNW = texture2D(tex, v_rgbNW).xyz;
    vec3 rgbNE = texture2D(tex, v_rgbNE).xyz;
    vec3 rgbSW = texture2D(tex, v_rgbSW).xyz;
    vec3 rgbSE = texture2D(tex, v_rgbSE).xyz;
    vec4 texColor = texture2D(tex, v_rgbM);
    vec3 rgbM  = texColor.xyz;
    vec3 luma = vec3(0.299, 0.587, 0.114);
    float lumaNW = dot(rgbNW, luma);
    float lumaNE = dot(rgbNE, luma);
    float lumaSW = dot(rgbSW, luma);
    float lumaSE = dot(rgbSE, luma);
    float lumaM  = dot(rgbM,  luma);
    float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));
    float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));

    mediump vec2 dir;
    dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));
    dir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));

    float dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) *
                          (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);

    float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);
    dir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX),
              max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),
                  dir * rcpDirMin)) * inverseVP;

    vec3 rgbA = 0.5 * (
                       texture2D(tex, fragCoord * inverseVP + dir * (1.0 / 3.0 - 0.5)).xyz +
                       texture2D(tex, fragCoord * inverseVP + dir * (2.0 / 3.0 - 0.5)).xyz);
    vec3 rgbB = rgbA * 0.5 + 0.25 * (
                                     texture2D(tex, fragCoord * inverseVP + dir * -0.5).xyz +
                                     texture2D(tex, fragCoord * inverseVP + dir * 0.5).xyz);

    float lumaB = dot(rgbB, luma);
    if ((lumaB < lumaMin) || (lumaB > lumaMax))
        color = vec4(rgbA, texColor.a);
    else
        color = vec4(rgbB, texColor.a);
    return color;
}

void main() {

      vec4 color;

      color = fxaa(uSampler, vFragCoord, inputSize.zw, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);

      gl_FragColor = color;
}
`;class Uf extends Ut{constructor(){super(Of,Lf)}}var kf=`precision highp float;

varying vec2 vTextureCoord;
varying vec4 vColor;

uniform float uNoise;
uniform float uSeed;
uniform sampler2D uSampler;

float rand(vec2 co)
{
    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
}

void main()
{
    vec4 color = texture2D(uSampler, vTextureCoord);
    float randomValue = rand(gl_FragCoord.xy * uSeed);
    float diff = (randomValue - 0.5) * uNoise;

    // Un-premultiply alpha before applying the color matrix. See issue #3539.
    if (color.a > 0.0) {
        color.rgb /= color.a;
    }

    color.r += diff;
    color.g += diff;
    color.b += diff;

    // Premultiply alpha again.
    color.rgb *= color.a;

    gl_FragColor = color;
}
`;class Gf extends Ut{constructor(t=.5,e=Math.random()){super(jr,kf,{uNoise:0,uSeed:0}),this.noise=t,this.seed=e}get noise(){return this.uniforms.uNoise}set noise(t){this.uniforms.uNoise=t}get seed(){return this.uniforms.uSeed}set seed(t){this.uniforms.uSeed=t}}const ga=new Z;at.prototype._cacheAsBitmap=!1,at.prototype._cacheData=null,at.prototype._cacheAsBitmapResolution=null,at.prototype._cacheAsBitmapMultisample=null;class Hf{constructor(){this.textureCacheId=null,this.originalRender=null,this.originalRenderCanvas=null,this.originalCalculateBounds=null,this.originalGetLocalBounds=null,this.originalUpdateTransform=null,this.originalDestroy=null,this.originalMask=null,this.originalFilterArea=null,this.originalContainsPoint=null,this.sprite=null}}Object.defineProperties(at.prototype,{cacheAsBitmapResolution:{get(){return this._cacheAsBitmapResolution},set(i){i!==this._cacheAsBitmapResolution&&(this._cacheAsBitmapResolution=i,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmapMultisample:{get(){return this._cacheAsBitmapMultisample},set(i){i!==this._cacheAsBitmapMultisample&&(this._cacheAsBitmapMultisample=i,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmap:{get(){return this._cacheAsBitmap},set(i){if(this._cacheAsBitmap===i)return;this._cacheAsBitmap=i;let t;i?(this._cacheData||(this._cacheData=new Hf),t=this._cacheData,t.originalRender=this.render,t.originalRenderCanvas=this.renderCanvas,t.originalUpdateTransform=this.updateTransform,t.originalCalculateBounds=this.calculateBounds,t.originalGetLocalBounds=this.getLocalBounds,t.originalDestroy=this.destroy,t.originalContainsPoint=this.containsPoint,t.originalMask=this._mask,t.originalFilterArea=this.filterArea,this.render=this._renderCached,this.renderCanvas=this._renderCachedCanvas,this.destroy=this._cacheAsBitmapDestroy):(t=this._cacheData,t.sprite&&this._destroyCachedDisplayObject(),this.render=t.originalRender,this.renderCanvas=t.originalRenderCanvas,this.calculateBounds=t.originalCalculateBounds,this.getLocalBounds=t.originalGetLocalBounds,this.destroy=t.originalDestroy,this.updateTransform=t.originalUpdateTransform,this.containsPoint=t.originalContainsPoint,this._mask=t.originalMask,this.filterArea=t.originalFilterArea)}}}),at.prototype._renderCached=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObject(t),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._render(t))},at.prototype._initCachedDisplayObject=function(t){var d,f,p;if((d=this._cacheData)!=null&&d.sprite)return;const e=this.alpha;this.alpha=1,t.batch.flush();const s=this.getLocalBounds(null,!0).clone();if((f=this.filters)!=null&&f.length){const g=this.filters[0].padding;s.pad(g)}s.ceil(P.RESOLUTION);const r=t.renderTexture.current,n=t.renderTexture.sourceFrame.clone(),o=t.renderTexture.destinationFrame.clone(),a=t.projection.transform,h=Ot.create({width:s.width,height:s.height,resolution:this.cacheAsBitmapResolution||t.resolution,multisample:(p=this.cacheAsBitmapMultisample)!=null?p:t.multisample}),l=`cacheAsBitmap_${me()}`;this._cacheData.textureCacheId=l,$.addToCache(h.baseTexture,l),L.addToCache(h,l);const c=this.transform.localTransform.copyTo(ga).invert().translate(-s.x,-s.y);this.render=this._cacheData.originalRender,t.render(this,{renderTexture:h,clear:!0,transform:c,skipUpdateTransform:!1}),t.framebuffer.blit(),t.projection.transform=a,t.renderTexture.bind(r,n,o),this.render=this._renderCached,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=e;const u=new jt(h);u.transform.worldTransform=this.transform.worldTransform,u.anchor.x=-(s.x/s.width),u.anchor.y=-(s.y/s.height),u.alpha=e,u._bounds=this._bounds,this._cacheData.sprite=u,this.transform._parentID=-1,this.parent?this.updateTransform():(this.enableTempParent(),this.updateTransform(),this.disableTempParent(null)),this.containsPoint=u.containsPoint.bind(u)},at.prototype._renderCachedCanvas=function(t){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObjectCanvas(t),this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._renderCanvas(t))},at.prototype._initCachedDisplayObjectCanvas=function(t){var c;if((c=this._cacheData)!=null&&c.sprite)return;const e=this.getLocalBounds(null,!0),s=this.alpha;this.alpha=1;const r=t.canvasContext.activeContext,n=t._projTransform;e.ceil(P.RESOLUTION);const o=Ot.create({width:e.width,height:e.height}),a=`cacheAsBitmap_${me()}`;this._cacheData.textureCacheId=a,$.addToCache(o.baseTexture,a),L.addToCache(o,a);const h=ga;this.transform.localTransform.copyTo(h),h.invert(),h.tx-=e.x,h.ty-=e.y,this.renderCanvas=this._cacheData.originalRenderCanvas,t.render(this,{renderTexture:o,clear:!0,transform:h,skipUpdateTransform:!1}),t.canvasContext.activeContext=r,t._projTransform=n,this.renderCanvas=this._renderCachedCanvas,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=s;const l=new jt(o);l.transform.worldTransform=this.transform.worldTransform,l.anchor.x=-(e.x/e.width),l.anchor.y=-(e.y/e.height),l.alpha=s,l._bounds=this._bounds,this._cacheData.sprite=l,this.transform._parentID=-1,this.parent?this.updateTransform():(this.parent=t._tempDisplayObjectParent,this.updateTransform(),this.parent=null),this.containsPoint=l.containsPoint.bind(l)},at.prototype._calculateCachedBounds=function(){this._bounds.clear(),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite._calculateBounds(),this._bounds.updateID=this._boundsID},at.prototype._getCachedLocalBounds=function(){return this._cacheData.sprite.getLocalBounds(null)},at.prototype._destroyCachedDisplayObject=function(){this._cacheData.sprite._texture.destroy(!0),this._cacheData.sprite=null,$.removeFromCache(this._cacheData.textureCacheId),L.removeFromCache(this._cacheData.textureCacheId),this._cacheData.textureCacheId=null},at.prototype._cacheAsBitmapDestroy=function(t){this.cacheAsBitmap=!1,this.destroy(t)},at.prototype.name=null,wt.prototype.getChildByName=function(t,e){for(let s=0,r=this.children.length;s<r;s++)if(this.children[s].name===t)return this.children[s];if(e)for(let s=0,r=this.children.length;s<r;s++){const n=this.children[s];if(!n.getChildByName)continue;const o=n.getChildByName(t,!0);if(o)return o}return null},at.prototype.getGlobalPosition=function(t=new W,e=!1){return this.parent?this.parent.toGlobal(this.position,t,e):(t.x=this.position.x,t.y=this.position.y),t};const _a={accessible:!1,accessibleTitle:null,accessibleHint:null,tabIndex:0,_accessibleActive:!1,_accessibleDiv:null,accessibleType:"button",accessiblePointerEvents:"auto",accessibleChildren:!0,renderId:-1};class We{constructor(t){this.bubbles=!0,this.cancelBubble=!0,this.cancelable=!1,this.composed=!1,this.defaultPrevented=!1,this.eventPhase=We.prototype.NONE,this.propagationStopped=!1,this.propagationImmediatelyStopped=!1,this.layer=new W,this.page=new W,this.AT_TARGET=1,this.BUBBLING_PHASE=2,this.CAPTURING_PHASE=3,this.NONE=0,this.manager=t}get layerX(){return this.layer.x}get layerY(){return this.layer.y}get pageX(){return this.page.x}get pageY(){return this.page.y}get data(){return this}composedPath(){return this.manager&&(!this.path||this.path[this.path.length-1]!==this.target)&&(this.path=this.target?this.manager.propagationPath(this.target):[]),this.path}initEvent(t,e,s){throw new Error("initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}initUIEvent(t,e,s,r,n){throw new Error("initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}preventDefault(){this.nativeEvent instanceof Event&&this.nativeEvent.cancelable&&this.nativeEvent.preventDefault(),this.defaultPrevented=!0}stopImmediatePropagation(){this.propagationImmediatelyStopped=!0}stopPropagation(){this.propagationStopped=!0}}class _i extends We{constructor(){super(...arguments),this.client=new W,this.movement=new W,this.offset=new W,this.global=new W,this.screen=new W}get clientX(){return this.client.x}get clientY(){return this.client.y}get x(){return this.clientX}get y(){return this.clientY}get movementX(){return this.movement.x}get movementY(){return this.movement.y}get offsetX(){return this.offset.x}get offsetY(){return this.offset.y}get globalX(){return this.global.x}get globalY(){return this.global.y}get screenX(){return this.screen.x}get screenY(){return this.screen.y}getModifierState(t){return"getModifierState"in this.nativeEvent&&this.nativeEvent.getModifierState(t)}initMouseEvent(t,e,s,r,n,o,a,h,l,c,u,d,f,p,g){throw new Error("Method not implemented.")}}class kt extends _i{constructor(){super(...arguments),this.width=0,this.height=0,this.isPrimary=!1}getCoalescedEvents(){return this.type==="pointermove"||this.type==="mousemove"||this.type==="touchmove"?[this]:[]}getPredictedEvents(){throw new Error("getPredictedEvents is not supported!")}}class ys extends _i{constructor(){super(...arguments),this.DOM_DELTA_LINE=0,this.DOM_DELTA_PAGE=1,this.DOM_DELTA_PIXEL=2}}const Xf=2048,Vf=new W,un=new W;class va{constructor(t){this.dispatch=new Le,this.moveOnAll=!1,this.mappingState={trackingData:{}},this.eventPool=new Map,this.rootTarget=t,this.hitPruneFn=this.hitPruneFn.bind(this),this.hitTestFn=this.hitTestFn.bind(this),this.mapPointerDown=this.mapPointerDown.bind(this),this.mapPointerMove=this.mapPointerMove.bind(this),this.mapPointerOut=this.mapPointerOut.bind(this),this.mapPointerOver=this.mapPointerOver.bind(this),this.mapPointerUp=this.mapPointerUp.bind(this),this.mapPointerUpOutside=this.mapPointerUpOutside.bind(this),this.mapWheel=this.mapWheel.bind(this),this.mappingTable={},this.addEventMapping("pointerdown",this.mapPointerDown),this.addEventMapping("pointermove",this.mapPointerMove),this.addEventMapping("pointerout",this.mapPointerOut),this.addEventMapping("pointerleave",this.mapPointerOut),this.addEventMapping("pointerover",this.mapPointerOver),this.addEventMapping("pointerup",this.mapPointerUp),this.addEventMapping("pointerupoutside",this.mapPointerUpOutside),this.addEventMapping("wheel",this.mapWheel)}addEventMapping(t,e){this.mappingTable[t]||(this.mappingTable[t]=[]),this.mappingTable[t].push({fn:e,priority:0}),this.mappingTable[t].sort((s,r)=>s.priority-r.priority)}dispatchEvent(t,e){t.propagationStopped=!1,t.propagationImmediatelyStopped=!1,this.propagate(t,e),this.dispatch.emit(e||t.type,t)}mapEvent(t){if(!this.rootTarget)return;const e=this.mappingTable[t.type];if(e)for(let s=0,r=e.length;s<r;s++)e[s].fn(t);else console.warn(`[EventBoundary]: Event mapping not defined for ${t.type}`)}hitTest(t,e){const s=this.hitTestRecursive(this.rootTarget,this.rootTarget.interactive,Vf.set(t,e),this.hitTestFn,this.hitPruneFn);return s&&s[0]}propagate(t,e){if(!t.target)return;const s=t.composedPath();t.eventPhase=t.CAPTURING_PHASE;for(let r=0,n=s.length-1;r<n;r++)if(t.currentTarget=s[r],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return;if(t.eventPhase=t.AT_TARGET,t.currentTarget=t.target,this.notifyTarget(t,e),!(t.propagationStopped||t.propagationImmediatelyStopped)){t.eventPhase=t.BUBBLING_PHASE;for(let r=s.length-2;r>=0;r--)if(t.currentTarget=s[r],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return}}all(t,e,s=this.rootTarget){t.eventPhase=t.BUBBLING_PHASE;const r=s.children;if(r)for(let n=0;n<r.length;n++)this.all(t,e,r[n]);t.currentTarget=s,this.notifyTarget(t,e)}propagationPath(t){const e=[t];for(let s=0;s<Xf&&t!==this.rootTarget;s++){if(!t.parent)throw new Error("Cannot find propagation path to disconnected target");e.push(t.parent),t=t.parent}return e.reverse(),e}hitTestRecursive(t,e,s,r,n){if(!t||!t.visible||n(t,s))return null;if(t.interactiveChildren&&t.children){const o=t.children;for(let a=o.length-1;a>=0;a--){const h=o[a],l=this.hitTestRecursive(h,e||h.interactive,s,r,n);if(l){if(l.length>0&&!l[l.length-1].parent)continue;return(l.length>0||t.interactive)&&l.push(t),l}}}return e&&r(t,s)?t.interactive?[t]:[]:null}hitPruneFn(t,e){var s;if(t.hitArea&&(t.worldTransform.applyInverse(e,un),!t.hitArea.contains(un.x,un.y)))return!0;if(t._mask){const r=t._mask.isMaskData?t._mask.maskObject:t._mask;if(r&&!((s=r.containsPoint)!=null&&s.call(r,e)))return!0}return!1}hitTestFn(t,e){return t.hitArea?!0:t.containsPoint?t.containsPoint(e):!1}notifyTarget(t,e){e=e!=null?e:t.type;const s=t.eventPhase===t.CAPTURING_PHASE||t.eventPhase===t.AT_TARGET?`${e}capture`:e;this.notifyListeners(t,s),t.eventPhase===t.AT_TARGET&&this.notifyListeners(t,e)}mapPointerDown(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.createPointerEvent(t);if(this.dispatchEvent(e,"pointerdown"),e.pointerType==="touch")this.dispatchEvent(e,"touchstart");else if(e.pointerType==="mouse"||e.pointerType==="pen"){const r=e.button===2;this.dispatchEvent(e,r?"rightdown":"mousedown")}const s=this.trackingData(t.pointerId);s.pressTargetsByButton[t.button]=e.composedPath(),this.freeEvent(e)}mapPointerMove(t){var a;if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.createPointerEvent(t),s=e.pointerType==="mouse"||e.pointerType==="pen",r=this.trackingData(t.pointerId),n=this.findMountedTarget(r.overTargets);if(r.overTargets&&n!==e.target){const h=t.type==="mousemove"?"mouseout":"pointerout",l=this.createPointerEvent(t,h,n);if(this.dispatchEvent(l,"pointerout"),s&&this.dispatchEvent(l,"mouseout"),!e.composedPath().includes(n)){const c=this.createPointerEvent(t,"pointerleave",n);for(c.eventPhase=c.AT_TARGET;c.target&&!e.composedPath().includes(c.target);)c.currentTarget=c.target,this.notifyTarget(c),s&&this.notifyTarget(c,"mouseleave"),c.target=c.target.parent;this.freeEvent(c)}this.freeEvent(l)}if(n!==e.target){const h=t.type==="mousemove"?"mouseover":"pointerover",l=this.clonePointerEvent(e,h);this.dispatchEvent(l,"pointerover"),s&&this.dispatchEvent(l,"mouseover");let c=n==null?void 0:n.parent;for(;c&&c!==this.rootTarget.parent&&c!==e.target;)c=c.parent;if(!c||c===this.rootTarget.parent){const d=this.clonePointerEvent(e,"pointerenter");for(d.eventPhase=d.AT_TARGET;d.target&&d.target!==n&&d.target!==this.rootTarget.parent;)d.currentTarget=d.target,this.notifyTarget(d),s&&this.notifyTarget(d,"mouseenter"),d.target=d.target.parent;this.freeEvent(d)}this.freeEvent(l)}const o=this.moveOnAll?"all":"dispatchEvent";this[o](e,"pointermove"),e.pointerType==="touch"&&this[o](e,"touchmove"),s&&(this[o](e,"mousemove"),this.cursor=(a=e.target)==null?void 0:a.cursor),r.overTargets=e.composedPath(),this.freeEvent(e)}mapPointerOver(t){var o;if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.trackingData(t.pointerId),s=this.createPointerEvent(t),r=s.pointerType==="mouse"||s.pointerType==="pen";this.dispatchEvent(s,"pointerover"),r&&this.dispatchEvent(s,"mouseover"),s.pointerType==="mouse"&&(this.cursor=(o=s.target)==null?void 0:o.cursor);const n=this.clonePointerEvent(s,"pointerenter");for(n.eventPhase=n.AT_TARGET;n.target&&n.target!==this.rootTarget.parent;)n.currentTarget=n.target,this.notifyTarget(n),r&&this.notifyTarget(n,"mouseenter"),n.target=n.target.parent;e.overTargets=s.composedPath(),this.freeEvent(s),this.freeEvent(n)}mapPointerOut(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.trackingData(t.pointerId);if(e.overTargets){const s=t.pointerType==="mouse"||t.pointerType==="pen",r=this.findMountedTarget(e.overTargets),n=this.createPointerEvent(t,"pointerout",r);this.dispatchEvent(n),s&&this.dispatchEvent(n,"mouseout");const o=this.createPointerEvent(t,"pointerleave",r);for(o.eventPhase=o.AT_TARGET;o.target&&o.target!==this.rootTarget.parent;)o.currentTarget=o.target,this.notifyTarget(o),s&&this.notifyTarget(o,"mouseleave"),o.target=o.target.parent;e.overTargets=null,this.freeEvent(n),this.freeEvent(o)}this.cursor=null}mapPointerUp(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=performance.now(),s=this.createPointerEvent(t);if(this.dispatchEvent(s,"pointerup"),s.pointerType==="touch")this.dispatchEvent(s,"touchend");else if(s.pointerType==="mouse"||s.pointerType==="pen"){const a=s.button===2;this.dispatchEvent(s,a?"rightup":"mouseup")}const r=this.trackingData(t.pointerId),n=this.findMountedTarget(r.pressTargetsByButton[t.button]);let o=n;if(n&&!s.composedPath().includes(n)){let a=n;for(;a&&!s.composedPath().includes(a);){if(s.currentTarget=a,this.notifyTarget(s,"pointerupoutside"),s.pointerType==="touch")this.notifyTarget(s,"touchendoutside");else if(s.pointerType==="mouse"||s.pointerType==="pen"){const h=s.button===2;this.notifyTarget(s,h?"rightupoutside":"mouseupoutside")}a=a.parent}delete r.pressTargetsByButton[t.button],o=a}if(o){const a=this.clonePointerEvent(s,"click");a.target=o,a.path=null,r.clicksByButton[t.button]||(r.clicksByButton[t.button]={clickCount:0,target:a.target,timeStamp:e});const h=r.clicksByButton[t.button];h.target===a.target&&e-h.timeStamp<200?++h.clickCount:h.clickCount=1,h.target=a.target,h.timeStamp=e,a.detail=h.clickCount,a.pointerType==="mouse"?this.dispatchEvent(a,"click"):a.pointerType==="touch"&&this.dispatchEvent(a,"tap"),this.dispatchEvent(a,"pointertap"),this.freeEvent(a)}this.freeEvent(s)}mapPointerUpOutside(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.trackingData(t.pointerId),s=this.findMountedTarget(e.pressTargetsByButton[t.button]),r=this.createPointerEvent(t);if(s){let n=s;for(;n;)r.currentTarget=n,this.notifyTarget(r,"pointerupoutside"),r.pointerType==="touch"?this.notifyTarget(r,"touchendoutside"):(r.pointerType==="mouse"||r.pointerType==="pen")&&this.notifyTarget(r,r.button===2?"rightupoutside":"mouseupoutside"),n=n.parent;delete e.pressTargetsByButton[t.button]}this.freeEvent(r)}mapWheel(t){if(!(t instanceof ys)){console.warn("EventBoundary cannot map a non-wheel event as a wheel event");return}const e=this.createWheelEvent(t);this.dispatchEvent(e),this.freeEvent(e)}findMountedTarget(t){if(!t)return null;let e=t[0];for(let s=1;s<t.length&&t[s].parent===e;s++)e=t[s];return e}createPointerEvent(t,e,s){const r=this.allocateEvent(kt);return this.copyPointerData(t,r),this.copyMouseData(t,r),this.copyData(t,r),r.nativeEvent=t.nativeEvent,r.originalEvent=t,r.target=s!=null?s:this.hitTest(r.global.x,r.global.y),typeof e=="string"&&(r.type=e),r}createWheelEvent(t){const e=this.allocateEvent(ys);return this.copyWheelData(t,e),this.copyMouseData(t,e),this.copyData(t,e),e.nativeEvent=t.nativeEvent,e.originalEvent=t,e.target=this.hitTest(e.global.x,e.global.y),e}clonePointerEvent(t,e){const s=this.allocateEvent(kt);return s.nativeEvent=t.nativeEvent,s.originalEvent=t.originalEvent,this.copyPointerData(t,s),this.copyMouseData(t,s),this.copyData(t,s),s.target=t.target,s.path=t.composedPath().slice(),s.type=e!=null?e:s.type,s}copyWheelData(t,e){e.deltaMode=t.deltaMode,e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ}copyPointerData(t,e){t instanceof kt&&e instanceof kt&&(e.pointerId=t.pointerId,e.width=t.width,e.height=t.height,e.isPrimary=t.isPrimary,e.pointerType=t.pointerType,e.pressure=t.pressure,e.tangentialPressure=t.tangentialPressure,e.tiltX=t.tiltX,e.tiltY=t.tiltY,e.twist=t.twist)}copyMouseData(t,e){t instanceof _i&&e instanceof _i&&(e.altKey=t.altKey,e.button=t.button,e.buttons=t.buttons,e.client.copyFrom(t.client),e.ctrlKey=t.ctrlKey,e.metaKey=t.metaKey,e.movement.copyFrom(t.movement),e.screen.copyFrom(t.screen),e.global.copyFrom(t.global))}copyData(t,e){e.isTrusted=t.isTrusted,e.srcElement=t.srcElement,e.timeStamp=performance.now(),e.type=t.type,e.detail=t.detail,e.view=t.view,e.which=t.which,e.layer.copyFrom(t.layer),e.page.copyFrom(t.page)}trackingData(t){return this.mappingState.trackingData[t]||(this.mappingState.trackingData[t]={pressTargetsByButton:{},clicksByButton:{},overTarget:null}),this.mappingState.trackingData[t]}allocateEvent(t){this.eventPool.has(t)||this.eventPool.set(t,[]);const e=this.eventPool.get(t).pop()||new t(this);return e.eventPhase=e.NONE,e.currentTarget=null,e.path=null,e.target=null,e}freeEvent(t){if(t.manager!==this)throw new Error("It is illegal to free an event not managed by this EventBoundary!");const e=t.constructor;this.eventPool.has(e)||this.eventPool.set(e,[]),this.eventPool.get(e).push(t)}notifyListeners(t,e){const s=t.currentTarget._events[e];if(!!s)if("fn"in s)s.fn.call(s.context,t);else for(let r=0,n=s.length;r<n&&!t.propagationImmediatelyStopped;r++)s[r].fn.call(s[r].context,t)}}const zf=1,Wf={touchstart:"pointerdown",touchend:"pointerup",touchendoutside:"pointerupoutside",touchmove:"pointermove",touchcancel:"pointercancel"};class dn{constructor(t){this.supportsTouchEvents="ontouchstart"in globalThis,this.supportsPointerEvents=!!globalThis.PointerEvent,this.domElement=null,this.resolution=1,this.renderer=t,this.rootBoundary=new va(null),this.autoPreventDefault=!0,this.eventsAdded=!1,this.rootPointerEvent=new kt(null),this.rootWheelEvent=new ys(null),this.cursorStyles={default:"inherit",pointer:"pointer"},this.onPointerDown=this.onPointerDown.bind(this),this.onPointerMove=this.onPointerMove.bind(this),this.onPointerUp=this.onPointerUp.bind(this),this.onPointerOverOut=this.onPointerOverOut.bind(this),this.onWheel=this.onWheel.bind(this)}init(){const{view:t,resolution:e}=this.renderer;this.setTargetElement(t),this.resolution=e}resolutionChange(t){this.resolution=t}destroy(){this.setTargetElement(null),this.renderer=null}setCursor(t){t=t||"default";let e=!0;if(globalThis.OffscreenCanvas&&this.domElement instanceof OffscreenCanvas&&(e=!1),this.currentCursor===t)return;this.currentCursor=t;const s=this.cursorStyles[t];if(s)switch(typeof s){case"string":e&&(this.domElement.style.cursor=s);break;case"function":s(t);break;case"object":e&&Object.assign(this.domElement.style,s);break}else e&&typeof t=="string"&&!Object.prototype.hasOwnProperty.call(this.cursorStyles,t)&&(this.domElement.style.cursor=t)}onPointerDown(t){if(this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.supportsTouchEvents&&t.pointerType==="touch")return;const e=this.normalizeToPointerData(t);this.autoPreventDefault&&e[0].isNormalized&&(t.cancelable||!("cancelable"in t))&&t.preventDefault();for(let s=0,r=e.length;s<r;s++){const n=e[s],o=this.bootstrapEvent(this.rootPointerEvent,n);this.rootBoundary.mapEvent(o)}this.setCursor(this.rootBoundary.cursor)}onPointerMove(t){if(this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.supportsTouchEvents&&t.pointerType==="touch")return;const e=this.normalizeToPointerData(t);for(let s=0,r=e.length;s<r;s++){const n=this.bootstrapEvent(this.rootPointerEvent,e[s]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}onPointerUp(t){if(this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.supportsTouchEvents&&t.pointerType==="touch")return;let e=t.target;t.composedPath&&t.composedPath().length>0&&(e=t.composedPath()[0]);const s=e!==this.domElement?"outside":"",r=this.normalizeToPointerData(t);for(let n=0,o=r.length;n<o;n++){const a=this.bootstrapEvent(this.rootPointerEvent,r[n]);a.type+=s,this.rootBoundary.mapEvent(a)}this.setCursor(this.rootBoundary.cursor)}onPointerOverOut(t){if(this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.supportsTouchEvents&&t.pointerType==="touch")return;const e=this.normalizeToPointerData(t);for(let s=0,r=e.length;s<r;s++){const n=this.bootstrapEvent(this.rootPointerEvent,e[s]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}onWheel(t){const e=this.normalizeWheelEvent(t);this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.rootBoundary.mapEvent(e)}setTargetElement(t){this.removeEvents(),this.domElement=t,this.addEvents()}addEvents(){if(this.eventsAdded||!this.domElement)return;const t=this.domElement.style;t&&(globalThis.navigator.msPointerEnabled?(t.msContentZooming="none",t.msTouchAction="none"):this.supportsPointerEvents&&(t.touchAction="none")),this.supportsPointerEvents?(globalThis.document.addEventListener("pointermove",this.onPointerMove,!0),this.domElement.addEventListener("pointerdown",this.onPointerDown,!0),this.domElement.addEventListener("pointerleave",this.onPointerOverOut,!0),this.domElement.addEventListener("pointerover",this.onPointerOverOut,!0),globalThis.addEventListener("pointerup",this.onPointerUp,!0)):(globalThis.document.addEventListener("mousemove",this.onPointerMove,!0),this.domElement.addEventListener("mousedown",this.onPointerDown,!0),this.domElement.addEventListener("mouseout",this.onPointerOverOut,!0),this.domElement.addEventListener("mouseover",this.onPointerOverOut,!0),globalThis.addEventListener("mouseup",this.onPointerUp,!0)),this.supportsTouchEvents&&(this.domElement.addEventListener("touchstart",this.onPointerDown,!0),this.domElement.addEventListener("touchend",this.onPointerUp,!0),this.domElement.addEventListener("touchmove",this.onPointerMove,!0)),this.domElement.addEventListener("wheel",this.onWheel,{passive:!0,capture:!0}),this.eventsAdded=!0}removeEvents(){if(!this.eventsAdded||!this.domElement)return;const t=this.domElement.style;globalThis.navigator.msPointerEnabled?(t.msContentZooming="",t.msTouchAction=""):this.supportsPointerEvents&&(t.touchAction=""),this.supportsPointerEvents?(globalThis.document.removeEventListener("pointermove",this.onPointerMove,!0),this.domElement.removeEventListener("pointerdown",this.onPointerDown,!0),this.domElement.removeEventListener("pointerleave",this.onPointerOverOut,!0),this.domElement.removeEventListener("pointerover",this.onPointerOverOut,!0),globalThis.removeEventListener("pointerup",this.onPointerUp,!0)):(globalThis.document.removeEventListener("mousemove",this.onPointerMove,!0),this.domElement.removeEventListener("mousedown",this.onPointerDown,!0),this.domElement.removeEventListener("mouseout",this.onPointerOverOut,!0),this.domElement.removeEventListener("mouseover",this.onPointerOverOut,!0),globalThis.removeEventListener("mouseup",this.onPointerUp,!0)),this.supportsTouchEvents&&(this.domElement.removeEventListener("touchstart",this.onPointerDown,!0),this.domElement.removeEventListener("touchend",this.onPointerUp,!0),this.domElement.removeEventListener("touchmove",this.onPointerMove,!0)),this.domElement.removeEventListener("wheel",this.onWheel,!0),this.domElement=null,this.eventsAdded=!1}mapPositionToPoint(t,e,s){let r;this.domElement.parentElement?r=this.domElement.getBoundingClientRect():r={x:0,y:0,width:this.domElement.width,height:this.domElement.height,left:0,top:0};const n=1/this.resolution;t.x=(e-r.left)*(this.domElement.width/r.width)*n,t.y=(s-r.top)*(this.domElement.height/r.height)*n}normalizeToPointerData(t){const e=[];if(this.supportsTouchEvents&&t instanceof TouchEvent)for(let s=0,r=t.changedTouches.length;s<r;s++){const n=t.changedTouches[s];typeof n.button=="undefined"&&(n.button=0),typeof n.buttons=="undefined"&&(n.buttons=1),typeof n.isPrimary=="undefined"&&(n.isPrimary=t.touches.length===1&&t.type==="touchstart"),typeof n.width=="undefined"&&(n.width=n.radiusX||1),typeof n.height=="undefined"&&(n.height=n.radiusY||1),typeof n.tiltX=="undefined"&&(n.tiltX=0),typeof n.tiltY=="undefined"&&(n.tiltY=0),typeof n.pointerType=="undefined"&&(n.pointerType="touch"),typeof n.pointerId=="undefined"&&(n.pointerId=n.identifier||0),typeof n.pressure=="undefined"&&(n.pressure=n.force||.5),typeof n.twist=="undefined"&&(n.twist=0),typeof n.tangentialPressure=="undefined"&&(n.tangentialPressure=0),typeof n.layerX=="undefined"&&(n.layerX=n.offsetX=n.clientX),typeof n.layerY=="undefined"&&(n.layerY=n.offsetY=n.clientY),n.isNormalized=!0,n.type=t.type,e.push(n)}else if(!globalThis.MouseEvent||t instanceof MouseEvent&&(!this.supportsPointerEvents||!(t instanceof globalThis.PointerEvent))){const s=t;typeof s.isPrimary=="undefined"&&(s.isPrimary=!0),typeof s.width=="undefined"&&(s.width=1),typeof s.height=="undefined"&&(s.height=1),typeof s.tiltX=="undefined"&&(s.tiltX=0),typeof s.tiltY=="undefined"&&(s.tiltY=0),typeof s.pointerType=="undefined"&&(s.pointerType="mouse"),typeof s.pointerId=="undefined"&&(s.pointerId=zf),typeof s.pressure=="undefined"&&(s.pressure=.5),typeof s.twist=="undefined"&&(s.twist=0),typeof s.tangentialPressure=="undefined"&&(s.tangentialPressure=0),s.isNormalized=!0,e.push(s)}else e.push(t);return e}normalizeWheelEvent(t){const e=this.rootWheelEvent;return this.transferMouseData(e,t),e.deltaMode=t.deltaMode,e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ,this.mapPositionToPoint(e.screen,t.clientX,t.clientY),e.global.copyFrom(e.screen),e.offset.copyFrom(e.screen),e.nativeEvent=t,e.type=t.type,e}bootstrapEvent(t,e){return t.originalEvent=null,t.nativeEvent=e,t.pointerId=e.pointerId,t.width=e.width,t.height=e.height,t.isPrimary=e.isPrimary,t.pointerType=e.pointerType,t.pressure=e.pressure,t.tangentialPressure=e.tangentialPressure,t.tiltX=e.tiltX,t.tiltY=e.tiltY,t.twist=e.twist,this.transferMouseData(t,e),this.mapPositionToPoint(t.screen,e.clientX,e.clientY),t.global.copyFrom(t.screen),t.offset.copyFrom(t.screen),t.isTrusted=e.isTrusted,t.type==="pointerleave"&&(t.type="pointerout"),t.type.startsWith("mouse")&&(t.type=t.type.replace("mouse","pointer")),t.type.startsWith("touch")&&(t.type=Wf[t.type]||t.type),t}transferMouseData(t,e){t.isTrusted=e.isTrusted,t.srcElement=e.srcElement,t.timeStamp=performance.now(),t.type=e.type,t.altKey=e.altKey,t.button=e.button,t.buttons=e.buttons,t.client.x=e.clientX,t.client.y=e.clientY,t.ctrlKey=e.ctrlKey,t.metaKey=e.metaKey,t.movement.x=e.movementX,t.movement.y=e.movementY,t.page.x=e.pageX,t.page.y=e.pageY,t.relatedTarget=null,t.shiftKey=e.shiftKey}}dn.extension={name:"events",type:[F.RendererSystem,F.CanvasRendererSystem]},U.add(dn);const ya={interactive:!1,interactiveChildren:!0,hitArea:null,addEventListener(i,t,e){const s=typeof e=="boolean"&&e||typeof e=="object"&&e.capture,r=typeof t=="function"?void 0:t;i=s?`${i}capture`:i,t=typeof t=="function"?t:t.handleEvent,this.on(i,t,r)},removeEventListener(i,t,e){const s=typeof e=="boolean"&&e||typeof e=="object"&&e.capture,r=typeof t=="function"?void 0:t;i=s?`${i}capture`:i,t=typeof t=="function"?t:t.handleEvent,this.off(i,t,r)},dispatchEvent(i){if(!(i instanceof We))throw new Error("DisplayObject cannot propagate events outside of the Federated Events API");return i.defaultPrevented=!1,i.path=null,i.target=this,i.manager.dispatchEvent(i),!i.defaultPrevented}};at.mixin(ya),at.mixin(_a);const jf=9,xs=100,Yf=0,$f=0,xa=2,Ta=1,qf=-1e3,Kf=-1e3,Zf=2;class fn{constructor(t){this.debug=!1,this._isActive=!1,this._isMobileAccessibility=!1,this.pool=[],this.renderId=0,this.children=[],this.androidUpdateCount=0,this.androidUpdateFrequency=500,this._hookDiv=null,(Vt.tablet||Vt.phone)&&this.createTouchHook();const e=document.createElement("div");e.style.width=`${xs}px`,e.style.height=`${xs}px`,e.style.position="absolute",e.style.top=`${Yf}px`,e.style.left=`${$f}px`,e.style.zIndex=xa.toString(),this.div=e,this.renderer=t,this._onKeyDown=this._onKeyDown.bind(this),this._onMouseMove=this._onMouseMove.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)}get isActive(){return this._isActive}get isMobileAccessibility(){return this._isMobileAccessibility}createTouchHook(){const t=document.createElement("button");t.style.width=`${Ta}px`,t.style.height=`${Ta}px`,t.style.position="absolute",t.style.top=`${qf}px`,t.style.left=`${Kf}px`,t.style.zIndex=Zf.toString(),t.style.backgroundColor="#FF0000",t.title="select to enable accessibility for this content",t.addEventListener("focus",()=>{this._isMobileAccessibility=!0,this.activate(),this.destroyTouchHook()}),document.body.appendChild(t),this._hookDiv=t}destroyTouchHook(){!this._hookDiv||(document.body.removeChild(this._hookDiv),this._hookDiv=null)}activate(){var t;this._isActive||(this._isActive=!0,globalThis.document.addEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown,!1),this.renderer.on("postrender",this.update,this),(t=this.renderer.view.parentNode)==null||t.appendChild(this.div))}deactivate(){var t;!this._isActive||this._isMobileAccessibility||(this._isActive=!1,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.addEventListener("keydown",this._onKeyDown,!1),this.renderer.off("postrender",this.update),(t=this.div.parentNode)==null||t.removeChild(this.div))}updateAccessibleObjects(t){if(!t.visible||!t.accessibleChildren)return;t.accessible&&t.interactive&&(t._accessibleActive||this.addChild(t),t.renderId=this.renderId);const e=t.children;if(e)for(let s=0;s<e.length;s++)this.updateAccessibleObjects(e[s])}update(){const t=performance.now();if(Vt.android.device&&t<this.androidUpdateCount||(this.androidUpdateCount=t+this.androidUpdateFrequency,!this.renderer.renderingToScreen))return;this.renderer.lastObjectRendered&&this.updateAccessibleObjects(this.renderer.lastObjectRendered);const{x:e,y:s,width:r,height:n}=this.renderer.view.getBoundingClientRect(),{width:o,height:a,resolution:h}=this.renderer,l=r/o*h,c=n/a*h;let u=this.div;u.style.left=`${e}px`,u.style.top=`${s}px`,u.style.width=`${o}px`,u.style.height=`${a}px`;for(let d=0;d<this.children.length;d++){const f=this.children[d];if(f.renderId!==this.renderId)f._accessibleActive=!1,Ae(this.children,d,1),this.div.removeChild(f._accessibleDiv),this.pool.push(f._accessibleDiv),f._accessibleDiv=null,d--;else{u=f._accessibleDiv;let p=f.hitArea;const g=f.worldTransform;f.hitArea?(u.style.left=`${(g.tx+p.x*g.a)*l}px`,u.style.top=`${(g.ty+p.y*g.d)*c}px`,u.style.width=`${p.width*g.a*l}px`,u.style.height=`${p.height*g.d*c}px`):(p=f.getBounds(),this.capHitArea(p),u.style.left=`${p.x*l}px`,u.style.top=`${p.y*c}px`,u.style.width=`${p.width*l}px`,u.style.height=`${p.height*c}px`,u.title!==f.accessibleTitle&&f.accessibleTitle!==null&&(u.title=f.accessibleTitle),u.getAttribute("aria-label")!==f.accessibleHint&&f.accessibleHint!==null&&u.setAttribute("aria-label",f.accessibleHint)),(f.accessibleTitle!==u.title||f.tabIndex!==u.tabIndex)&&(u.title=f.accessibleTitle,u.tabIndex=f.tabIndex,this.debug&&this.updateDebugHTML(u))}}this.renderId++}updateDebugHTML(t){t.innerHTML=`type: ${t.type}</br> title : ${t.title}</br> tabIndex: ${t.tabIndex}`}capHitArea(t){t.x<0&&(t.width+=t.x,t.x=0),t.y<0&&(t.height+=t.y,t.y=0);const{width:e,height:s}=this.renderer;t.x+t.width>e&&(t.width=e-t.x),t.y+t.height>s&&(t.height=s-t.y)}addChild(t){let e=this.pool.pop();e||(e=document.createElement("button"),e.style.width=`${xs}px`,e.style.height=`${xs}px`,e.style.backgroundColor=this.debug?"rgba(255,255,255,0.5)":"transparent",e.style.position="absolute",e.style.zIndex=xa.toString(),e.style.borderStyle="none",navigator.userAgent.toLowerCase().includes("chrome")?e.setAttribute("aria-live","off"):e.setAttribute("aria-live","polite"),navigator.userAgent.match(/rv:.*Gecko\//)?e.setAttribute("aria-relevant","additions"):e.setAttribute("aria-relevant","text"),e.addEventListener("click",this._onClick.bind(this)),e.addEventListener("focus",this._onFocus.bind(this)),e.addEventListener("focusout",this._onFocusOut.bind(this))),e.style.pointerEvents=t.accessiblePointerEvents,e.type=t.accessibleType,t.accessibleTitle&&t.accessibleTitle!==null?e.title=t.accessibleTitle:(!t.accessibleHint||t.accessibleHint===null)&&(e.title=`displayObject ${t.tabIndex}`),t.accessibleHint&&t.accessibleHint!==null&&e.setAttribute("aria-label",t.accessibleHint),this.debug&&this.updateDebugHTML(e),t._accessibleActive=!0,t._accessibleDiv=e,e.displayObject=t,this.children.push(t),this.div.appendChild(t._accessibleDiv),t._accessibleDiv.tabIndex=t.tabIndex}_dispatchEvent(t,e){const{displayObject:s}=t.target,r=this.renderer.events.rootBoundary,n=Object.assign(new We(r),{target:s});r.rootTarget=this.renderer.lastObjectRendered,e.forEach(o=>r.dispatchEvent(n,o))}_onClick(t){this._dispatchEvent(t,["click","pointertap","tap"])}_onFocus(t){t.target.getAttribute("aria-live")||t.target.setAttribute("aria-live","assertive"),this._dispatchEvent(t,["mouseover"])}_onFocusOut(t){t.target.getAttribute("aria-live")||t.target.setAttribute("aria-live","polite"),this._dispatchEvent(t,["mouseout"])}_onKeyDown(t){t.keyCode===jf&&this.activate()}_onMouseMove(t){t.movementX===0&&t.movementY===0||this.deactivate()}destroy(){this.destroyTouchHook(),this.div=null,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown),this.pool=null,this.children=null,this.renderer=null}}fn.extension={name:"accessibility",type:[F.RendererPlugin,F.CanvasRendererPlugin]},U.add(fn);const pn=class{constructor(i){this.stage=new wt,i=Object.assign({forceCanvas:!1},i),this.renderer=oa(i),pn._plugins.forEach(t=>{t.init.call(this,i)})}render(){this.renderer.render(this.stage)}get view(){return this.renderer.view}get screen(){return this.renderer.screen}destroy(i,t){const e=pn._plugins.slice(0);e.reverse(),e.forEach(s=>{s.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(i),this.renderer=null}};let mn=pn;mn._plugins=[],U.handleByList(F.Application,mn._plugins);class gn{static init(t){Object.defineProperty(this,"resizeTo",{set(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{!this._resizeTo||(this.cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this.cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this.cancelResize();let e,s;if(this._resizeTo===globalThis.window)e=globalThis.innerWidth,s=globalThis.innerHeight;else{const{clientWidth:r,clientHeight:n}=this._resizeTo;e=r,s=n}this.renderer.resize(e,s),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=t.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this.cancelResize(),this.cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}gn.extension=F.Application,U.add(gn);class Qf{constructor(t,e=!1){this._loader=t,this._assetList=[],this._isLoading=!1,this._maxConcurrent=1,this.verbose=e}add(t){t.forEach(e=>{this._assetList.push(e)}),this.verbose&&console.log("[BackgroundLoader] assets: ",this._assetList),this._isActive&&!this._isLoading&&this._next()}async _next(){if(this._assetList.length&&this._isActive){this._isLoading=!0;const t=[],e=Math.min(this._assetList.length,this._maxConcurrent);for(let s=0;s<e;s++)t.push(this._assetList.pop());await this._loader.load(t),this._isLoading=!1,this._next()}}get active(){return this._isActive}set active(t){this._isActive!==t&&(this._isActive=t,t&&!this._isLoading&&this._next())}}function _n(i,t){if(Array.isArray(t)){for(const e of t)if(i.startsWith(`data:${e}`))return!0;return!1}return i.startsWith(`data:${t}`)}function vi(i,t){const e=i.split("?")[0],s=yt.extname(e).toLowerCase();return Array.isArray(t)?t.includes(s.toLowerCase()):s.toLowerCase()===t}const ee=(i,t)=>(Array.isArray(i)||(i=[i]),t?i.map(e=>typeof e=="string"?t(e):e):i);function ba(i,t,e,s,r){const n=t[e];for(let o=0;o<n.length;o++){const a=n[o];e<t.length-1?ba(i.replace(s[e],a),t,e+1,s,r):r.push(i.replace(s[e],a))}}function Ea(i){const t=/\{(.*?)\}/g,e=i.match(t),s=[];if(e){const r=[];e.forEach(n=>{const o=n.substring(1,n.length-1).split(",");r.push(o)}),ba(i,r,0,e,s)}else s.push(i);return s}const yi=i=>!Array.isArray(i);class Jf{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(t){return this._cache.has(t)}get(t){const e=this._cache.get(t);return e||console.warn(`[Assets] Asset id ${t} was not found in the Cache`),e}set(t,e){const s=ee(t);let r;for(let a=0;a<this.parsers.length;a++){const h=this.parsers[a];if(h.test(e)){r=h.getCacheableAssets(s,e);break}}r||(r={},s.forEach(a=>{r[a]=e}));const n=Object.keys(r),o={cacheKeys:n,keys:s};if(s.forEach(a=>{this._cacheMap.set(a,o)}),n.forEach(a=>{this._cache.has(a)&&this._cache.get(a)!==e&&console.warn("[Cache] already has key:",a),this._cache.set(a,r[a])}),e instanceof L){const a=e;s.forEach(h=>{a.baseTexture!==L.EMPTY.baseTexture&&$.addToCache(a.baseTexture,h),L.addToCache(a,h)})}}remove(t){if(this._cacheMap.get(t),!this._cacheMap.has(t)){console.warn(`[Assets] Asset id ${t} was not found in the Cache`);return}const e=this._cacheMap.get(t);e.cacheKeys.forEach(r=>{this._cache.delete(r)}),e.keys.forEach(r=>{this._cacheMap.delete(r)})}get parsers(){return this._parsers}}const je=new Jf;class tp{constructor(){this._parsers=[],this.promiseCache={}}reset(){this.promiseCache={}}_getLoadPromiseAndParser(t,e){const s={promise:null,parser:null};return s.promise=(async()=>{var n,o;let r=null;for(let a=0;a<this.parsers.length;a++){const h=this.parsers[a];if(h.load&&((n=h.test)==null?void 0:n.call(h,t,e,this))){r=await h.load(t,e,this),s.parser=h;break}}if(!s.parser)return console.warn(`[Assets] ${t} could not be loaded as we don't know how to parse it, ensure the correct parser has being added`),null;for(let a=0;a<this.parsers.length;a++){const h=this.parsers[a];h.parse&&h.parse&&await((o=h.testParse)==null?void 0:o.call(h,r,e,this))&&(r=await h.parse(r,e,this)||r,s.parser=h)}return r})(),s}async load(t,e){let s=0;const r={},n=yi(t),o=ee(t,l=>({src:l})),a=o.length,h=o.map(async l=>{const c=yt.toAbsolute(l.src);if(!r[l.src])try{this.promiseCache[c]||(this.promiseCache[c]=this._getLoadPromiseAndParser(c,l)),r[l.src]=await this.promiseCache[c].promise,e&&e(++s/a)}catch(u){throw delete this.promiseCache[c],delete r[l.src],new Error(`[Loader.load] Failed to load ${c}.
${u}`)}});return await Promise.all(h),n?r[o[0].src]:r}async unload(t){const s=ee(t,r=>({src:r})).map(async r=>{var a,h;const n=yt.toAbsolute(r.src),o=this.promiseCache[n];if(o){const l=await o.promise;(h=(a=o.parser)==null?void 0:a.unload)==null||h.call(a,l,r,this),delete this.promiseCache[n]}});await Promise.all(s)}get parsers(){return this._parsers}}class ep{constructor(){this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}prefer(...t){t.forEach(e=>{this._preferredOrder.push(e),e.priority||(e.priority=Object.keys(e.params))}),this._resolverHash={}}set basePath(t){this._basePath=t}get basePath(){return this._basePath}set rootPath(t){this._rootPath=t}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this._preferredOrder=[],this._resolverHash={},this._assetMap={},this._rootPath=null,this._basePath=null,this._manifest=null}addManifest(t){this._manifest&&console.warn("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=t,t.bundles.forEach(e=>{this.addBundle(e.name,e.assets)})}addBundle(t,e){const s=[];Array.isArray(e)?e.forEach(r=>{typeof r.name=="string"?s.push(r.name):s.push(...r.name),this.add(r.name,r.srcs)}):Object.keys(e).forEach(r=>{s.push(r),this.add(r,e[r])}),this._bundles[t]=s}add(t,e,s){const r=ee(t);r.forEach(o=>{this._assetMap[o]&&console.warn(`[Resolver] already has key: ${o} overwriting`)}),Array.isArray(e)||(typeof e=="string"?e=Ea(e):e=[e]);const n=e.map(o=>{var h;let a=o;if(typeof o=="string"){let l=!1;for(let c=0;c<this._parsers.length;c++){const u=this._parsers[c];if(u.test(o)){a=u.parse(o),l=!0;break}}l||(a={src:o})}return a.format||(a.format=a.src.split(".").pop()),a.alias||(a.alias=r),(this._basePath||this._rootPath)&&(a.src=yt.toAbsolute(a.src,this._basePath,this._rootPath)),a.data=(h=a.data)!=null?h:s,a});r.forEach(o=>{this._assetMap[o]=n})}resolveBundle(t){const e=yi(t);t=ee(t);const s={};return t.forEach(r=>{const n=this._bundles[r];n&&(s[r]=this.resolve(n))}),e?s[t[0]]:s}resolveUrl(t){const e=this.resolve(t);if(typeof t!="string"){const s={};for(const r in e)s[r]=e[r].src;return s}return e.src}resolve(t){const e=yi(t);t=ee(t);const s={};return t.forEach(r=>{var n;if(!this._resolverHash[r])if(this._assetMap[r]){let o=this._assetMap[r];const a=this._getPreferredOrder(o),h=o[0];a==null||a.priority.forEach(l=>{a.params[l].forEach(c=>{const u=o.filter(d=>d[l]?d[l]===c:!1);u.length&&(o=u)})}),this._resolverHash[r]=(n=o[0])!=null?n:h}else{let o=r;(this._basePath||this._rootPath)&&(o=yt.toAbsolute(o,this._basePath,this._rootPath)),this._resolverHash[r]={src:o}}s[r]=this._resolverHash[r]}),e?s[t[0]]:s}_getPreferredOrder(t){for(let e=0;e<t.length;e++){const s=t[0],r=this._preferredOrder.find(n=>n.params.format.includes(s.format));if(r)return r}return this._preferredOrder[0]}}class wa{constructor(){this._detections=[],this._initialized=!1,this.resolver=new ep,this.loader=new tp,this.cache=je,this._backgroundLoader=new Qf(this.loader),this._backgroundLoader.active=!0,this.reset()}async init(t={}){var n,o,a,h;if(this._initialized){console.warn("[Assets]AssetManager already initialized, did you load before calling this Asset.init()?");return}if(this._initialized=!0,t.basePath&&(this.resolver.basePath=t.basePath),t.manifest){let l=t.manifest;typeof l=="string"&&(l=await this.load(l)),this.resolver.addManifest(l)}const e=(o=(n=t.texturePreference)==null?void 0:n.resolution)!=null?o:1,s=typeof e=="number"?[e]:e;let r=[];if((a=t.texturePreference)!=null&&a.format){const l=(h=t.texturePreference)==null?void 0:h.format;r=typeof l=="string"?[l]:l;for(const c of this._detections)await c.test()||(r=await c.remove(r))}else for(const l of this._detections)await l.test()&&(r=await l.add(r));this.resolver.prefer({params:{format:r,resolution:s}})}add(t,e,s){this.resolver.add(t,e,s)}async load(t,e){this._initialized||await this.init();const s=yi(t),r=ee(t).map(a=>typeof a!="string"?(this.resolver.add(a.src,a),a.src):a),n=this.resolver.resolve(r),o=await this._mapLoadToResolve(n,e);return s?o[r[0]]:o}addBundle(t,e){this.resolver.addBundle(t,e)}async loadBundle(t,e){this._initialized||await this.init();let s=!1;typeof t=="string"&&(s=!0,t=[t]);const r=this.resolver.resolveBundle(t),n={},o=Object.keys(r);let a=0,h=0;const l=()=>{e==null||e(++a/h)},c=o.map(u=>{const d=r[u];return h+=Object.keys(d).length,this._mapLoadToResolve(d,l).then(f=>{n[u]=f})});return await Promise.all(c),s?n[t[0]]:n}async backgroundLoad(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolve(t);this._backgroundLoader.add(Object.values(e))}async backgroundLoadBundle(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolveBundle(t);Object.values(e).forEach(s=>{this._backgroundLoader.add(Object.values(s))})}reset(){this.resolver.reset(),this.loader.reset(),this.cache.reset(),this._initialized=!1}get(t){if(typeof t=="string")return je.get(t);const e={};for(let s=0;s<t.length;s++)e[s]=je.get(t[s]);return e}async _mapLoadToResolve(t,e){const s=Object.values(t),r=Object.keys(t);this._backgroundLoader.active=!1;const n=await this.loader.load(s,e);this._backgroundLoader.active=!0;const o={};return s.forEach((a,h)=>{const l=n[a.src],c=[a.src];a.alias&&c.push(...a.alias),o[r[h]]=l,je.set(c,l)}),o}async unload(t){this._initialized||await this.init();const e=ee(t).map(r=>typeof r!="string"?r.src:r),s=this.resolver.resolve(e);await this._unloadFromResolved(s)}async unloadBundle(t){this._initialized||await this.init(),t=ee(t);const e=this.resolver.resolveBundle(t),s=Object.keys(e).map(r=>this._unloadFromResolved(e[r]));await Promise.all(s)}async _unloadFromResolved(t){const e=Object.values(t);e.forEach(s=>{je.remove(s.src)}),await this.loader.unload(e)}get detections(){return this._detections}}const xi=new wa;U.handleByList(F.LoadParser,xi.loader.parsers).handleByList(F.ResolveParser,xi.resolver.parsers).handleByList(F.CacheParser,xi.cache.parsers).handleByList(F.DetectionParser,xi.detections);const Sa={loader:F.LoadParser,resolver:F.ResolveParser,cache:F.CacheParser,detection:F.DetectionParser};U.handle(F.Asset,i=>{const t=i.ref;Object.entries(Sa).filter(([e])=>!!t[e]).forEach(([e,s])=>{var r;return U.add(Object.assign(t[e],{extension:(r=t[e].extension)!=null?r:s}))})},i=>{const t=i.ref;Object.keys(Sa).filter(e=>!!t[e]).forEach(e=>U.remove(t[e]))});const Aa={extension:F.CacheParser,test:i=>Array.isArray(i)&&i.every(t=>t instanceof L),getCacheableAssets:(i,t)=>{const e={};return i.forEach(s=>{t.forEach((r,n)=>{e[s+(n===0?"":n+1)]=r})}),e}};U.add(Aa);var Yt=(i=>(i[i.Low=0]="Low",i[i.Normal=1]="Normal",i[i.High=2]="High",i))(Yt||{});const Ca={extension:{type:F.LoadParser,priority:Yt.Low},test(i){return yt.extname(i).includes(".json")},async load(i){return await(await P.ADAPTER.fetch(i)).json()}};U.add(Ca);const Ra={extension:{type:F.LoadParser,priority:Yt.Low},test(i){return yt.extname(i).includes(".txt")},async load(i){return await(await P.ADAPTER.fetch(i)).text()}};U.add(Ra);const ip=["normal","bold","100","200","300","400","500","600","700","800","900"],sp=[".ttf",".otf",".woff",".woff2"],rp=["font/ttf","font/otf","font/woff","font/woff2"];function Ia(i){const t=yt.extname(i);return yt.basename(i,t).replace(/(-|_)/g," ").toLowerCase().split(" ").map(n=>n.charAt(0).toUpperCase()+n.slice(1)).join(" ")}const Pa={extension:{type:F.LoadParser,priority:Yt.Low},test(i){return _n(i,rp)||vi(i,sp)},async load(i,t){var s,r,n,o,a,h;if(!globalThis.navigator.onLine)throw new Error("[loadWebFont] Cannot load font - navigator is offline");const e=P.ADAPTER.getFontFaceSet();if(e){const l=[],c=(r=(s=t.data)==null?void 0:s.family)!=null?r:Ia(i),u=(a=(o=(n=t.data)==null?void 0:n.weights)==null?void 0:o.filter(f=>ip.includes(f)))!=null?a:["normal"],d=(h=t.data)!=null?h:{};for(let f=0;f<u.length;f++){const p=u[f],g=new FontFace(c,`url(${encodeURI(i)})`,Hn(re({},d),{weight:p}));await g.load(),e.add(g),l.push(g)}return l.length===1?l[0]:l}return console.warn("[loadWebFont] FontFace API is not supported. Skipping loading font"),null},unload(i){(Array.isArray(i)?i:[i]).forEach(t=>P.ADAPTER.getFontFaceSet().delete(t))}};U.add(Pa);let Ma=0,vn;const np={id:"checkImageBitmap",code:`
    async function checkImageBitmap()
    {
        try
        {
            if (typeof createImageBitmap !== 'function') return false;

            const response = await fetch('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=');
            const imageBlob =  await response.blob();
            const imageBitmap = await createImageBitmap(imageBlob);

            return imageBitmap.width === 1 && imageBitmap.height === 1;
        }
        catch (e)
        {
            return false;
        }
    }
    checkImageBitmap().then((result) => { self.postMessage(result); });
    `},op={id:"loadImageBitmap",code:`
    async function loadImageBitmap(url)
    {
        const response = await fetch(url);

        if (!response.ok)
        {
            throw new Error(\`[WorkerManager.loadImageBitmap] Failed to fetch \${url}: \`
                + \`\${response.status} \${response.statusText}\`);
        }

        const imageBlob =  await response.blob();
        const imageBitmap = await createImageBitmap(imageBlob);

        return imageBitmap;
    }
    self.onmessage = async (event) =>
    {
        try
        {
            const imageBitmap = await loadImageBitmap(event.data.data[0]);

            self.postMessage({
                data: imageBitmap,
                uuid: event.data.uuid,
                id: event.data.id,
            }, [imageBitmap]);
        }
        catch(e)
        {
            self.postMessage({
                error: e,
                uuid: event.data.uuid,
                id: event.data.id,
            });
        }
    };`};let yn;class ap{constructor(){this._initialized=!1,this._createdWorkers=0,this.workerPool=[],this.queue=[],this.resolveHash={}}isImageBitmapSupported(){return this._isImageBitmapSupported!==void 0?this._isImageBitmapSupported:(this._isImageBitmapSupported=new Promise(t=>{const e=URL.createObjectURL(new Blob([np.code],{type:"application/javascript"})),s=new Worker(e);s.addEventListener("message",r=>{s.terminate(),URL.revokeObjectURL(e),t(r.data)})}),this._isImageBitmapSupported)}loadImageBitmap(t){return this._run("loadImageBitmap",[t])}async _initWorkers(){this._initialized||(this._initialized=!0)}getWorker(){vn===void 0&&(vn=navigator.hardwareConcurrency||4);let t=this.workerPool.pop();return!t&&this._createdWorkers<vn&&(yn||(yn=URL.createObjectURL(new Blob([op.code],{type:"application/javascript"}))),this._createdWorkers++,t=new Worker(yn),t.addEventListener("message",e=>{this.complete(e.data),this.returnWorker(e.target),this.next()})),t}returnWorker(t){this.workerPool.push(t)}complete(t){t.error!==void 0?this.resolveHash[t.uuid].reject(t.error):this.resolveHash[t.uuid].resolve(t.data),this.resolveHash[t.uuid]=null}async _run(t,e){await this._initWorkers();const s=new Promise((r,n)=>{this.queue.push({id:t,arguments:e,resolve:r,reject:n})});return this.next(),s}next(){if(!this.queue.length)return;const t=this.getWorker();if(!t)return;const e=this.queue.pop(),s=e.id;this.resolveHash[Ma]={resolve:e.resolve,reject:e.reject},t.postMessage({data:e.arguments,uuid:Ma++,id:s})}}const Ba=new ap;function Ti(i,t,e){const s=new L(i);return s.baseTexture.on("dispose",()=>{delete t.promiseCache[e]}),s}const hp=[".jpeg",".jpg",".png",".webp",".avif"],lp=["image/jpeg","image/png","image/webp","image/avif"];async function Da(i){const t=await P.ADAPTER.fetch(i);if(!t.ok)throw new Error(`[loadImageBitmap] Failed to fetch ${i}: ${t.status} ${t.statusText}`);const e=await t.blob();return await createImageBitmap(e)}const Ts={extension:{type:F.LoadParser,priority:Yt.High},config:{preferWorkers:!0},test(i){return _n(i,lp)||vi(i,hp)},async load(i,t,e){let s=null;globalThis.createImageBitmap?this.config.preferWorkers&&await Ba.isImageBitmapSupported()?s=await Ba.loadImageBitmap(i):s=await Da(i):s=await new Promise(n=>{s=new Image,s.crossOrigin="anonymous",s.src=i,s.complete?n(s):s.onload=()=>{n(s)}});const r=new $(s,re({resolution:he(i)},t.data));return r.resource.src=i,Ti(r,e,i)},unload(i){i.destroy(!0)}};U.add(Ts);const cp={extension:{type:F.LoadParser,priority:Yt.High},test(i){return yt.extname(i).includes(".svg")},async testParse(i){return Ve.test(i)},async parse(i,t,e){var o,a,h;const s=new Ve(i,(o=t==null?void 0:t.data)==null?void 0:o.resourceOptions),r=new $(s,re({resolution:he(i)},t==null?void 0:t.data));r.resource.src=i;const n=Ti(r,e,i);return(h=(a=t==null?void 0:t.data)==null?void 0:a.resourceOptions)!=null&&h.autoLoad||await s.load(),n},async load(i,t){return(await P.ADAPTER.fetch(i)).text()},unload:Ts.unload},Na={extension:F.ResolveParser,test:Ts.test,parse:i=>{var t,e;return{resolution:parseFloat((e=(t=P.RETINA_PREFIX.exec(i))==null?void 0:t[1])!=null?e:"1"),format:i.split(".").pop(),src:i}}};U.add(Na);const Fa={extension:{type:F.DetectionParser,priority:1},test:async()=>{if(!globalThis.createImageBitmap)return!1;const i="data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=",t=await P.ADAPTER.fetch(i).then(e=>e.blob());return createImageBitmap(t).then(()=>!0,()=>!1)},add:async i=>[...i,"avif"],remove:async i=>i.filter(t=>t!=="avif")};U.add(Fa);const Oa={extension:{type:F.DetectionParser,priority:0},test:async()=>{if(!globalThis.createImageBitmap)return!1;const i="data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=",t=await P.ADAPTER.fetch(i).then(e=>e.blob());return createImageBitmap(t).then(()=>!0,()=>!1)},add:async i=>[...i,"webp"],remove:async i=>i.filter(t=>t!=="webp")};U.add(Oa);const La=["png","jpg","jpeg"],Ua={extension:{type:F.DetectionParser,priority:-1},test:()=>Promise.resolve(!0),add:async i=>[...i,...La],remove:async i=>i.filter(t=>!La.includes(t))};U.add(Ua);var Ct=(i=>(i[i.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",i[i.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",i[i.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",i[i.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",i[i.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917]="COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT",i[i.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918]="COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT",i[i.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919]="COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT",i[i.COMPRESSED_SRGB_S3TC_DXT1_EXT=35916]="COMPRESSED_SRGB_S3TC_DXT1_EXT",i[i.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",i[i.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",i[i.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",i[i.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",i[i.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",i[i.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",i[i.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",i[i.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",i[i.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",i[i.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",i[i.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",i[i.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",i[i.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",i[i.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",i[i.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",i[i.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",i[i.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35986]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",i[i.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",i[i.COMPRESSED_RGBA_ASTC_4x4_KHR=37808]="COMPRESSED_RGBA_ASTC_4x4_KHR",i))(Ct||{});const bi={[33776]:.5,[33777]:.5,[33778]:1,[33779]:1,[35916]:.5,[35917]:.5,[35918]:1,[35919]:1,[37488]:.5,[37489]:.5,[37490]:1,[37491]:1,[37492]:.5,[37496]:1,[37493]:.5,[37497]:1,[37494]:.5,[37495]:.5,[35840]:.5,[35842]:.5,[35841]:.25,[35843]:.25,[36196]:.5,[35986]:.5,[35986]:1,[34798]:1,[37808]:1};class ka extends Xe{constructor(t,e={width:1,height:1,autoLoad:!0}){let s,r;typeof t=="string"?(s=t,r=new Uint8Array):(s=null,r=t),super(r,e),this.origin=s,this.buffer=r?new ps(r):null,this.origin&&e.autoLoad!==!1&&this.load(),r!=null&&r.length&&(this.loaded=!0,this.onBlobLoaded(this.buffer.rawBinaryData))}onBlobLoaded(t){}async load(){const s=await(await(await fetch(this.origin)).blob()).arrayBuffer();return this.data=new Uint32Array(s),this.buffer=new ps(s),this.loaded=!0,this.onBlobLoaded(s),this.update(),this}}class _e extends ka{constructor(t,e){super(t,e),this.format=e.format,this.levels=e.levels||1,this._width=e.width,this._height=e.height,this._extension=_e._formatToExtension(this.format),(e.levelBuffers||this.buffer)&&(this._levelBuffers=e.levelBuffers||_e._createLevelBuffers(t instanceof Uint8Array?t:this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height))}upload(t,e,s){const r=t.gl;if(!t.context.extensions[this._extension])throw new Error(`${this._extension} textures are not supported on the current machine`);if(!this._levelBuffers)return!1;for(let o=0,a=this.levels;o<a;o++){const{levelID:h,levelWidth:l,levelHeight:c,levelBuffer:u}=this._levelBuffers[o];r.compressedTexImage2D(r.TEXTURE_2D,h,this.format,l,c,0,u)}return!0}onBlobLoaded(){this._levelBuffers=_e._createLevelBuffers(this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height)}static _formatToExtension(t){if(t>=33776&&t<=33779)return"s3tc";if(t>=37488&&t<=37497)return"etc";if(t>=35840&&t<=35843)return"pvrtc";if(t>=36196)return"etc1";if(t>=35986&&t<=34798)return"atc";throw new Error("Invalid (compressed) texture format given!")}static _createLevelBuffers(t,e,s,r,n,o,a){const h=new Array(s);let l=t.byteOffset,c=o,u=a,d=c+r-1&~(r-1),f=u+n-1&~(n-1),p=d*f*bi[e];for(let g=0;g<s;g++)h[g]={levelID:g,levelWidth:s>1?c:d,levelHeight:s>1?u:f,levelBuffer:new Uint8Array(t.buffer,l,p)},l+=p,c=c>>1||1,u=u>>1||1,d=c+r-1&~(r-1),f=u+n-1&~(n-1),p=d*f*bi[e];return h}}let de,Ye;function Ga(){Ye={s3tc:de.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:de.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:de.getExtension("WEBGL_compressed_texture_etc"),etc1:de.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:de.getExtension("WEBGL_compressed_texture_pvrtc")||de.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:de.getExtension("WEBGL_compressed_texture_atc"),astc:de.getExtension("WEBGL_compressed_texture_astc")}}const Ha={extension:{type:F.DetectionParser,priority:2},test:async()=>{const t=P.ADAPTER.createCanvas().getContext("webgl");return t?(de=t,!0):(console.warn("WebGL not available for compressed textures."),!1)},add:async i=>{Ye||Ga();const t=[];for(const e in Ye)!Ye[e]||t.push(e);return[...t,...i]},remove:async i=>(Ye||Ga(),i.filter(t=>!(t in Ye)))};U.add(Ha);const xn=4,bs=124,up=32,Xa=20,dp=542327876,Es={SIZE:1,FLAGS:2,HEIGHT:3,WIDTH:4,MIPMAP_COUNT:7,PIXEL_FORMAT:19},fp={SIZE:0,FLAGS:1,FOURCC:2,RGB_BITCOUNT:3,R_BIT_MASK:4,G_BIT_MASK:5,B_BIT_MASK:6,A_BIT_MASK:7},ws={DXGI_FORMAT:0,RESOURCE_DIMENSION:1,MISC_FLAG:2,ARRAY_SIZE:3,MISC_FLAGS2:4};var pp=(i=>(i[i.DXGI_FORMAT_UNKNOWN=0]="DXGI_FORMAT_UNKNOWN",i[i.DXGI_FORMAT_R32G32B32A32_TYPELESS=1]="DXGI_FORMAT_R32G32B32A32_TYPELESS",i[i.DXGI_FORMAT_R32G32B32A32_FLOAT=2]="DXGI_FORMAT_R32G32B32A32_FLOAT",i[i.DXGI_FORMAT_R32G32B32A32_UINT=3]="DXGI_FORMAT_R32G32B32A32_UINT",i[i.DXGI_FORMAT_R32G32B32A32_SINT=4]="DXGI_FORMAT_R32G32B32A32_SINT",i[i.DXGI_FORMAT_R32G32B32_TYPELESS=5]="DXGI_FORMAT_R32G32B32_TYPELESS",i[i.DXGI_FORMAT_R32G32B32_FLOAT=6]="DXGI_FORMAT_R32G32B32_FLOAT",i[i.DXGI_FORMAT_R32G32B32_UINT=7]="DXGI_FORMAT_R32G32B32_UINT",i[i.DXGI_FORMAT_R32G32B32_SINT=8]="DXGI_FORMAT_R32G32B32_SINT",i[i.DXGI_FORMAT_R16G16B16A16_TYPELESS=9]="DXGI_FORMAT_R16G16B16A16_TYPELESS",i[i.DXGI_FORMAT_R16G16B16A16_FLOAT=10]="DXGI_FORMAT_R16G16B16A16_FLOAT",i[i.DXGI_FORMAT_R16G16B16A16_UNORM=11]="DXGI_FORMAT_R16G16B16A16_UNORM",i[i.DXGI_FORMAT_R16G16B16A16_UINT=12]="DXGI_FORMAT_R16G16B16A16_UINT",i[i.DXGI_FORMAT_R16G16B16A16_SNORM=13]="DXGI_FORMAT_R16G16B16A16_SNORM",i[i.DXGI_FORMAT_R16G16B16A16_SINT=14]="DXGI_FORMAT_R16G16B16A16_SINT",i[i.DXGI_FORMAT_R32G32_TYPELESS=15]="DXGI_FORMAT_R32G32_TYPELESS",i[i.DXGI_FORMAT_R32G32_FLOAT=16]="DXGI_FORMAT_R32G32_FLOAT",i[i.DXGI_FORMAT_R32G32_UINT=17]="DXGI_FORMAT_R32G32_UINT",i[i.DXGI_FORMAT_R32G32_SINT=18]="DXGI_FORMAT_R32G32_SINT",i[i.DXGI_FORMAT_R32G8X24_TYPELESS=19]="DXGI_FORMAT_R32G8X24_TYPELESS",i[i.DXGI_FORMAT_D32_FLOAT_S8X24_UINT=20]="DXGI_FORMAT_D32_FLOAT_S8X24_UINT",i[i.DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS=21]="DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS",i[i.DXGI_FORMAT_X32_TYPELESS_G8X24_UINT=22]="DXGI_FORMAT_X32_TYPELESS_G8X24_UINT",i[i.DXGI_FORMAT_R10G10B10A2_TYPELESS=23]="DXGI_FORMAT_R10G10B10A2_TYPELESS",i[i.DXGI_FORMAT_R10G10B10A2_UNORM=24]="DXGI_FORMAT_R10G10B10A2_UNORM",i[i.DXGI_FORMAT_R10G10B10A2_UINT=25]="DXGI_FORMAT_R10G10B10A2_UINT",i[i.DXGI_FORMAT_R11G11B10_FLOAT=26]="DXGI_FORMAT_R11G11B10_FLOAT",i[i.DXGI_FORMAT_R8G8B8A8_TYPELESS=27]="DXGI_FORMAT_R8G8B8A8_TYPELESS",i[i.DXGI_FORMAT_R8G8B8A8_UNORM=28]="DXGI_FORMAT_R8G8B8A8_UNORM",i[i.DXGI_FORMAT_R8G8B8A8_UNORM_SRGB=29]="DXGI_FORMAT_R8G8B8A8_UNORM_SRGB",i[i.DXGI_FORMAT_R8G8B8A8_UINT=30]="DXGI_FORMAT_R8G8B8A8_UINT",i[i.DXGI_FORMAT_R8G8B8A8_SNORM=31]="DXGI_FORMAT_R8G8B8A8_SNORM",i[i.DXGI_FORMAT_R8G8B8A8_SINT=32]="DXGI_FORMAT_R8G8B8A8_SINT",i[i.DXGI_FORMAT_R16G16_TYPELESS=33]="DXGI_FORMAT_R16G16_TYPELESS",i[i.DXGI_FORMAT_R16G16_FLOAT=34]="DXGI_FORMAT_R16G16_FLOAT",i[i.DXGI_FORMAT_R16G16_UNORM=35]="DXGI_FORMAT_R16G16_UNORM",i[i.DXGI_FORMAT_R16G16_UINT=36]="DXGI_FORMAT_R16G16_UINT",i[i.DXGI_FORMAT_R16G16_SNORM=37]="DXGI_FORMAT_R16G16_SNORM",i[i.DXGI_FORMAT_R16G16_SINT=38]="DXGI_FORMAT_R16G16_SINT",i[i.DXGI_FORMAT_R32_TYPELESS=39]="DXGI_FORMAT_R32_TYPELESS",i[i.DXGI_FORMAT_D32_FLOAT=40]="DXGI_FORMAT_D32_FLOAT",i[i.DXGI_FORMAT_R32_FLOAT=41]="DXGI_FORMAT_R32_FLOAT",i[i.DXGI_FORMAT_R32_UINT=42]="DXGI_FORMAT_R32_UINT",i[i.DXGI_FORMAT_R32_SINT=43]="DXGI_FORMAT_R32_SINT",i[i.DXGI_FORMAT_R24G8_TYPELESS=44]="DXGI_FORMAT_R24G8_TYPELESS",i[i.DXGI_FORMAT_D24_UNORM_S8_UINT=45]="DXGI_FORMAT_D24_UNORM_S8_UINT",i[i.DXGI_FORMAT_R24_UNORM_X8_TYPELESS=46]="DXGI_FORMAT_R24_UNORM_X8_TYPELESS",i[i.DXGI_FORMAT_X24_TYPELESS_G8_UINT=47]="DXGI_FORMAT_X24_TYPELESS_G8_UINT",i[i.DXGI_FORMAT_R8G8_TYPELESS=48]="DXGI_FORMAT_R8G8_TYPELESS",i[i.DXGI_FORMAT_R8G8_UNORM=49]="DXGI_FORMAT_R8G8_UNORM",i[i.DXGI_FORMAT_R8G8_UINT=50]="DXGI_FORMAT_R8G8_UINT",i[i.DXGI_FORMAT_R8G8_SNORM=51]="DXGI_FORMAT_R8G8_SNORM",i[i.DXGI_FORMAT_R8G8_SINT=52]="DXGI_FORMAT_R8G8_SINT",i[i.DXGI_FORMAT_R16_TYPELESS=53]="DXGI_FORMAT_R16_TYPELESS",i[i.DXGI_FORMAT_R16_FLOAT=54]="DXGI_FORMAT_R16_FLOAT",i[i.DXGI_FORMAT_D16_UNORM=55]="DXGI_FORMAT_D16_UNORM",i[i.DXGI_FORMAT_R16_UNORM=56]="DXGI_FORMAT_R16_UNORM",i[i.DXGI_FORMAT_R16_UINT=57]="DXGI_FORMAT_R16_UINT",i[i.DXGI_FORMAT_R16_SNORM=58]="DXGI_FORMAT_R16_SNORM",i[i.DXGI_FORMAT_R16_SINT=59]="DXGI_FORMAT_R16_SINT",i[i.DXGI_FORMAT_R8_TYPELESS=60]="DXGI_FORMAT_R8_TYPELESS",i[i.DXGI_FORMAT_R8_UNORM=61]="DXGI_FORMAT_R8_UNORM",i[i.DXGI_FORMAT_R8_UINT=62]="DXGI_FORMAT_R8_UINT",i[i.DXGI_FORMAT_R8_SNORM=63]="DXGI_FORMAT_R8_SNORM",i[i.DXGI_FORMAT_R8_SINT=64]="DXGI_FORMAT_R8_SINT",i[i.DXGI_FORMAT_A8_UNORM=65]="DXGI_FORMAT_A8_UNORM",i[i.DXGI_FORMAT_R1_UNORM=66]="DXGI_FORMAT_R1_UNORM",i[i.DXGI_FORMAT_R9G9B9E5_SHAREDEXP=67]="DXGI_FORMAT_R9G9B9E5_SHAREDEXP",i[i.DXGI_FORMAT_R8G8_B8G8_UNORM=68]="DXGI_FORMAT_R8G8_B8G8_UNORM",i[i.DXGI_FORMAT_G8R8_G8B8_UNORM=69]="DXGI_FORMAT_G8R8_G8B8_UNORM",i[i.DXGI_FORMAT_BC1_TYPELESS=70]="DXGI_FORMAT_BC1_TYPELESS",i[i.DXGI_FORMAT_BC1_UNORM=71]="DXGI_FORMAT_BC1_UNORM",i[i.DXGI_FORMAT_BC1_UNORM_SRGB=72]="DXGI_FORMAT_BC1_UNORM_SRGB",i[i.DXGI_FORMAT_BC2_TYPELESS=73]="DXGI_FORMAT_BC2_TYPELESS",i[i.DXGI_FORMAT_BC2_UNORM=74]="DXGI_FORMAT_BC2_UNORM",i[i.DXGI_FORMAT_BC2_UNORM_SRGB=75]="DXGI_FORMAT_BC2_UNORM_SRGB",i[i.DXGI_FORMAT_BC3_TYPELESS=76]="DXGI_FORMAT_BC3_TYPELESS",i[i.DXGI_FORMAT_BC3_UNORM=77]="DXGI_FORMAT_BC3_UNORM",i[i.DXGI_FORMAT_BC3_UNORM_SRGB=78]="DXGI_FORMAT_BC3_UNORM_SRGB",i[i.DXGI_FORMAT_BC4_TYPELESS=79]="DXGI_FORMAT_BC4_TYPELESS",i[i.DXGI_FORMAT_BC4_UNORM=80]="DXGI_FORMAT_BC4_UNORM",i[i.DXGI_FORMAT_BC4_SNORM=81]="DXGI_FORMAT_BC4_SNORM",i[i.DXGI_FORMAT_BC5_TYPELESS=82]="DXGI_FORMAT_BC5_TYPELESS",i[i.DXGI_FORMAT_BC5_UNORM=83]="DXGI_FORMAT_BC5_UNORM",i[i.DXGI_FORMAT_BC5_SNORM=84]="DXGI_FORMAT_BC5_SNORM",i[i.DXGI_FORMAT_B5G6R5_UNORM=85]="DXGI_FORMAT_B5G6R5_UNORM",i[i.DXGI_FORMAT_B5G5R5A1_UNORM=86]="DXGI_FORMAT_B5G5R5A1_UNORM",i[i.DXGI_FORMAT_B8G8R8A8_UNORM=87]="DXGI_FORMAT_B8G8R8A8_UNORM",i[i.DXGI_FORMAT_B8G8R8X8_UNORM=88]="DXGI_FORMAT_B8G8R8X8_UNORM",i[i.DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM=89]="DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM",i[i.DXGI_FORMAT_B8G8R8A8_TYPELESS=90]="DXGI_FORMAT_B8G8R8A8_TYPELESS",i[i.DXGI_FORMAT_B8G8R8A8_UNORM_SRGB=91]="DXGI_FORMAT_B8G8R8A8_UNORM_SRGB",i[i.DXGI_FORMAT_B8G8R8X8_TYPELESS=92]="DXGI_FORMAT_B8G8R8X8_TYPELESS",i[i.DXGI_FORMAT_B8G8R8X8_UNORM_SRGB=93]="DXGI_FORMAT_B8G8R8X8_UNORM_SRGB",i[i.DXGI_FORMAT_BC6H_TYPELESS=94]="DXGI_FORMAT_BC6H_TYPELESS",i[i.DXGI_FORMAT_BC6H_UF16=95]="DXGI_FORMAT_BC6H_UF16",i[i.DXGI_FORMAT_BC6H_SF16=96]="DXGI_FORMAT_BC6H_SF16",i[i.DXGI_FORMAT_BC7_TYPELESS=97]="DXGI_FORMAT_BC7_TYPELESS",i[i.DXGI_FORMAT_BC7_UNORM=98]="DXGI_FORMAT_BC7_UNORM",i[i.DXGI_FORMAT_BC7_UNORM_SRGB=99]="DXGI_FORMAT_BC7_UNORM_SRGB",i[i.DXGI_FORMAT_AYUV=100]="DXGI_FORMAT_AYUV",i[i.DXGI_FORMAT_Y410=101]="DXGI_FORMAT_Y410",i[i.DXGI_FORMAT_Y416=102]="DXGI_FORMAT_Y416",i[i.DXGI_FORMAT_NV12=103]="DXGI_FORMAT_NV12",i[i.DXGI_FORMAT_P010=104]="DXGI_FORMAT_P010",i[i.DXGI_FORMAT_P016=105]="DXGI_FORMAT_P016",i[i.DXGI_FORMAT_420_OPAQUE=106]="DXGI_FORMAT_420_OPAQUE",i[i.DXGI_FORMAT_YUY2=107]="DXGI_FORMAT_YUY2",i[i.DXGI_FORMAT_Y210=108]="DXGI_FORMAT_Y210",i[i.DXGI_FORMAT_Y216=109]="DXGI_FORMAT_Y216",i[i.DXGI_FORMAT_NV11=110]="DXGI_FORMAT_NV11",i[i.DXGI_FORMAT_AI44=111]="DXGI_FORMAT_AI44",i[i.DXGI_FORMAT_IA44=112]="DXGI_FORMAT_IA44",i[i.DXGI_FORMAT_P8=113]="DXGI_FORMAT_P8",i[i.DXGI_FORMAT_A8P8=114]="DXGI_FORMAT_A8P8",i[i.DXGI_FORMAT_B4G4R4A4_UNORM=115]="DXGI_FORMAT_B4G4R4A4_UNORM",i[i.DXGI_FORMAT_P208=116]="DXGI_FORMAT_P208",i[i.DXGI_FORMAT_V208=117]="DXGI_FORMAT_V208",i[i.DXGI_FORMAT_V408=118]="DXGI_FORMAT_V408",i[i.DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE=119]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE",i[i.DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE=120]="DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE",i[i.DXGI_FORMAT_FORCE_UINT=121]="DXGI_FORMAT_FORCE_UINT",i))(pp||{}),mp=(i=>(i[i.DDS_DIMENSION_TEXTURE1D=2]="DDS_DIMENSION_TEXTURE1D",i[i.DDS_DIMENSION_TEXTURE2D=3]="DDS_DIMENSION_TEXTURE2D",i[i.DDS_DIMENSION_TEXTURE3D=6]="DDS_DIMENSION_TEXTURE3D",i))(mp||{});const gp=1,_p=2,vp=4,yp=64,xp=512,Tp=131072,bp=827611204,Ep=861165636,wp=894720068,Sp=808540228,Ap=4,Cp={[bp]:Ct.COMPRESSED_RGBA_S3TC_DXT1_EXT,[Ep]:Ct.COMPRESSED_RGBA_S3TC_DXT3_EXT,[wp]:Ct.COMPRESSED_RGBA_S3TC_DXT5_EXT},Rp={[70]:Ct.COMPRESSED_RGBA_S3TC_DXT1_EXT,[71]:Ct.COMPRESSED_RGBA_S3TC_DXT1_EXT,[73]:Ct.COMPRESSED_RGBA_S3TC_DXT3_EXT,[74]:Ct.COMPRESSED_RGBA_S3TC_DXT3_EXT,[76]:Ct.COMPRESSED_RGBA_S3TC_DXT5_EXT,[77]:Ct.COMPRESSED_RGBA_S3TC_DXT5_EXT,[72]:Ct.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,[75]:Ct.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,[78]:Ct.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT};function Va(i){const t=new Uint32Array(i);if(t[0]!==dp)throw new Error("Invalid DDS file magic word");const s=new Uint32Array(i,0,bs/Uint32Array.BYTES_PER_ELEMENT),r=s[Es.HEIGHT],n=s[Es.WIDTH],o=s[Es.MIPMAP_COUNT],a=new Uint32Array(i,Es.PIXEL_FORMAT*Uint32Array.BYTES_PER_ELEMENT,up/Uint32Array.BYTES_PER_ELEMENT),h=a[gp];if(h&vp){const l=a[fp.FOURCC];if(l!==Sp){const _=Cp[l],y=xn+bs,E=new Uint8Array(i,y);return[new _e(E,{format:_,width:n,height:r,levels:o})]}const c=xn+bs,u=new Uint32Array(t.buffer,c,Xa/Uint32Array.BYTES_PER_ELEMENT),d=u[ws.DXGI_FORMAT],f=u[ws.RESOURCE_DIMENSION],p=u[ws.MISC_FLAG],g=u[ws.ARRAY_SIZE],m=Rp[d];if(m===void 0)throw new Error(`DDSParser cannot parse texture data with DXGI format ${d}`);if(p===Ap)throw new Error("DDSParser does not support cubemap textures");if(f===6)throw new Error("DDSParser does not supported 3D texture data");const x=new Array,T=xn+bs+Xa;if(g===1)x.push(new Uint8Array(i,T));else{const _=bi[m];let y=0,E=n,B=r;for(let b=0;b<o;b++){const I=Math.max(1,E+3&-4),O=Math.max(1,B+3&-4);y+=I*O*_,E=E>>>1,B=B>>>1}let A=T;for(let b=0;b<g;b++)x.push(new Uint8Array(i,A,y)),A+=y}return x.map(_=>new _e(_,{format:m,width:n,height:r,levels:o}))}throw h&yp?new Error("DDSParser does not support uncompressed texture data."):h&xp?new Error("DDSParser does not supported YUV uncompressed texture data."):h&Tp?new Error("DDSParser does not support single-channel (lumninance) texture data!"):h&_p?new Error("DDSParser does not support single-channel (alpha) texture data!"):new Error("DDSParser failed to load a texture file due to an unknown reason!")}const za=[171,75,84,88,32,49,49,187,13,10,26,10],Ip=67305985,$t={FILE_IDENTIFIER:0,ENDIANNESS:12,GL_TYPE:16,GL_TYPE_SIZE:20,GL_FORMAT:24,GL_INTERNAL_FORMAT:28,GL_BASE_INTERNAL_FORMAT:32,PIXEL_WIDTH:36,PIXEL_HEIGHT:40,PIXEL_DEPTH:44,NUMBER_OF_ARRAY_ELEMENTS:48,NUMBER_OF_FACES:52,NUMBER_OF_MIPMAP_LEVELS:56,BYTES_OF_KEY_VALUE_DATA:60},Tn=64,bn={[H.UNSIGNED_BYTE]:1,[H.UNSIGNED_SHORT]:2,[H.INT]:4,[H.UNSIGNED_INT]:4,[H.FLOAT]:4,[H.HALF_FLOAT]:8},Wa={[N.RGBA]:4,[N.RGB]:3,[N.RG]:2,[N.RED]:1,[N.LUMINANCE]:1,[N.LUMINANCE_ALPHA]:2,[N.ALPHA]:1},ja={[H.UNSIGNED_SHORT_4_4_4_4]:2,[H.UNSIGNED_SHORT_5_5_5_1]:2,[H.UNSIGNED_SHORT_5_6_5]:2};function Ya(i,t,e=!1){const s=new DataView(t);if(!Pp(i,s))return null;const r=s.getUint32($t.ENDIANNESS,!0)===Ip,n=s.getUint32($t.GL_TYPE,r),o=s.getUint32($t.GL_FORMAT,r),a=s.getUint32($t.GL_INTERNAL_FORMAT,r),h=s.getUint32($t.PIXEL_WIDTH,r),l=s.getUint32($t.PIXEL_HEIGHT,r)||1,c=s.getUint32($t.PIXEL_DEPTH,r)||1,u=s.getUint32($t.NUMBER_OF_ARRAY_ELEMENTS,r)||1,d=s.getUint32($t.NUMBER_OF_FACES,r),f=s.getUint32($t.NUMBER_OF_MIPMAP_LEVELS,r),p=s.getUint32($t.BYTES_OF_KEY_VALUE_DATA,r);if(l===0||c!==1)throw new Error("Only 2D textures are supported");if(d!==1)throw new Error("CubeTextures are not supported by KTXLoader yet!");if(u!==1)throw new Error("WebGL does not support array textures");const g=4,m=4,x=h+3&-4,T=l+3&-4,_=new Array(u);let y=h*l;n===0&&(y=x*T);let E;if(n!==0?bn[n]?E=bn[n]*Wa[o]:E=ja[n]:E=bi[a],E===void 0)throw new Error("Unable to resolve the pixel format stored in the *.ktx file!");const B=e?Bp(s,p,r):null;let b=y*E,I=h,O=l,w=x,D=T,R=Tn+p;for(let S=0;S<f;S++){const X=s.getUint32(R,r);let z=R+4;for(let k=0;k<u;k++){let J=_[k];J||(J=_[k]=new Array(f)),J[S]={levelID:S,levelWidth:f>1||n!==0?I:w,levelHeight:f>1||n!==0?O:D,levelBuffer:new Uint8Array(t,z,b)},z+=b}R+=X+4,R=R%4!==0?R+4-R%4:R,I=I>>1||1,O=O>>1||1,w=I+g-1&~(g-1),D=O+m-1&~(m-1),b=w*D*E}return n!==0?{uncompressed:_.map(S=>{let X=S[0].levelBuffer,z=!1;return n===H.FLOAT?X=new Float32Array(S[0].levelBuffer.buffer,S[0].levelBuffer.byteOffset,S[0].levelBuffer.byteLength/4):n===H.UNSIGNED_INT?(z=!0,X=new Uint32Array(S[0].levelBuffer.buffer,S[0].levelBuffer.byteOffset,S[0].levelBuffer.byteLength/4)):n===H.INT&&(z=!0,X=new Int32Array(S[0].levelBuffer.buffer,S[0].levelBuffer.byteOffset,S[0].levelBuffer.byteLength/4)),{resource:new Xe(X,{width:S[0].levelWidth,height:S[0].levelHeight}),type:n,format:z?Mp(o):o}}),kvData:B}:{compressed:_.map(S=>new _e(null,{format:a,width:h,height:l,levels:f,levelBuffers:S})),kvData:B}}function Pp(i,t){for(let e=0;e<za.length;e++)if(t.getUint8(e)!==za[e])return console.error(`${i} is not a valid *.ktx file!`),!1;return!0}function Mp(i){switch(i){case N.RGBA:return N.RGBA_INTEGER;case N.RGB:return N.RGB_INTEGER;case N.RG:return N.RG_INTEGER;case N.RED:return N.RED_INTEGER;default:return i}}function Bp(i,t,e){const s=new Map;let r=0;for(;r<t;){const n=i.getUint32(Tn+r,e),o=Tn+r+4,a=3-(n+3)%4;if(n===0||n>t-r){console.error("KTXLoader: keyAndValueByteSize out of bounds");break}let h=0;for(;h<n&&i.getUint8(o+h)!==0;h++);if(h===-1){console.error("KTXLoader: Failed to find null byte terminating kvData key");break}const l=new TextDecoder().decode(new Uint8Array(i.buffer,o,h)),c=new DataView(i.buffer,o+h+1,n-h-1);s.set(l,c),r+=4+n+a}return s}const $a={extension:{type:F.LoadParser,priority:Yt.High},test(i){return vi(i,".dds")},async load(i,t,e){const r=await(await P.ADAPTER.fetch(i)).arrayBuffer(),o=Va(r).map(a=>{const h=new $(a,re({mipmap:Gt.OFF,alphaMode:Nt.NO_PREMULTIPLIED_ALPHA,resolution:he(i)},t.data));return Ti(h,e,i)});return o.length===1?o[0]:o},unload(i){Array.isArray(i)?i.forEach(t=>t.destroy(!0)):i.destroy(!0)}};U.add($a);const qa={extension:{type:F.LoadParser,priority:Yt.High},test(i){return vi(i,".ktx")},async load(i,t,e){const r=await(await P.ADAPTER.fetch(i)).arrayBuffer(),{compressed:n,uncompressed:o,kvData:a}=Ya(i,r),h=n!=null?n:o,l=re({mipmap:Gt.OFF,alphaMode:Nt.NO_PREMULTIPLIED_ALPHA,resolution:he(i)},t.data),c=h.map(u=>{h===o&&Object.assign(l,{type:u.type,format:u.format});const d=new $(u,l);return d.ktxKeyValueData=a,Ti(d,e,i)});return c.length===1?c[0]:c},unload(i){Array.isArray(i)?i.forEach(t=>t.destroy(!0)):i.destroy(!0)}};U.add(qa);const Ka={extension:F.ResolveParser,test:i=>{const e=i.split("?")[0].split(".").pop();return["basis","ktx","dds"].includes(e)},parse:i=>{var s,r,n,o;if(i.split("?")[0].split(".").pop()==="ktx"){const a=[".s3tc.ktx",".s3tc_sRGB.ktx",".etc.ktx",".etc1.ktx",".pvrt.ktx",".atc.ktx",".astc.ktx"];if(a.some(h=>i.endsWith(h)))return{resolution:parseFloat((r=(s=P.RETINA_PREFIX.exec(i))==null?void 0:s[1])!=null?r:"1"),format:a.find(h=>i.endsWith(h)),src:i}}return{resolution:parseFloat((o=(n=P.RETINA_PREFIX.exec(i))==null?void 0:n[1])!=null?o:"1"),format:i.split(".").pop(),src:i}}};U.add(Ka);const Za=new j,Qa=4,En=class{constructor(i){this.renderer=i}async image(i,t,e){const s=new Image;return s.src=await this.base64(i,t,e),s}async base64(i,t,e){const s=this.canvas(i);if(s.toDataURL!==void 0)return s.toDataURL(t,e);if(s.convertToBlob!==void 0){const r=await s.convertToBlob({type:t,quality:e});return await new Promise(n=>{const o=new FileReader;o.onload=()=>n(o.result),o.readAsDataURL(r)})}throw new Error("Extract.base64() requires ICanvas.toDataURL or ICanvas.convertToBlob to be implemented")}canvas(i,t){const e=this.renderer;let s,r=!1,n,o=!1;i&&(i instanceof Ot?n=i:(n=this.renderer.generateTexture(i),o=!0)),n?(s=n.baseTexture.resolution,t=t!=null?t:n.frame,r=!1,e.renderTexture.bind(n)):(s=e.resolution,t||(t=Za,t.width=e.width,t.height=e.height),r=!0,e.renderTexture.bind(null));const a=Math.round(t.width*s),h=Math.round(t.height*s);let l=new Ge(a,h,1);const c=new Uint8Array(Qa*a*h),u=e.gl;u.readPixels(Math.round(t.x*s),Math.round(t.y*s),a,h,u.RGBA,u.UNSIGNED_BYTE,c);const d=l.context.getImageData(0,0,a,h);if(En.arrayPostDivide(c,d.data),l.context.putImageData(d,0,0),r){const f=new Ge(l.width,l.height,1);f.context.scale(1,-1),f.context.drawImage(l.canvas,0,-h),l.destroy(),l=f}return o&&n.destroy(!0),l.canvas}pixels(i,t){const e=this.renderer;let s,r,n=!1;i&&(i instanceof Ot?r=i:(r=this.renderer.generateTexture(i),n=!0)),r?(s=r.baseTexture.resolution,t=t!=null?t:r.frame,e.renderTexture.bind(r)):(s=e.resolution,t||(t=Za,t.width=e.width,t.height=e.height),e.renderTexture.bind(null));const o=Math.round(t.width*s),a=Math.round(t.height*s),h=new Uint8Array(Qa*o*a),l=e.gl;return l.readPixels(Math.round(t.x*s),Math.round(t.y*s),o,a,l.RGBA,l.UNSIGNED_BYTE,h),n&&r.destroy(!0),En.arrayPostDivide(h,h),h}destroy(){this.renderer=null}static arrayPostDivide(i,t){for(let e=0;e<i.length;e+=4){const s=t[e+3]=i[e+3];s!==0?(t[e]=Math.round(Math.min(i[e]*255/s,255)),t[e+1]=Math.round(Math.min(i[e+1]*255/s,255)),t[e+2]=Math.round(Math.min(i[e+2]*255/s,255))):(t[e]=i[e],t[e+1]=i[e+1],t[e+2]=i[e+2])}}};let wn=En;wn.extension={name:"extract",type:F.RendererSystem},U.add(wn);var fe=(i=>(i.MITER="miter",i.BEVEL="bevel",i.ROUND="round",i))(fe||{}),ve=(i=>(i.BUTT="butt",i.ROUND="round",i.SQUARE="square",i))(ve||{});const $e={adaptive:!0,maxLength:10,minSegments:8,maxSegments:2048,epsilon:1e-4,_segmentsCount(i,t=20){if(!this.adaptive||!i||isNaN(i))return t;let e=Math.ceil(i/this.maxLength);return e<this.minSegments?e=this.minSegments:e>this.maxSegments&&(e=this.maxSegments),e}};class Ei{constructor(){this.color=16777215,this.alpha=1,this.texture=L.WHITE,this.matrix=null,this.visible=!1,this.reset()}clone(){const t=new Ei;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t}reset(){this.color=16777215,this.alpha=1,this.texture=L.WHITE,this.matrix=null,this.visible=!1}destroy(){this.texture=null,this.matrix=null}}function Ja(i,t=!1){const e=i.length;if(e<6)return;let s=0;for(let r=0,n=i[e-2],o=i[e-1];r<e;r+=2){const a=i[r],h=i[r+1];s+=(a-n)*(h+o),n=a,o=h}if(!t&&s>0||t&&s<=0){const r=e/2;for(let n=r+r%2;n<e;n+=2){const o=e-n-2,a=e-n-1,h=n,l=n+1;[i[o],i[h]]=[i[h],i[o]],[i[a],i[l]]=[i[l],i[a]]}}}const Sn={build(i){i.points=i.shape.points.slice()},triangulate(i,t){let e=i.points;const s=i.holes,r=t.points,n=t.indices;if(e.length>=6){Ja(e,!1);const o=[];for(let l=0;l<s.length;l++){const c=s[l];Ja(c.points,!0),o.push(e.length/2),e=e.concat(c.points)}const a=Ws(e,o,2);if(!a)return;const h=r.length/2;for(let l=0;l<a.length;l+=3)n.push(a[l]+h),n.push(a[l+1]+h),n.push(a[l+2]+h);for(let l=0;l<e.length;l++)r.push(e[l])}}},wi={build(i){const t=i.points;let e,s,r,n,o,a;if(i.type===et.CIRC){const p=i.shape;e=p.x,s=p.y,o=a=p.radius,r=n=0}else if(i.type===et.ELIP){const p=i.shape;e=p.x,s=p.y,o=p.width,a=p.height,r=n=0}else{const p=i.shape,g=p.width/2,m=p.height/2;e=p.x+g,s=p.y+m,o=a=Math.max(0,Math.min(p.radius,Math.min(g,m))),r=g-o,n=m-a}if(!(o>=0&&a>=0&&r>=0&&n>=0)){t.length=0;return}const h=Math.ceil(2.3*Math.sqrt(o+a)),l=h*8+(r?4:0)+(n?4:0);if(t.length=l,l===0)return;if(h===0){t.length=8,t[0]=t[6]=e+r,t[1]=t[3]=s+n,t[2]=t[4]=e-r,t[5]=t[7]=s-n;return}let c=0,u=h*4+(r?2:0)+2,d=u,f=l;{const p=r+o,g=n,m=e+p,x=e-p,T=s+g;if(t[c++]=m,t[c++]=T,t[--u]=T,t[--u]=x,n){const _=s-g;t[d++]=x,t[d++]=_,t[--f]=_,t[--f]=m}}for(let p=1;p<h;p++){const g=Math.PI/2*(p/h),m=r+Math.cos(g)*o,x=n+Math.sin(g)*a,T=e+m,_=e-m,y=s+x,E=s-x;t[c++]=T,t[c++]=y,t[--u]=y,t[--u]=_,t[d++]=_,t[d++]=E,t[--f]=E,t[--f]=T}{const p=r,g=n+a,m=e+p,x=e-p,T=s+g,_=s-g;t[c++]=m,t[c++]=T,t[--f]=_,t[--f]=m,r&&(t[c++]=x,t[c++]=T,t[--f]=_,t[--f]=x)}},triangulate(i,t){const e=i.points,s=t.points,r=t.indices;if(e.length===0)return;let n=s.length/2;const o=n;let a,h;if(i.type!==et.RREC){const c=i.shape;a=c.x,h=c.y}else{const c=i.shape;a=c.x+c.width/2,h=c.y+c.height/2}const l=i.matrix;s.push(i.matrix?l.a*a+l.c*h+l.tx:a,i.matrix?l.b*a+l.d*h+l.ty:h),n++,s.push(e[0],e[1]);for(let c=2;c<e.length;c+=2)s.push(e[c],e[c+1]),r.push(n++,o,n);r.push(o+1,o,n)}},th={build(i){const t=i.shape,e=t.x,s=t.y,r=t.width,n=t.height,o=i.points;o.length=0,r>=0&&n>=0&&o.push(e,s,e+r,s,e+r,s+n,e,s+n)},triangulate(i,t){const e=i.points,s=t.points;if(e.length===0)return;const r=s.length/2;s.push(e[0],e[1],e[2],e[3],e[6],e[7],e[4],e[5]),t.indices.push(r,r+1,r+2,r+1,r+2,r+3)}},eh={build(i){wi.build(i)},triangulate(i,t){wi.triangulate(i,t)}};function ih(i,t,e,s,r,n,o,a){const h=i-e*r,l=t-s*r,c=i+e*n,u=t+s*n;let d,f;o?(d=s,f=-e):(d=-s,f=e);const p=h+d,g=l+f,m=c+d,x=u+f;return a.push(p,g),a.push(m,x),2}function De(i,t,e,s,r,n,o,a){const h=e-i,l=s-t;let c=Math.atan2(h,l),u=Math.atan2(r-i,n-t);a&&c<u?c+=Math.PI*2:!a&&c>u&&(u+=Math.PI*2);let d=c;const f=u-c,p=Math.abs(f),g=Math.sqrt(h*h+l*l),m=(15*p*Math.sqrt(g)/Math.PI>>0)+1,x=f/m;if(d+=x,a){o.push(i,t),o.push(e,s);for(let T=1,_=d;T<m;T++,_+=x)o.push(i,t),o.push(i+Math.sin(_)*g,t+Math.cos(_)*g);o.push(i,t),o.push(r,n)}else{o.push(e,s),o.push(i,t);for(let T=1,_=d;T<m;T++,_+=x)o.push(i+Math.sin(_)*g,t+Math.cos(_)*g),o.push(i,t);o.push(r,n),o.push(i,t)}return m*2}function Dp(i,t){const e=i.shape;let s=i.points||e.points.slice();const r=t.closePointEps;if(s.length===0)return;const n=i.lineStyle,o=new W(s[0],s[1]),a=new W(s[s.length-2],s[s.length-1]),h=e.type!==et.POLY||e.closeStroke,l=Math.abs(o.x-a.x)<r&&Math.abs(o.y-a.y)<r;if(h){s=s.slice(),l&&(s.pop(),s.pop(),a.set(s[s.length-2],s[s.length-1]));const k=(o.x+a.x)*.5,J=(a.y+o.y)*.5;s.unshift(k,J),s.push(k,J)}const c=t.points,u=s.length/2;let d=s.length;const f=c.length/2,p=n.width/2,g=p*p,m=n.miterLimit*n.miterLimit;let x=s[0],T=s[1],_=s[2],y=s[3],E=0,B=0,A=-(T-y),b=x-_,I=0,O=0,w=Math.sqrt(A*A+b*b);A/=w,b/=w,A*=p,b*=p;const D=n.alignment,R=(1-D)*2,S=D*2;h||(n.cap===ve.ROUND?d+=De(x-A*(R-S)*.5,T-b*(R-S)*.5,x-A*R,T-b*R,x+A*S,T+b*S,c,!0)+2:n.cap===ve.SQUARE&&(d+=ih(x,T,A,b,R,S,!0,c))),c.push(x-A*R,T-b*R),c.push(x+A*S,T+b*S);for(let k=1;k<u-1;++k){x=s[(k-1)*2],T=s[(k-1)*2+1],_=s[k*2],y=s[k*2+1],E=s[(k+1)*2],B=s[(k+1)*2+1],A=-(T-y),b=x-_,w=Math.sqrt(A*A+b*b),A/=w,b/=w,A*=p,b*=p,I=-(y-B),O=_-E,w=Math.sqrt(I*I+O*O),I/=w,O/=w,I*=p,O*=p;const J=_-x,st=T-y,M=_-E,V=B-y,Y=J*M+st*V,q=st*M-V*J,K=q<0;if(Math.abs(q)<.001*Math.abs(Y)){c.push(_-A*R,y-b*R),c.push(_+A*S,y+b*S),Y>=0&&(n.join===fe.ROUND?d+=De(_,y,_-A*R,y-b*R,_-I*R,y-O*R,c,!1)+4:d+=2,c.push(_-I*S,y-O*S),c.push(_+I*R,y+O*R));continue}const tt=(-A+x)*(-b+y)-(-A+_)*(-b+T),Q=(-I+E)*(-O+y)-(-I+_)*(-O+B),it=(J*Q-M*tt)/q,ht=(V*tt-st*Q)/q,gt=(it-_)*(it-_)+(ht-y)*(ht-y),rt=_+(it-_)*R,ct=y+(ht-y)*R,_t=_-(it-_)*S,vt=y-(ht-y)*S,ie=Math.min(J*J+st*st,M*M+V*V),se=K?R:S,Ni=ie+se*se*g;gt<=Ni?n.join===fe.BEVEL||gt/g>m?(K?(c.push(rt,ct),c.push(_+A*S,y+b*S),c.push(rt,ct),c.push(_+I*S,y+O*S)):(c.push(_-A*R,y-b*R),c.push(_t,vt),c.push(_-I*R,y-O*R),c.push(_t,vt)),d+=2):n.join===fe.ROUND?K?(c.push(rt,ct),c.push(_+A*S,y+b*S),d+=De(_,y,_+A*S,y+b*S,_+I*S,y+O*S,c,!0)+4,c.push(rt,ct),c.push(_+I*S,y+O*S)):(c.push(_-A*R,y-b*R),c.push(_t,vt),d+=De(_,y,_-A*R,y-b*R,_-I*R,y-O*R,c,!1)+4,c.push(_-I*R,y-O*R),c.push(_t,vt)):(c.push(rt,ct),c.push(_t,vt)):(c.push(_-A*R,y-b*R),c.push(_+A*S,y+b*S),n.join===fe.ROUND?K?d+=De(_,y,_+A*S,y+b*S,_+I*S,y+O*S,c,!0)+2:d+=De(_,y,_-A*R,y-b*R,_-I*R,y-O*R,c,!1)+2:n.join===fe.MITER&&gt/g<=m&&(K?(c.push(_t,vt),c.push(_t,vt)):(c.push(rt,ct),c.push(rt,ct)),d+=2),c.push(_-I*R,y-O*R),c.push(_+I*S,y+O*S),d+=2)}x=s[(u-2)*2],T=s[(u-2)*2+1],_=s[(u-1)*2],y=s[(u-1)*2+1],A=-(T-y),b=x-_,w=Math.sqrt(A*A+b*b),A/=w,b/=w,A*=p,b*=p,c.push(_-A*R,y-b*R),c.push(_+A*S,y+b*S),h||(n.cap===ve.ROUND?d+=De(_-A*(R-S)*.5,y-b*(R-S)*.5,_-A*R,y-b*R,_+A*S,y+b*S,c,!1)+2:n.cap===ve.SQUARE&&(d+=ih(_,y,A,b,R,S,!1,c)));const X=t.indices,z=$e.epsilon*$e.epsilon;for(let k=f;k<d+f-2;++k)x=c[k*2],T=c[k*2+1],_=c[(k+1)*2],y=c[(k+1)*2+1],E=c[(k+2)*2],B=c[(k+2)*2+1],!(Math.abs(x*(y-B)+_*(B-T)+E*(T-y))<z)&&X.push(k,k+1,k+2)}function Np(i,t){let e=0;const s=i.shape,r=i.points||s.points,n=s.type!==et.POLY||s.closeStroke;if(r.length===0)return;const o=t.points,a=t.indices,h=r.length/2,l=o.length/2;let c=l;for(o.push(r[0],r[1]),e=1;e<h;e++)o.push(r[e*2],r[e*2+1]),a.push(c,c+1),c++;n&&a.push(c,l)}function An(i,t){i.lineStyle.native?Np(i,t):Dp(i,t)}class Cn{static curveTo(t,e,s,r,n,o){const a=o[o.length-2],l=o[o.length-1]-e,c=a-t,u=r-e,d=s-t,f=Math.abs(l*d-c*u);if(f<1e-8||n===0)return(o[o.length-2]!==t||o[o.length-1]!==e)&&o.push(t,e),null;const p=l*l+c*c,g=u*u+d*d,m=l*u+c*d,x=n*Math.sqrt(p)/f,T=n*Math.sqrt(g)/f,_=x*m/p,y=T*m/g,E=x*d+T*c,B=x*u+T*l,A=c*(T+_),b=l*(T+_),I=d*(x+y),O=u*(x+y),w=Math.atan2(b-B,A-E),D=Math.atan2(O-B,I-E);return{cx:E+t,cy:B+e,radius:n,startAngle:w,endAngle:D,anticlockwise:c*u>d*l}}static arc(t,e,s,r,n,o,a,h,l){const c=a-o,u=$e._segmentsCount(Math.abs(c)*n,Math.ceil(Math.abs(c)/Ke)*40),d=c/(u*2),f=d*2,p=Math.cos(d),g=Math.sin(d),m=u-1,x=m%1/m;for(let T=0;T<=m;++T){const _=T+x*T,y=d+o+f*_,E=Math.cos(y),B=-Math.sin(y);l.push((p*E+g*B)*n+s,(p*-B+g*E)*n+r)}}}class Ss{static curveLength(t,e,s,r,n,o,a,h){let c=0,u=0,d=0,f=0,p=0,g=0,m=0,x=0,T=0,_=0,y=0,E=t,B=e;for(let A=1;A<=10;++A)u=A/10,d=u*u,f=d*u,p=1-u,g=p*p,m=g*p,x=m*t+3*g*u*s+3*p*d*n+f*a,T=m*e+3*g*u*r+3*p*d*o+f*h,_=E-x,y=B-T,E=x,B=T,c+=Math.sqrt(_*_+y*y);return c}static curveTo(t,e,s,r,n,o,a){const h=a[a.length-2],l=a[a.length-1];a.length-=2;const c=$e._segmentsCount(Ss.curveLength(h,l,t,e,s,r,n,o));let u=0,d=0,f=0,p=0,g=0;a.push(h,l);for(let m=1,x=0;m<=c;++m)x=m/c,u=1-x,d=u*u,f=d*u,p=x*x,g=p*x,a.push(f*h+3*d*x*t+3*u*p*s+g*n,f*l+3*d*x*e+3*u*p*r+g*o)}}class As{static curveLength(t,e,s,r,n,o){const a=t-2*s+n,h=e-2*r+o,l=2*s-2*t,c=2*r-2*e,u=4*(a*a+h*h),d=4*(a*l+h*c),f=l*l+c*c,p=2*Math.sqrt(u+d+f),g=Math.sqrt(u),m=2*u*g,x=2*Math.sqrt(f),T=d/g;return(m*p+g*d*(p-x)+(4*f*u-d*d)*Math.log((2*g+T+p)/(T+x)))/(4*m)}static curveTo(t,e,s,r,n){const o=n[n.length-2],a=n[n.length-1],h=$e._segmentsCount(As.curveLength(o,a,t,e,s,r));let l=0,c=0;for(let u=1;u<=h;++u){const d=u/h;l=o+(t-o)*d,c=a+(e-a)*d,n.push(l+(t+(s-t)*d-l)*d,c+(e+(r-e)*d-c)*d)}}}class sh{constructor(){this.reset()}begin(t,e,s){this.reset(),this.style=t,this.start=e,this.attribStart=s}end(t,e){this.attribSize=e-this.attribStart,this.size=t-this.start}reset(){this.style=null,this.size=0,this.start=0,this.attribStart=0,this.attribSize=0}}const Cs={[et.POLY]:Sn,[et.CIRC]:wi,[et.ELIP]:wi,[et.RECT]:th,[et.RREC]:eh},Rn=[],Si=[];class Ai{constructor(t,e=null,s=null,r=null){this.points=[],this.holes=[],this.shape=t,this.lineStyle=s,this.fillStyle=e,this.matrix=r,this.type=t.type}clone(){return new Ai(this.shape,this.fillStyle,this.lineStyle,this.matrix)}destroy(){this.shape=null,this.holes.length=0,this.holes=null,this.points.length=0,this.points=null,this.lineStyle=null,this.fillStyle=null}}const qe=new W,rh=class extends $r{constructor(){super(),this.closePointEps=1e-4,this.boundsPadding=0,this.uvsFloat32=null,this.indicesUint16=null,this.batchable=!1,this.points=[],this.colors=[],this.uvs=[],this.indices=[],this.textureIds=[],this.graphicsData=[],this.drawCalls=[],this.batchDirty=-1,this.batches=[],this.dirty=0,this.cacheDirty=-1,this.clearDirty=0,this.shapeIndex=0,this._bounds=new ui,this.boundsDirty=-1}get bounds(){return this.updateBatches(),this.boundsDirty!==this.dirty&&(this.boundsDirty=this.dirty,this.calculateBounds()),this._bounds}invalidate(){this.boundsDirty=-1,this.dirty++,this.batchDirty++,this.shapeIndex=0,this.points.length=0,this.colors.length=0,this.uvs.length=0,this.indices.length=0,this.textureIds.length=0;for(let i=0;i<this.drawCalls.length;i++)this.drawCalls[i].texArray.clear(),Si.push(this.drawCalls[i]);this.drawCalls.length=0;for(let i=0;i<this.batches.length;i++){const t=this.batches[i];t.reset(),Rn.push(t)}this.batches.length=0}clear(){return this.graphicsData.length>0&&(this.invalidate(),this.clearDirty++,this.graphicsData.length=0),this}drawShape(i,t=null,e=null,s=null){const r=new Ai(i,t,e,s);return this.graphicsData.push(r),this.dirty++,this}drawHole(i,t=null){if(!this.graphicsData.length)return null;const e=new Ai(i,null,null,t),s=this.graphicsData[this.graphicsData.length-1];return e.lineStyle=s.lineStyle,s.holes.push(e),this.dirty++,this}destroy(){super.destroy();for(let i=0;i<this.graphicsData.length;++i)this.graphicsData[i].destroy();this.points.length=0,this.points=null,this.colors.length=0,this.colors=null,this.uvs.length=0,this.uvs=null,this.indices.length=0,this.indices=null,this.indexBuffer.destroy(),this.indexBuffer=null,this.graphicsData.length=0,this.graphicsData=null,this.drawCalls.length=0,this.drawCalls=null,this.batches.length=0,this.batches=null,this._bounds=null}containsPoint(i){const t=this.graphicsData;for(let e=0;e<t.length;++e){const s=t[e];if(!!s.fillStyle.visible&&s.shape&&(s.matrix?s.matrix.applyInverse(i,qe):qe.copyFrom(i),s.shape.contains(qe.x,qe.y))){let r=!1;if(s.holes){for(let n=0;n<s.holes.length;n++)if(s.holes[n].shape.contains(qe.x,qe.y)){r=!0;break}}if(!r)return!0}}return!1}updateBatches(){if(!this.graphicsData.length){this.batchable=!0;return}if(!this.validateBatching())return;this.cacheDirty=this.dirty;const i=this.uvs,t=this.graphicsData;let e=null,s=null;this.batches.length>0&&(e=this.batches[this.batches.length-1],s=e.style);for(let a=this.shapeIndex;a<t.length;a++){this.shapeIndex++;const h=t[a],l=h.fillStyle,c=h.lineStyle;Cs[h.type].build(h),h.matrix&&this.transformPoints(h.points,h.matrix),(l.visible||c.visible)&&this.processHoles(h.holes);for(let d=0;d<2;d++){const f=d===0?l:c;if(!f.visible)continue;const p=f.texture.baseTexture,g=this.indices.length,m=this.points.length/2;p.wrapMode=Kt.REPEAT,d===0?this.processFill(h):this.processLine(h);const x=this.points.length/2-m;x!==0&&(e&&!this._compareStyles(s,f)&&(e.end(g,m),e=null),e||(e=Rn.pop()||new sh,e.begin(f,g,m),this.batches.push(e),s=f),this.addUvs(this.points,i,f.texture,m,x,f.matrix))}}const r=this.indices.length,n=this.points.length/2;if(e&&e.end(r,n),this.batches.length===0){this.batchable=!0;return}const o=n>65535;this.indicesUint16&&this.indices.length===this.indicesUint16.length&&o===this.indicesUint16.BYTES_PER_ELEMENT>2?this.indicesUint16.set(this.indices):this.indicesUint16=o?new Uint32Array(this.indices):new Uint16Array(this.indices),this.batchable=this.isBatchable(),this.batchable?this.packBatches():this.buildDrawCalls()}_compareStyles(i,t){return!(!i||!t||i.texture.baseTexture!==t.texture.baseTexture||i.color+i.alpha!==t.color+t.alpha||!!i.native!=!!t.native)}validateBatching(){if(this.dirty===this.cacheDirty||!this.graphicsData.length)return!1;for(let i=0,t=this.graphicsData.length;i<t;i++){const e=this.graphicsData[i],s=e.fillStyle,r=e.lineStyle;if(s&&!s.texture.baseTexture.valid||r&&!r.texture.baseTexture.valid)return!1}return!0}packBatches(){this.batchDirty++,this.uvsFloat32=new Float32Array(this.uvs);const i=this.batches;for(let t=0,e=i.length;t<e;t++){const s=i[t];for(let r=0;r<s.size;r++){const n=s.start+r;this.indicesUint16[n]=this.indicesUint16[n]-s.attribStart}}}isBatchable(){if(this.points.length>65535*2)return!1;const i=this.batches;for(let t=0;t<i.length;t++)if(i[t].style.native)return!1;return this.points.length<rh.BATCHABLE_SIZE*2}buildDrawCalls(){let i=++$._globalBatch;for(let c=0;c<this.drawCalls.length;c++)this.drawCalls[c].texArray.clear(),Si.push(this.drawCalls[c]);this.drawCalls.length=0;const t=this.colors,e=this.textureIds;let s=Si.pop();s||(s=new ds,s.texArray=new fs),s.texArray.count=0,s.start=0,s.size=0,s.type=Bt.TRIANGLES;let r=0,n=null,o=0,a=!1,h=Bt.TRIANGLES,l=0;this.drawCalls.push(s);for(let c=0;c<this.batches.length;c++){const u=this.batches[c],d=8,f=u.style,p=f.texture.baseTexture;a!==!!f.native&&(a=!!f.native,h=a?Bt.LINES:Bt.TRIANGLES,n=null,r=d,i++),n!==p&&(n=p,p._batchEnabled!==i&&(r===d&&(i++,r=0,s.size>0&&(s=Si.pop(),s||(s=new ds,s.texArray=new fs),this.drawCalls.push(s)),s.start=l,s.size=0,s.texArray.count=0,s.type=h),p.touched=1,p._batchEnabled=i,p._batchLocation=r,p.wrapMode=Kt.REPEAT,s.texArray.elements[s.texArray.count++]=p,r++)),s.size+=u.size,l+=u.size,o=p._batchLocation,this.addColors(t,f.color,f.alpha,u.attribSize,u.attribStart),this.addTextureIds(e,o,u.attribSize,u.attribStart)}$._globalBatch=i,this.packAttributes()}packAttributes(){const i=this.points,t=this.uvs,e=this.colors,s=this.textureIds,r=new ArrayBuffer(i.length*3*4),n=new Float32Array(r),o=new Uint32Array(r);let a=0;for(let h=0;h<i.length/2;h++)n[a++]=i[h*2],n[a++]=i[h*2+1],n[a++]=t[h*2],n[a++]=t[h*2+1],o[a++]=e[h],n[a++]=s[h];this._buffer.update(r),this._indexBuffer.update(this.indicesUint16)}processFill(i){i.holes.length?Sn.triangulate(i,this):Cs[i.type].triangulate(i,this)}processLine(i){An(i,this);for(let t=0;t<i.holes.length;t++)An(i.holes[t],this)}processHoles(i){for(let t=0;t<i.length;t++){const e=i[t];Cs[e.type].build(e),e.matrix&&this.transformPoints(e.points,e.matrix)}}calculateBounds(){const i=this._bounds;i.clear(),i.addVertexData(this.points,0,this.points.length),i.pad(this.boundsPadding,this.boundsPadding)}transformPoints(i,t){for(let e=0;e<i.length/2;e++){const s=i[e*2],r=i[e*2+1];i[e*2]=t.a*s+t.c*r+t.tx,i[e*2+1]=t.b*s+t.d*r+t.ty}}addColors(i,t,e,s,r=0){const n=(t>>16)+(t&65280)+((t&255)<<16),o=qi(n,e);i.length=Math.max(i.length,r+s);for(let a=0;a<s;a++)i[r+a]=o}addTextureIds(i,t,e,s=0){i.length=Math.max(i.length,s+e);for(let r=0;r<e;r++)i[s+r]=t}addUvs(i,t,e,s,r,n=null){let o=0;const a=t.length,h=e.frame;for(;o<r;){let c=i[(s+o)*2],u=i[(s+o)*2+1];if(n){const d=n.a*c+n.c*u+n.tx;u=n.b*c+n.d*u+n.ty,c=d}o++,t.push(c/h.width,u/h.height)}const l=e.baseTexture;(h.width<l.width||h.height<l.height)&&this.adjustUvs(t,e,a,r)}adjustUvs(i,t,e,s){const r=t.baseTexture,n=1e-6,o=e+s*2,a=t.frame,h=a.width/r.width,l=a.height/r.height;let c=a.x/a.width,u=a.y/a.height,d=Math.floor(i[e]+n),f=Math.floor(i[e+1]+n);for(let p=e+2;p<o;p+=2)d=Math.min(d,Math.floor(i[p]+n)),f=Math.min(f,Math.floor(i[p+1]+n));c-=d,u-=f;for(let p=e;p<o;p+=2)i[p]=(i[p]+c)*h,i[p+1]=(i[p+1]+u)*l}};let In=rh;In.BATCHABLE_SIZE=100;class Rs extends Ei{constructor(){super(...arguments),this.width=0,this.alignment=.5,this.native=!1,this.cap=ve.BUTT,this.join=fe.MITER,this.miterLimit=10}clone(){const t=new Rs;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t.width=this.width,t.alignment=this.alignment,t.native=this.native,t.cap=this.cap,t.join=this.join,t.miterLimit=this.miterLimit,t}reset(){super.reset(),this.color=0,this.alignment=.5,this.width=0,this.native=!1}}const Fp=new Float32Array(3),Pn={},Is=class extends wt{constructor(i=null){super(),this.shader=null,this.pluginName="batch",this.currentPath=null,this.batches=[],this.batchTint=-1,this.batchDirty=-1,this.vertexData=null,this._fillStyle=new Ei,this._lineStyle=new Rs,this._matrix=null,this._holeMode=!1,this.state=Jt.for2d(),this._geometry=i||new In,this._geometry.refCount++,this._transformID=-1,this.tint=16777215,this.blendMode=C.NORMAL}get geometry(){return this._geometry}clone(){return this.finishPoly(),new Is(this._geometry)}set blendMode(i){this.state.blendMode=i}get blendMode(){return this.state.blendMode}get tint(){return this._tint}set tint(i){this._tint=i}get fill(){return this._fillStyle}get line(){return this._lineStyle}lineStyle(i=null,t=0,e=1,s=.5,r=!1){return typeof i=="number"&&(i={width:i,color:t,alpha:e,alignment:s,native:r}),this.lineTextureStyle(i)}lineTextureStyle(i){i=Object.assign({width:0,texture:L.WHITE,color:i!=null&&i.texture?16777215:0,alpha:1,matrix:null,alignment:.5,native:!1,cap:ve.BUTT,join:fe.MITER,miterLimit:10},i),this.currentPath&&this.startPoly();const t=i.width>0&&i.alpha>0;return t?(i.matrix&&(i.matrix=i.matrix.clone(),i.matrix.invert()),Object.assign(this._lineStyle,{visible:t},i)):this._lineStyle.reset(),this}startPoly(){if(this.currentPath){const i=this.currentPath.points,t=this.currentPath.points.length;t>2&&(this.drawShape(this.currentPath),this.currentPath=new xe,this.currentPath.closeStroke=!1,this.currentPath.points.push(i[t-2],i[t-1]))}else this.currentPath=new xe,this.currentPath.closeStroke=!1}finishPoly(){this.currentPath&&(this.currentPath.points.length>2?(this.drawShape(this.currentPath),this.currentPath=null):this.currentPath.points.length=0)}moveTo(i,t){return this.startPoly(),this.currentPath.points[0]=i,this.currentPath.points[1]=t,this}lineTo(i,t){this.currentPath||this.moveTo(0,0);const e=this.currentPath.points,s=e[e.length-2],r=e[e.length-1];return(s!==i||r!==t)&&e.push(i,t),this}_initCurve(i=0,t=0){this.currentPath?this.currentPath.points.length===0&&(this.currentPath.points=[i,t]):this.moveTo(i,t)}quadraticCurveTo(i,t,e,s){this._initCurve();const r=this.currentPath.points;return r.length===0&&this.moveTo(0,0),As.curveTo(i,t,e,s,r),this}bezierCurveTo(i,t,e,s,r,n){return this._initCurve(),Ss.curveTo(i,t,e,s,r,n,this.currentPath.points),this}arcTo(i,t,e,s,r){this._initCurve(i,t);const n=this.currentPath.points,o=Cn.curveTo(i,t,e,s,r,n);if(o){const{cx:a,cy:h,radius:l,startAngle:c,endAngle:u,anticlockwise:d}=o;this.arc(a,h,l,c,u,d)}return this}arc(i,t,e,s,r,n=!1){if(s===r)return this;if(!n&&r<=s?r+=Ke:n&&s<=r&&(s+=Ke),r-s===0)return this;const a=i+Math.cos(s)*e,h=t+Math.sin(s)*e,l=this._geometry.closePointEps;let c=this.currentPath?this.currentPath.points:null;if(c){const u=Math.abs(c[c.length-2]-a),d=Math.abs(c[c.length-1]-h);u<l&&d<l||c.push(a,h)}else this.moveTo(a,h),c=this.currentPath.points;return Cn.arc(a,h,i,t,e,s,r,n,c),this}beginFill(i=0,t=1){return this.beginTextureFill({texture:L.WHITE,color:i,alpha:t})}beginTextureFill(i){i=Object.assign({texture:L.WHITE,color:16777215,alpha:1,matrix:null},i),this.currentPath&&this.startPoly();const t=i.alpha>0;return t?(i.matrix&&(i.matrix=i.matrix.clone(),i.matrix.invert()),Object.assign(this._fillStyle,{visible:t},i)):this._fillStyle.reset(),this}endFill(){return this.finishPoly(),this._fillStyle.reset(),this}drawRect(i,t,e,s){return this.drawShape(new j(i,t,e,s))}drawRoundedRect(i,t,e,s,r){return this.drawShape(new Hi(i,t,e,s,r))}drawCircle(i,t,e){return this.drawShape(new ki(i,t,e))}drawEllipse(i,t,e,s){return this.drawShape(new Gi(i,t,e,s))}drawPolygon(...i){let t,e=!0;const s=i[0];s.points?(e=s.closeStroke,t=s.points):Array.isArray(i[0])?t=i[0]:t=i;const r=new xe(t);return r.closeStroke=e,this.drawShape(r),this}drawShape(i){return this._holeMode?this._geometry.drawHole(i,this._matrix):this._geometry.drawShape(i,this._fillStyle.clone(),this._lineStyle.clone(),this._matrix),this}clear(){return this._geometry.clear(),this._lineStyle.reset(),this._fillStyle.reset(),this._boundsID++,this._matrix=null,this._holeMode=!1,this.currentPath=null,this}isFastRect(){const i=this._geometry.graphicsData;return i.length===1&&i[0].shape.type===et.RECT&&!i[0].matrix&&!i[0].holes.length&&!(i[0].lineStyle.visible&&i[0].lineStyle.width)}_render(i){this.finishPoly();const t=this._geometry;t.updateBatches(),t.batchable?(this.batchDirty!==t.batchDirty&&this._populateBatches(),this._renderBatched(i)):(i.batch.flush(),this._renderDirect(i))}_populateBatches(){const i=this._geometry,t=this.blendMode,e=i.batches.length;this.batchTint=-1,this._transformID=-1,this.batchDirty=i.batchDirty,this.batches.length=e,this.vertexData=new Float32Array(i.points);for(let s=0;s<e;s++){const r=i.batches[s],n=r.style.color,o=new Float32Array(this.vertexData.buffer,r.attribStart*4*2,r.attribSize*2),a=new Float32Array(i.uvsFloat32.buffer,r.attribStart*4*2,r.attribSize*2),h=new Uint16Array(i.indicesUint16.buffer,r.start*2,r.size),l={vertexData:o,blendMode:t,indices:h,uvs:a,_batchRGB:ae(n),_tintRGB:n,_texture:r.style.texture,alpha:r.style.alpha,worldAlpha:1};this.batches[s]=l}}_renderBatched(i){if(!!this.batches.length){i.batch.setObjectRenderer(i.plugins[this.pluginName]),this.calculateVertices(),this.calculateTints();for(let t=0,e=this.batches.length;t<e;t++){const s=this.batches[t];s.worldAlpha=this.worldAlpha*s.alpha,i.plugins[this.pluginName].render(s)}}}_renderDirect(i){const t=this._resolveDirectShader(i),e=this._geometry,s=this.tint,r=this.worldAlpha,n=t.uniforms,o=e.drawCalls;n.translationMatrix=this.transform.worldTransform,n.tint[0]=(s>>16&255)/255*r,n.tint[1]=(s>>8&255)/255*r,n.tint[2]=(s&255)/255*r,n.tint[3]=r,i.shader.bind(t),i.geometry.bind(e,t),i.state.set(this.state);for(let a=0,h=o.length;a<h;a++)this._renderDrawCallDirect(i,e.drawCalls[a])}_renderDrawCallDirect(i,t){const{texArray:e,type:s,size:r,start:n}=t,o=e.count;for(let a=0;a<o;a++)i.texture.bind(e.elements[a],a);i.geometry.draw(s,r,n)}_resolveDirectShader(i){let t=this.shader;const e=this.pluginName;if(!t){if(!Pn[e]){const{MAX_TEXTURES:s}=i.plugins[e],r=new Int32Array(s);for(let a=0;a<s;a++)r[a]=a;const n={tint:new Float32Array([1,1,1,1]),translationMatrix:new Z,default:Lt.from({uSamplers:r},!0)},o=i.plugins[e]._shader.program;Pn[e]=new Wt(o,n)}t=Pn[e]}return t}_calculateBounds(){this.finishPoly();const i=this._geometry;if(!i.graphicsData.length)return;const{minX:t,minY:e,maxX:s,maxY:r}=i.bounds;this._bounds.addFrame(this.transform,t,e,s,r)}containsPoint(i){return this.worldTransform.applyInverse(i,Is._TEMP_POINT),this._geometry.containsPoint(Is._TEMP_POINT)}calculateTints(){if(this.batchTint!==this.tint){this.batchTint=this.tint;const i=ae(this.tint,Fp);for(let t=0;t<this.batches.length;t++){const e=this.batches[t],s=e._batchRGB,r=i[0]*s[0]*255,n=i[1]*s[1]*255,o=i[2]*s[2]*255,a=(r<<16)+(n<<8)+(o|0);e._tintRGB=(a>>16)+(a&65280)+((a&255)<<16)}}}calculateVertices(){const i=this.transform._worldID;if(this._transformID===i)return;this._transformID=i;const t=this.transform.worldTransform,e=t.a,s=t.b,r=t.c,n=t.d,o=t.tx,a=t.ty,h=this._geometry.points,l=this.vertexData;let c=0;for(let u=0;u<h.length;u+=2){const d=h[u],f=h[u+1];l[c++]=e*d+r*f+o,l[c++]=n*f+s*d+a}}closePath(){const i=this.currentPath;return i&&(i.closeStroke=!0,this.finishPoly()),this}setMatrix(i){return this._matrix=i,this}beginHole(){return this.finishPoly(),this._holeMode=!0,this}endHole(){return this.finishPoly(),this._holeMode=!1,this}destroy(i){this._geometry.refCount--,this._geometry.refCount===0&&this._geometry.dispose(),this._matrix=null,this.currentPath=null,this._lineStyle.destroy(),this._lineStyle=null,this._fillStyle.destroy(),this._fillStyle=null,this._geometry=null,this.shader=null,this.vertexData=null,this.batches.length=0,this.batches=null,super.destroy(i)}};let Ne=Is;Ne._TEMP_POINT=new W;const Op={buildPoly:Sn,buildCircle:wi,buildRectangle:th,buildRoundedRectangle:eh,buildLine:An,ArcUtils:Cn,BezierUtils:Ss,QuadraticUtils:As,BatchPart:sh,FILL_COMMANDS:Cs,BATCH_POOL:Rn,DRAW_CALL_POOL:Si};class nh{constructor(t,e){this.uvBuffer=t,this.uvMatrix=e,this.data=null,this._bufferUpdateId=-1,this._textureUpdateId=-1,this._updateID=0}update(t){if(!t&&this._bufferUpdateId===this.uvBuffer._updateID&&this._textureUpdateId===this.uvMatrix._updateID)return;this._bufferUpdateId=this.uvBuffer._updateID,this._textureUpdateId=this.uvMatrix._updateID;const e=this.uvBuffer.data;(!this.data||this.data.length!==e.length)&&(this.data=new Float32Array(e.length)),this.uvMatrix.multiplyUvs(e,this.data),this._updateID++}}const Mn=new W,oh=new xe,ah=class extends wt{constructor(i,t,e,s=Bt.TRIANGLES){super(),this.geometry=i,this.shader=t,this.state=e||Jt.for2d(),this.drawMode=s,this.start=0,this.size=0,this.uvs=null,this.indices=null,this.vertexData=new Float32Array(1),this.vertexDirty=-1,this._transformID=-1,this._roundPixels=P.ROUND_PIXELS,this.batchUvs=null}get geometry(){return this._geometry}set geometry(i){this._geometry!==i&&(this._geometry&&(this._geometry.refCount--,this._geometry.refCount===0&&this._geometry.dispose()),this._geometry=i,this._geometry&&this._geometry.refCount++,this.vertexDirty=-1)}get uvBuffer(){return this.geometry.buffers[1]}get verticesBuffer(){return this.geometry.buffers[0]}set material(i){this.shader=i}get material(){return this.shader}set blendMode(i){this.state.blendMode=i}get blendMode(){return this.state.blendMode}set roundPixels(i){this._roundPixels!==i&&(this._transformID=-1),this._roundPixels=i}get roundPixels(){return this._roundPixels}get tint(){return"tint"in this.shader?this.shader.tint:null}set tint(i){this.shader.tint=i}get texture(){return"texture"in this.shader?this.shader.texture:null}set texture(i){this.shader.texture=i}_render(i){const t=this.geometry.buffers[0].data;this.shader.batchable&&this.drawMode===Bt.TRIANGLES&&t.length<ah.BATCHABLE_SIZE*2?this._renderToBatch(i):this._renderDefault(i)}_renderDefault(i){const t=this.shader;t.alpha=this.worldAlpha,t.update&&t.update(),i.batch.flush(),t.uniforms.translationMatrix=this.transform.worldTransform.toArray(!0),i.shader.bind(t),i.state.set(this.state),i.geometry.bind(this.geometry,t),i.geometry.draw(this.drawMode,this.size,this.start,this.geometry.instanceCount)}_renderToBatch(i){const t=this.geometry,e=this.shader;e.uvMatrix&&(e.uvMatrix.update(),this.calculateUvs()),this.calculateVertices(),this.indices=t.indexBuffer.data,this._tintRGB=e._tintRGB,this._texture=e.texture;const s=this.material.pluginName;i.batch.setObjectRenderer(i.plugins[s]),i.plugins[s].render(this)}calculateVertices(){const t=this.geometry.buffers[0],e=t.data,s=t._updateID;if(s===this.vertexDirty&&this._transformID===this.transform._worldID)return;this._transformID=this.transform._worldID,this.vertexData.length!==e.length&&(this.vertexData=new Float32Array(e.length));const r=this.transform.worldTransform,n=r.a,o=r.b,a=r.c,h=r.d,l=r.tx,c=r.ty,u=this.vertexData;for(let d=0;d<u.length/2;d++){const f=e[d*2],p=e[d*2+1];u[d*2]=n*f+a*p+l,u[d*2+1]=o*f+h*p+c}if(this._roundPixels){const d=P.RESOLUTION;for(let f=0;f<u.length;++f)u[f]=Math.round(u[f]*d)/d}this.vertexDirty=s}calculateUvs(){const i=this.geometry.buffers[1],t=this.shader;t.uvMatrix.isSimple?this.uvs=i.data:(this.batchUvs||(this.batchUvs=new nh(i,t.uvMatrix)),this.batchUvs.update(),this.uvs=this.batchUvs.data)}_calculateBounds(){this.calculateVertices(),this._bounds.addVertexData(this.vertexData,0,this.vertexData.length)}containsPoint(i){if(!this.getBounds().contains(i.x,i.y))return!1;this.worldTransform.applyInverse(i,Mn);const t=this.geometry.getBuffer("aVertexPosition").data,e=oh.points,s=this.geometry.getIndex().data,r=s.length,n=this.drawMode===4?3:1;for(let o=0;o+2<r;o+=n){const a=s[o]*2,h=s[o+1]*2,l=s[o+2]*2;if(e[0]=t[a],e[1]=t[a+1],e[2]=t[h],e[3]=t[h+1],e[4]=t[l],e[5]=t[l+1],oh.contains(Mn.x,Mn.y))return!0}return!1}destroy(i){super.destroy(i),this._cachedTexture&&(this._cachedTexture.destroy(),this._cachedTexture=null),this.geometry=null,this.shader=null,this.state=null,this.uvs=null,this.indices=null,this.vertexData=null}};let Mt=ah;Mt.BATCHABLE_SIZE=100;var Lp=`varying vec2 vTextureCoord;
uniform vec4 uColor;

uniform sampler2D uSampler;

void main(void)
{
    gl_FragColor = texture2D(uSampler, vTextureCoord) * uColor;
}
`,Up=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTextureMatrix;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;
}
`;class Fe extends Wt{constructor(t,e){const s={uSampler:t,alpha:1,uTextureMatrix:Z.IDENTITY,uColor:new Float32Array([1,1,1,1])};e=Object.assign({tint:16777215,alpha:1,pluginName:"batch"},e),e.uniforms&&Object.assign(s,e.uniforms),super(e.program||Qt.from(Up,Lp),s),this._colorDirty=!1,this.uvMatrix=new hs(t),this.batchable=e.program===void 0,this.pluginName=e.pluginName,this.tint=e.tint,this.alpha=e.alpha}get texture(){return this.uniforms.uSampler}set texture(t){this.uniforms.uSampler!==t&&(!this.uniforms.uSampler.baseTexture.alphaMode!=!t.baseTexture.alphaMode&&(this._colorDirty=!0),this.uniforms.uSampler=t,this.uvMatrix.texture=t)}set alpha(t){t!==this._alpha&&(this._alpha=t,this._colorDirty=!0)}get alpha(){return this._alpha}set tint(t){t!==this._tint&&(this._tint=t,this._tintRGB=(t>>16)+(t&65280)+((t&255)<<16),this._colorDirty=!0)}get tint(){return this._tint}update(){if(this._colorDirty){this._colorDirty=!1;const t=this.texture.baseTexture;ir(this._tint,this._alpha,this.uniforms.uColor,t.alphaMode)}this.uvMatrix.update()&&(this.uniforms.uTextureMatrix=this.uvMatrix.mapCoord)}}class Ci extends ce{constructor(t,e,s){super();const r=new dt(t),n=new dt(e,!0),o=new dt(s,!0,!0);this.addAttribute("aVertexPosition",r,2,!1,H.FLOAT).addAttribute("aTextureCoord",n,2,!1,H.FLOAT).addIndex(o),this._updateId=-1}get vertexDirtyId(){return this.buffers[0]._updateID}}class hh extends Ci{constructor(t=100,e=100,s=10,r=10){super(),this.segWidth=s,this.segHeight=r,this.width=t,this.height=e,this.build()}build(){const t=this.segWidth*this.segHeight,e=[],s=[],r=[],n=this.segWidth-1,o=this.segHeight-1,a=this.width/n,h=this.height/o;for(let c=0;c<t;c++){const u=c%this.segWidth,d=c/this.segWidth|0;e.push(u*a,d*h),s.push(u/n,d/o)}const l=n*o;for(let c=0;c<l;c++){const u=c%n,d=c/n|0,f=d*this.segWidth+u,p=d*this.segWidth+u+1,g=(d+1)*this.segWidth+u,m=(d+1)*this.segWidth+u+1;r.push(f,p,g,p,m,g)}this.buffers[0].data=new Float32Array(e),this.buffers[1].data=new Float32Array(s),this.indexBuffer.data=new Uint16Array(r),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}}class lh extends Ci{constructor(t=200,e,s=0){super(new Float32Array(e.length*4),new Float32Array(e.length*4),new Uint16Array((e.length-1)*6)),this.points=e,this._width=t,this.textureScale=s,this.build()}get width(){return this._width}build(){const t=this.points;if(!t)return;const e=this.getBuffer("aVertexPosition"),s=this.getBuffer("aTextureCoord"),r=this.getIndex();if(t.length<1)return;e.data.length/4!==t.length&&(e.data=new Float32Array(t.length*4),s.data=new Float32Array(t.length*4),r.data=new Uint16Array((t.length-1)*6));const n=s.data,o=r.data;n[0]=0,n[1]=0,n[2]=0,n[3]=1;let a=0,h=t[0];const l=this._width*this.textureScale,c=t.length;for(let d=0;d<c;d++){const f=d*4;if(this.textureScale>0){const p=h.x-t[d].x,g=h.y-t[d].y,m=Math.sqrt(p*p+g*g);h=t[d],a+=m/l}else a=d/(c-1);n[f]=a,n[f+1]=0,n[f+2]=a,n[f+3]=1}let u=0;for(let d=0;d<c-1;d++){const f=d*2;o[u++]=f,o[u++]=f+1,o[u++]=f+2,o[u++]=f+2,o[u++]=f+1,o[u++]=f+3}s.update(),r.update(),this.updateVertices()}updateVertices(){const t=this.points;if(t.length<1)return;let e=t[0],s,r=0,n=0;const o=this.buffers[0].data,a=t.length;for(let h=0;h<a;h++){const l=t[h],c=h*4;h<t.length-1?s=t[h+1]:s=l,n=-(s.x-e.x),r=s.y-e.y;let u=(1-h/(a-1))*10;u>1&&(u=1);const d=Math.sqrt(r*r+n*n),f=this.textureScale>0?this.textureScale*this._width/2:this._width/2;r/=d,n/=d,r*=f,n*=f,o[c]=l.x+r,o[c+1]=l.y+n,o[c+2]=l.x-r,o[c+3]=l.y-n,e=l}this.buffers[0].update()}update(){this.textureScale>0?this.build():this.updateVertices()}}class ch extends Mt{constructor(t,e,s=0){const r=new lh(t.height,e,s),n=new Fe(t);s>0&&(t.baseTexture.wrapMode=Kt.REPEAT),super(r,n),this.autoUpdate=!0}_render(t){const e=this.geometry;(this.autoUpdate||e._width!==this.shader.texture.height)&&(e._width=this.shader.texture.height,e.update()),super._render(t)}}class uh extends Mt{constructor(t,e,s){const r=new hh(t.width,t.height,e,s),n=new Fe(L.WHITE);super(r,n),this.texture=t,this.autoResize=!0}textureUpdated(){this._textureID=this.shader.texture._updateID;const t=this.geometry,{width:e,height:s}=this.shader.texture;this.autoResize&&(t.width!==e||t.height!==s)&&(t.width=this.shader.texture.width,t.height=this.shader.texture.height,t.build())}set texture(t){this.shader.texture!==t&&(this.shader.texture=t,this._textureID=-1,t.baseTexture.valid?this.textureUpdated():t.once("update",this.textureUpdated,this))}get texture(){return this.shader.texture}_render(t){this._textureID!==this.shader.texture._updateID&&this.textureUpdated(),super._render(t)}destroy(t){this.shader.texture.off("update",this.textureUpdated,this),super.destroy(t)}}class dh extends Mt{constructor(t=L.EMPTY,e,s,r,n){const o=new Ci(e,s,r);o.getBuffer("aVertexPosition").static=!1;const a=new Fe(t);super(o,a,null,n),this.autoUpdate=!0}get vertices(){return this.geometry.getBuffer("aVertexPosition").data}set vertices(t){this.geometry.getBuffer("aVertexPosition").data=t}_render(t){this.autoUpdate&&this.geometry.getBuffer("aVertexPosition").update(),super._render(t)}}const Ps=10;class Ri extends uh{constructor(t,e=Ps,s=Ps,r=Ps,n=Ps){super(L.WHITE,4,4),this._origWidth=t.orig.width,this._origHeight=t.orig.height,this._width=this._origWidth,this._height=this._origHeight,this._leftWidth=e,this._rightWidth=r,this._topHeight=s,this._bottomHeight=n,this.texture=t}textureUpdated(){this._textureID=this.shader.texture._updateID,this._refresh()}get vertices(){return this.geometry.getBuffer("aVertexPosition").data}set vertices(t){this.geometry.getBuffer("aVertexPosition").data=t}updateHorizontalVertices(){const t=this.vertices,e=this._getMinScale();t[9]=t[11]=t[13]=t[15]=this._topHeight*e,t[17]=t[19]=t[21]=t[23]=this._height-this._bottomHeight*e,t[25]=t[27]=t[29]=t[31]=this._height}updateVerticalVertices(){const t=this.vertices,e=this._getMinScale();t[2]=t[10]=t[18]=t[26]=this._leftWidth*e,t[4]=t[12]=t[20]=t[28]=this._width-this._rightWidth*e,t[6]=t[14]=t[22]=t[30]=this._width}_getMinScale(){const t=this._leftWidth+this._rightWidth,e=this._width>t?1:this._width/t,s=this._topHeight+this._bottomHeight,r=this._height>s?1:this._height/s;return Math.min(e,r)}get width(){return this._width}set width(t){this._width=t,this._refresh()}get height(){return this._height}set height(t){this._height=t,this._refresh()}get leftWidth(){return this._leftWidth}set leftWidth(t){this._leftWidth=t,this._refresh()}get rightWidth(){return this._rightWidth}set rightWidth(t){this._rightWidth=t,this._refresh()}get topHeight(){return this._topHeight}set topHeight(t){this._topHeight=t,this._refresh()}get bottomHeight(){return this._bottomHeight}set bottomHeight(t){this._bottomHeight=t,this._refresh()}_refresh(){const t=this.texture,e=this.geometry.buffers[1].data;this._origWidth=t.orig.width,this._origHeight=t.orig.height;const s=1/this._origWidth,r=1/this._origHeight;e[0]=e[8]=e[16]=e[24]=0,e[1]=e[3]=e[5]=e[7]=0,e[6]=e[14]=e[22]=e[30]=1,e[25]=e[27]=e[29]=e[31]=1,e[2]=e[10]=e[18]=e[26]=s*this._leftWidth,e[4]=e[12]=e[20]=e[28]=1-s*this._rightWidth,e[9]=e[11]=e[13]=e[15]=r*this._topHeight,e[17]=e[19]=e[21]=e[23]=1-r*this._bottomHeight,this.updateHorizontalVertices(),this.updateVerticalVertices(),this.geometry.buffers[0].update(),this.geometry.buffers[1].update()}}P.UPLOADS_PER_FRAME=4;class fh{constructor(t){this.maxItemsPerFrame=t,this.itemsLeft=0}beginFrame(){this.itemsLeft=this.maxItemsPerFrame}allowedToUpload(){return this.itemsLeft-- >0}}function kp(i,t){var s;let e=!1;if((s=i==null?void 0:i._textures)!=null&&s.length){for(let r=0;r<i._textures.length;r++)if(i._textures[r]instanceof L){const n=i._textures[r].baseTexture;t.includes(n)||(t.push(n),e=!0)}}return e}function Gp(i,t){if(i.baseTexture instanceof $){const e=i.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function Hp(i,t){if(i._texture&&i._texture instanceof L){const e=i._texture.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function Xp(i,t){return t instanceof gi?(t.updateText(!0),!0):!1}function Vp(i,t){if(t instanceof ue){const e=t.toFontString();return G.measureFont(e),!0}return!1}function zp(i,t){if(i instanceof gi){t.includes(i.style)||t.push(i.style),t.includes(i)||t.push(i);const e=i._texture.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function Wp(i,t){return i instanceof ue?(t.includes(i)||t.push(i),!0):!1}class Bn{constructor(t){this.limiter=new fh(P.UPLOADS_PER_FRAME),this.renderer=t,this.uploadHookHelper=null,this.queue=[],this.addHooks=[],this.uploadHooks=[],this.completes=[],this.ticking=!1,this.delayedTick=()=>{!this.queue||this.prepareItems()},this.registerFindHook(zp),this.registerFindHook(Wp),this.registerFindHook(kp),this.registerFindHook(Gp),this.registerFindHook(Hp),this.registerUploadHook(Xp),this.registerUploadHook(Vp)}upload(t){return new Promise(e=>{t&&this.add(t),this.queue.length?(this.completes.push(e),this.ticking||(this.ticking=!0,ot.system.addOnce(this.tick,this,pe.UTILITY))):e()})}tick(){setTimeout(this.delayedTick,0)}prepareItems(){for(this.limiter.beginFrame();this.queue.length&&this.limiter.allowedToUpload();){const t=this.queue[0];let e=!1;if(t&&!t._destroyed){for(let s=0,r=this.uploadHooks.length;s<r;s++)if(this.uploadHooks[s](this.uploadHookHelper,t)){this.queue.shift(),e=!0;break}}e||this.queue.shift()}if(this.queue.length)ot.system.addOnce(this.tick,this,pe.UTILITY);else{this.ticking=!1;const t=this.completes.slice(0);this.completes.length=0;for(let e=0,s=t.length;e<s;e++)t[e]()}}registerFindHook(t){return t&&this.addHooks.push(t),this}registerUploadHook(t){return t&&this.uploadHooks.push(t),this}add(t){for(let e=0,s=this.addHooks.length;e<s&&!this.addHooks[e](t,this.queue);e++);if(t instanceof wt)for(let e=t.children.length-1;e>=0;e--)this.add(t.children[e]);return this}destroy(){this.ticking&&ot.system.remove(this.tick,this),this.ticking=!1,this.addHooks=null,this.uploadHooks=null,this.renderer=null,this.completes=null,this.queue=null,this.limiter=null,this.uploadHookHelper=null}}function ph(i,t){return t instanceof $?(t._glTextures[i.CONTEXT_UID]||i.texture.bind(t),!0):!1}function jp(i,t){if(!(t instanceof Ne))return!1;const{geometry:e}=t;t.finishPoly(),e.updateBatches();const{batches:s}=e;for(let r=0;r<s.length;r++){const{texture:n}=s[r].style;n&&ph(i,n.baseTexture)}return e.batchable||i.geometry.bind(e,t._resolveDirectShader(i)),!0}function Yp(i,t){return i instanceof Ne?(t.push(i),!0):!1}class Dn extends Bn{constructor(t){super(t),this.uploadHookHelper=this.renderer,this.registerFindHook(Yp),this.registerUploadHook(ph),this.registerUploadHook(jp)}}Dn.extension={name:"prepare",type:F.RendererSystem},U.add(Dn);class $p{constructor(t){this.maxMilliseconds=t,this.frameStart=0}beginFrame(){this.frameStart=Date.now()}allowedToUpload(){return Date.now()-this.frameStart<this.maxMilliseconds}}const Ii=class{constructor(i,t,e=null){this.linkedSheets=[],this._texture=i instanceof L?i:null,this.baseTexture=i instanceof $?i:this._texture.baseTexture,this.textures={},this.animations={},this.data=t;const s=this.baseTexture.resource;this.resolution=this._updateResolution(e||(s?s.url:null)),this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}_updateResolution(i=null){const{scale:t}=this.data.meta;let e=he(i,null);return e===null&&(e=parseFloat(t!=null?t:"1")),e!==1&&this.baseTexture.setResolution(e),e}parse(){return new Promise(i=>{this._callback=i,this._batchIndex=0,this._frameKeys.length<=Ii.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(i){let t=i;const e=Ii.BATCH_SIZE;for(;t-i<e&&t<this._frameKeys.length;){const s=this._frameKeys[t],r=this._frames[s],n=r.frame;if(n){let o=null,a=null;const h=r.trimmed!==!1&&r.sourceSize?r.sourceSize:r.frame,l=new j(0,0,Math.floor(h.w)/this.resolution,Math.floor(h.h)/this.resolution);r.rotated?o=new j(Math.floor(n.x)/this.resolution,Math.floor(n.y)/this.resolution,Math.floor(n.h)/this.resolution,Math.floor(n.w)/this.resolution):o=new j(Math.floor(n.x)/this.resolution,Math.floor(n.y)/this.resolution,Math.floor(n.w)/this.resolution,Math.floor(n.h)/this.resolution),r.trimmed!==!1&&r.spriteSourceSize&&(a=new j(Math.floor(r.spriteSourceSize.x)/this.resolution,Math.floor(r.spriteSourceSize.y)/this.resolution,Math.floor(n.w)/this.resolution,Math.floor(n.h)/this.resolution)),this.textures[s]=new L(this.baseTexture,o,l,a,r.rotated?2:0,r.anchor),L.addToCache(this.textures[s],s)}t++}}_processAnimations(){const i=this.data.animations||{};for(const t in i){this.animations[t]=[];for(let e=0;e<i[t].length;e++){const s=i[t][e];this.animations[t].push(this.textures[s])}}}_parseComplete(){const i=this._callback;this._callback=null,this._batchIndex=0,i.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*Ii.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*Ii.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(i=!1){var t;for(const e in this.textures)this.textures[e].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,i&&((t=this._texture)==null||t.destroy(),this.baseTexture.destroy()),this._texture=null,this.baseTexture=null,this.linkedSheets=[]}};let Ms=Ii;Ms.BATCH_SIZE=1e3;const qp=["jpg","png","jpeg","avif","webp"];function mh(i,t,e){const s={};if(i.forEach(r=>{s[r]=t}),Object.keys(t.textures).forEach(r=>{s[r]=t.textures[r]}),!e){const r=yt.dirname(i[0]);t.linkedSheets.forEach((n,o)=>{const a=mh([`${r}/${t.data.meta.related_multi_packs[o]}`],n,!0);Object.assign(s,a)})}return s}const gh={extension:F.Asset,cache:{test:i=>i instanceof Ms,getCacheableAssets:(i,t)=>mh(i,t,!1)},resolver:{test:i=>{const e=i.split("?")[0].split("."),s=e.pop(),r=e.pop();return s==="json"&&qp.includes(r)},parse:i=>{var e,s;const t=i.split(".");return{resolution:parseFloat((s=(e=P.RETINA_PREFIX.exec(i))==null?void 0:e[1])!=null?s:"1"),format:t[t.length-2],src:i}}},loader:{extension:{type:F.LoadParser,priority:Yt.Normal},async testParse(i,t){return yt.extname(t.src).includes(".json")&&!!i.frames},async parse(i,t,e){var l,c;let s=yt.dirname(t.src);s&&s.lastIndexOf("/")!==s.length-1&&(s+="/");const r=s+i.meta.image,o=(await e.load([r]))[r],a=new Ms(o.baseTexture,i,t.src);await a.parse();const h=(l=i==null?void 0:i.meta)==null?void 0:l.related_multi_packs;if(Array.isArray(h)){const u=[];for(const f of h){if(typeof f!="string")continue;const p=s+f;(c=t.data)!=null&&c.ignoreMultiPack||u.push(e.load({src:p,data:{ignoreMultiPack:!0}}))}const d=await Promise.all(u);a.linkedSheets=d,d.forEach(f=>{f.linkedSheets=[a].concat(a.linkedSheets.filter(p=>p!==f))})}return a},unload(i){i.destroy(!0)}}};U.add(gh);class Bs extends jt{constructor(t,e=!0){super(t[0]instanceof L?t[0]:t[0].texture),this._textures=null,this._durations=null,this._autoUpdate=e,this._isConnectedToTicker=!1,this.animationSpeed=1,this.loop=!0,this.updateAnchor=!1,this.onComplete=null,this.onFrameChange=null,this.onLoop=null,this._currentTime=0,this._playing=!1,this._previousFrame=null,this.textures=t}stop(){!this._playing||(this._playing=!1,this._autoUpdate&&this._isConnectedToTicker&&(ot.shared.remove(this.update,this),this._isConnectedToTicker=!1))}play(){this._playing||(this._playing=!0,this._autoUpdate&&!this._isConnectedToTicker&&(ot.shared.add(this.update,this,pe.HIGH),this._isConnectedToTicker=!0))}gotoAndStop(t){this.stop(),this.currentFrame=t}gotoAndPlay(t){this.currentFrame=t,this.play()}update(t){if(!this._playing)return;const e=this.animationSpeed*t,s=this.currentFrame;if(this._durations!==null){let r=this._currentTime%1*this._durations[this.currentFrame];for(r+=e/60*1e3;r<0;)this._currentTime--,r+=this._durations[this.currentFrame];const n=Math.sign(this.animationSpeed*t);for(this._currentTime=Math.floor(this._currentTime);r>=this._durations[this.currentFrame];)r-=this._durations[this.currentFrame]*n,this._currentTime+=n;this._currentTime+=r/this._durations[this.currentFrame]}else this._currentTime+=e;this._currentTime<0&&!this.loop?(this.gotoAndStop(0),this.onComplete&&this.onComplete()):this._currentTime>=this._textures.length&&!this.loop?(this.gotoAndStop(this._textures.length-1),this.onComplete&&this.onComplete()):s!==this.currentFrame&&(this.loop&&this.onLoop&&(this.animationSpeed>0&&this.currentFrame<s?this.onLoop():this.animationSpeed<0&&this.currentFrame>s&&this.onLoop()),this.updateTexture())}updateTexture(){const t=this.currentFrame;this._previousFrame!==t&&(this._previousFrame=t,this._texture=this._textures[t],this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this.uvs=this._texture._uvs.uvsFloat32,this.updateAnchor&&this._anchor.copyFrom(this._texture.defaultAnchor),this.onFrameChange&&this.onFrameChange(this.currentFrame))}destroy(t){this.stop(),super.destroy(t),this.onComplete=null,this.onFrameChange=null,this.onLoop=null}static fromFrames(t){const e=[];for(let s=0;s<t.length;++s)e.push(L.from(t[s]));return new Bs(e)}static fromImages(t){const e=[];for(let s=0;s<t.length;++s)e.push(L.from(t[s]));return new Bs(e)}get totalFrames(){return this._textures.length}get textures(){return this._textures}set textures(t){if(t[0]instanceof L)this._textures=t,this._durations=null;else{this._textures=[],this._durations=[];for(let e=0;e<t.length;e++)this._textures.push(t[e].texture),this._durations.push(t[e].time)}this._previousFrame=null,this.gotoAndStop(0),this.updateTexture()}get currentFrame(){let t=Math.floor(this._currentTime)%this._textures.length;return t<0&&(t+=this._textures.length),t}set currentFrame(t){if(t<0||t>this.totalFrames-1)throw new Error(`[AnimatedSprite]: Invalid frame index value ${t}, expected to be between 0 and totalFrames ${this.totalFrames}.`);const e=this.currentFrame;this._currentTime=t,e!==this.currentFrame&&this.updateTexture()}get playing(){return this._playing}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){t!==this._autoUpdate&&(this._autoUpdate=t,!this._autoUpdate&&this._isConnectedToTicker?(ot.shared.remove(this.update,this),this._isConnectedToTicker=!1):this._autoUpdate&&!this._isConnectedToTicker&&this._playing&&(ot.shared.add(this.update,this),this._isConnectedToTicker=!0))}}class Pi{constructor(){this.info=[],this.common=[],this.page=[],this.char=[],this.kerning=[],this.distanceField=[]}}class Mi{static test(t){return typeof t=="string"&&t.startsWith("info face=")}static parse(t){const e=t.match(/^[a-z]+\s+.+$/gm),s={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(const n in e){const o=e[n].match(/^[a-z]+/gm)[0],a=e[n].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),h={};for(const l in a){const c=a[l].split("="),u=c[0],d=c[1].replace(/"/gm,""),f=parseFloat(d),p=isNaN(f)?d:f;h[u]=p}s[o].push(h)}const r=new Pi;return s.info.forEach(n=>r.info.push({face:n.face,size:parseInt(n.size,10)})),s.common.forEach(n=>r.common.push({lineHeight:parseInt(n.lineHeight,10)})),s.page.forEach(n=>r.page.push({id:parseInt(n.id,10),file:n.file})),s.char.forEach(n=>r.char.push({id:parseInt(n.id,10),page:parseInt(n.page,10),x:parseInt(n.x,10),y:parseInt(n.y,10),width:parseInt(n.width,10),height:parseInt(n.height,10),xoffset:parseInt(n.xoffset,10),yoffset:parseInt(n.yoffset,10),xadvance:parseInt(n.xadvance,10)})),s.kerning.forEach(n=>r.kerning.push({first:parseInt(n.first,10),second:parseInt(n.second,10),amount:parseInt(n.amount,10)})),s.distanceField.forEach(n=>r.distanceField.push({distanceRange:parseInt(n.distanceRange,10),fieldType:n.fieldType})),r}}class Ds{static test(t){const e=t;return"getElementsByTagName"in e&&e.getElementsByTagName("page").length&&e.getElementsByTagName("info")[0].getAttribute("face")!==null}static parse(t){const e=new Pi,s=t.getElementsByTagName("info"),r=t.getElementsByTagName("common"),n=t.getElementsByTagName("page"),o=t.getElementsByTagName("char"),a=t.getElementsByTagName("kerning"),h=t.getElementsByTagName("distanceField");for(let l=0;l<s.length;l++)e.info.push({face:s[l].getAttribute("face"),size:parseInt(s[l].getAttribute("size"),10)});for(let l=0;l<r.length;l++)e.common.push({lineHeight:parseInt(r[l].getAttribute("lineHeight"),10)});for(let l=0;l<n.length;l++)e.page.push({id:parseInt(n[l].getAttribute("id"),10)||0,file:n[l].getAttribute("file")});for(let l=0;l<o.length;l++){const c=o[l];e.char.push({id:parseInt(c.getAttribute("id"),10),page:parseInt(c.getAttribute("page"),10)||0,x:parseInt(c.getAttribute("x"),10),y:parseInt(c.getAttribute("y"),10),width:parseInt(c.getAttribute("width"),10),height:parseInt(c.getAttribute("height"),10),xoffset:parseInt(c.getAttribute("xoffset"),10),yoffset:parseInt(c.getAttribute("yoffset"),10),xadvance:parseInt(c.getAttribute("xadvance"),10)})}for(let l=0;l<a.length;l++)e.kerning.push({first:parseInt(a[l].getAttribute("first"),10),second:parseInt(a[l].getAttribute("second"),10),amount:parseInt(a[l].getAttribute("amount"),10)});for(let l=0;l<h.length;l++)e.distanceField.push({fieldType:h[l].getAttribute("fieldType"),distanceRange:parseInt(h[l].getAttribute("distanceRange"),10)});return e}}class Ns{static test(t){return typeof t=="string"&&t.includes("<font>")?Ds.test(P.ADAPTER.parseXML(t)):!1}static parse(t){return Ds.parse(P.ADAPTER.parseXML(t))}}const Nn=[Mi,Ds,Ns];function _h(i){for(let t=0;t<Nn.length;t++)if(Nn[t].test(i))return Nn[t];return null}function Kp(i,t,e,s,r,n){const o=e.fill;if(Array.isArray(o)){if(o.length===1)return o[0]}else return o;let a;const h=e.dropShadow?e.dropShadowDistance:0,l=e.padding||0,c=i.width/s-h-l*2,u=i.height/s-h-l*2,d=o.slice(),f=e.fillGradientStops.slice();if(!f.length){const p=d.length+1;for(let g=1;g<p;++g)f.push(g/p)}if(d.unshift(o[0]),f.unshift(0),d.push(o[o.length-1]),f.push(1),e.fillGradientType===mi.LINEAR_VERTICAL){a=t.createLinearGradient(c/2,l,c/2,u+l);let p=0;const m=(n.fontProperties.fontSize+e.strokeThickness)/u;for(let x=0;x<r.length;x++){const T=n.lineHeight*x;for(let _=0;_<d.length;_++){let y=0;typeof f[_]=="number"?y=f[_]:y=_/d.length;const E=T/u+y*m;let B=Math.max(p,E);B=Math.min(B,1),a.addColorStop(B,d[_]),p=B}}}else{a=t.createLinearGradient(l,u/2,c+l,u/2);const p=d.length+1;let g=1;for(let m=0;m<d.length;m++){let x;typeof f[m]=="number"?x=f[m]:x=g/p,a.addColorStop(x,d[m]),g++}}return a}function Zp(i,t,e,s,r,n,o){const a=e.text,h=e.fontProperties;t.translate(s,r),t.scale(n,n);const l=o.strokeThickness/2,c=-(o.strokeThickness/2);if(t.font=o.toFontString(),t.lineWidth=o.strokeThickness,t.textBaseline=o.textBaseline,t.lineJoin=o.lineJoin,t.miterLimit=o.miterLimit,t.fillStyle=Kp(i,t,o,n,[a],e),t.strokeStyle=o.stroke,o.dropShadow){const u=o.dropShadowColor,d=ae(typeof u=="number"?u:$i(u)),f=o.dropShadowBlur*n,p=o.dropShadowDistance*n;t.shadowColor=`rgba(${d[0]*255},${d[1]*255},${d[2]*255},${o.dropShadowAlpha})`,t.shadowBlur=f,t.shadowOffsetX=Math.cos(o.dropShadowAngle)*p,t.shadowOffsetY=Math.sin(o.dropShadowAngle)*p}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;o.stroke&&o.strokeThickness&&t.strokeText(a,l,c+e.lineHeight-h.descent),o.fill&&t.fillText(a,l,c+e.lineHeight-h.descent),t.setTransform(1,0,0,1,0,0),t.fillStyle="rgba(0, 0, 0, 0)"}function vh(i){return Array.from?Array.from(i):i.split("")}function Qp(i){typeof i=="string"&&(i=[i]);const t=[];for(let e=0,s=i.length;e<s;e++){const r=i[e];if(Array.isArray(r)){if(r.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${r.length}.`);const n=r[0].charCodeAt(0),o=r[1].charCodeAt(0);if(o<n)throw new Error("[BitmapFont]: Invalid character range.");for(let a=n,h=o;a<=h;a++)t.push(String.fromCharCode(a))}else t.push(...vh(r))}if(t.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}function Fs(i){return i.codePointAt?i.codePointAt(0):i.charCodeAt(0)}const qt=class{constructor(i,t,e){var l,c;const[s]=i.info,[r]=i.common,[n]=i.page,[o]=i.distanceField,a=he(n.file),h={};this._ownsTextures=e,this.font=s.face,this.size=s.size,this.lineHeight=r.lineHeight/a,this.chars={},this.pageTextures=h;for(let u=0;u<i.page.length;u++){const{id:d,file:f}=i.page[u];h[d]=t instanceof Array?t[u]:t[f],(o==null?void 0:o.fieldType)&&o.fieldType!=="none"&&(h[d].baseTexture.alphaMode=Nt.NO_PREMULTIPLIED_ALPHA,h[d].baseTexture.mipmap=Gt.OFF)}for(let u=0;u<i.char.length;u++){const{id:d,page:f}=i.char[u];let{x:p,y:g,width:m,height:x,xoffset:T,yoffset:_,xadvance:y}=i.char[u];p/=a,g/=a,m/=a,x/=a,T/=a,_/=a,y/=a;const E=new j(p+h[f].frame.x/a,g+h[f].frame.y/a,m,x);this.chars[d]={xOffset:T,yOffset:_,xAdvance:y,kerning:{},texture:new L(h[f].baseTexture,E),page:f}}for(let u=0;u<i.kerning.length;u++){let{first:d,second:f,amount:p}=i.kerning[u];d/=a,f/=a,p/=a,this.chars[f]&&(this.chars[f].kerning[d]=p)}this.distanceFieldRange=o==null?void 0:o.distanceRange,this.distanceFieldType=(c=(l=o==null?void 0:o.fieldType)==null?void 0:l.toLowerCase())!=null?c:"none"}destroy(){for(const i in this.chars)this.chars[i].texture.destroy(),this.chars[i].texture=null;for(const i in this.pageTextures)this._ownsTextures&&this.pageTextures[i].destroy(!0),this.pageTextures[i]=null;this.chars=null,this.pageTextures=null}static install(i,t,e){let s;if(i instanceof Pi)s=i;else{const n=_h(i);if(!n)throw new Error("Unrecognized data format for font.");s=n.parse(i)}t instanceof L&&(t=[t]);const r=new qt(s,t,e);return qt.available[r.font]=r,r}static uninstall(i){const t=qt.available[i];if(!t)throw new Error(`No font found named '${i}'`);t.destroy(),delete qt.available[i]}static from(i,t,e){if(!i)throw new Error("[BitmapFont] Property `name` is required.");const B=Object.assign({},qt.defaultOptions,e),{chars:s,padding:r,resolution:n,textureWidth:o,textureHeight:a}=B,h=Xn(B,["chars","padding","resolution","textureWidth","textureHeight"]),l=Qp(s),c=t instanceof ue?t:new ue(t),u=o,d=new Pi;d.info[0]={face:c.fontFamily,size:c.fontSize},d.common[0]={lineHeight:c.fontSize};let f=0,p=0,g,m,x,T=0;const _=[],y=[];for(let A=0;A<l.length;A++){g||(g=P.ADAPTER.createCanvas(),g.width=o,g.height=a,m=g.getContext("2d"),x=new $(g,re({resolution:n},h)),_.push(x),y.push(new L(x)),d.page.push({id:y.length-1,file:""}));const b=l[A],I=G.measureText(b,c,!1,g),O=I.width,w=Math.ceil(I.height),D=Math.ceil((c.fontStyle==="italic"?2:1)*O);if(p>=a-w*n){if(p===0)throw new Error(`[BitmapFont] textureHeight ${a}px is too small (fontFamily: '${c.fontFamily}', fontSize: ${c.fontSize}px, char: '${b}')`);--A,g=null,m=null,x=null,p=0,f=0,T=0;continue}if(T=Math.max(w+I.fontProperties.descent,T),D*n+f>=u){if(f===0)throw new Error(`[BitmapFont] textureWidth ${o}px is too small (fontFamily: '${c.fontFamily}', fontSize: ${c.fontSize}px, char: '${b}')`);--A,p+=T*n,p=Math.ceil(p),f=0,T=0;continue}Zp(g,m,I,f,p,n,c);const R=Fs(I.text);d.char.push({id:R,page:y.length-1,x:f/n,y:p/n,width:D,height:w,xoffset:0,yoffset:0,xadvance:Math.ceil(O-(c.dropShadow?c.dropShadowDistance:0)-(c.stroke?c.strokeThickness:0))}),f+=(D+2*r)*n,f=Math.ceil(f)}for(let A=0,b=l.length;A<b;A++){const I=l[A];for(let O=0;O<b;O++){const w=l[O],D=m.measureText(I).width,R=m.measureText(w).width,X=m.measureText(I+w).width-(D+R);X&&d.kerning.push({first:Fs(I),second:Fs(w),amount:X})}}const E=new qt(d,y,!0);return qt.available[i]!==void 0&&qt.uninstall(i),qt.available[i]=E,E}};let St=qt;St.ALPHA=[["a","z"],["A","Z"]," "],St.NUMERIC=[["0","9"]],St.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],St.ASCII=[[" ","~"]],St.defaultOptions={resolution:1,textureWidth:512,textureHeight:512,padding:4,chars:qt.ALPHANUMERIC},St.available={};var Jp=`// Pixi texture info\r
varying vec2 vTextureCoord;\r
uniform sampler2D uSampler;\r
\r
// Tint\r
uniform vec4 uColor;\r
\r
// on 2D applications fwidth is screenScale / glyphAtlasScale * distanceFieldRange\r
uniform float uFWidth;\r
\r
void main(void) {\r
\r
  // To stack MSDF and SDF we need a non-pre-multiplied-alpha texture.\r
  vec4 texColor = texture2D(uSampler, vTextureCoord);\r
\r
  // MSDF\r
  float median = texColor.r + texColor.g + texColor.b -\r
                  min(texColor.r, min(texColor.g, texColor.b)) -\r
                  max(texColor.r, max(texColor.g, texColor.b));\r
  // SDF\r
  median = min(median, texColor.a);\r
\r
  float screenPxDistance = uFWidth * (median - 0.5);\r
  float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\r
  if (median < 0.01) {\r
    alpha = 0.0;\r
  } else if (median > 0.99) {\r
    alpha = 1.0;\r
  }\r
\r
  // NPM Textures, NPM outputs\r
  gl_FragColor = vec4(uColor.rgb, uColor.a * alpha);\r
\r
}\r
`,tm=`// Mesh material default fragment\r
attribute vec2 aVertexPosition;\r
attribute vec2 aTextureCoord;\r
\r
uniform mat3 projectionMatrix;\r
uniform mat3 translationMatrix;\r
uniform mat3 uTextureMatrix;\r
\r
varying vec2 vTextureCoord;\r
\r
void main(void)\r
{\r
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\r
\r
    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\r
}\r
`;const yh=[],xh=[],Th=[],bh=class extends wt{constructor(i,t={}){super(),this._tint=16777215;const{align:e,tint:s,maxWidth:r,letterSpacing:n,fontName:o,fontSize:a}=Object.assign({},bh.styleDefaults,t);if(!St.available[o])throw new Error(`Missing BitmapFont "${o}"`);this._activePagesMeshData=[],this._textWidth=0,this._textHeight=0,this._align=e,this._tint=s,this._font=void 0,this._fontName=o,this._fontSize=a,this.text=i,this._maxWidth=r,this._maxLineHeight=0,this._letterSpacing=n,this._anchor=new oe(()=>{this.dirty=!0},this,0,0),this._roundPixels=P.ROUND_PIXELS,this.dirty=!0,this._resolution=P.RESOLUTION,this._autoResolution=!0,this._textureCache={}}updateText(){var O;const i=St.available[this._fontName],t=this.fontSize,e=t/i.size,s=new W,r=[],n=[],o=[],a=this._text.replace(/(?:\r\n|\r)/g,`
`)||" ",h=vh(a),l=this._maxWidth*i.size/t,c=i.distanceFieldType==="none"?yh:xh;let u=null,d=0,f=0,p=0,g=-1,m=0,x=0,T=0,_=0;for(let w=0;w<h.length;w++){const D=h[w],R=Fs(D);if(/(?:\s)/.test(D)&&(g=w,m=d,_++),D==="\r"||D===`
`){n.push(d),o.push(-1),f=Math.max(f,d),++p,++x,s.x=0,s.y+=i.lineHeight,u=null,_=0;continue}const S=i.chars[R];if(!S)continue;u&&S.kerning[u]&&(s.x+=S.kerning[u]);const X=Th.pop()||{texture:L.EMPTY,line:0,charCode:0,prevSpaces:0,position:new W};X.texture=S.texture,X.line=p,X.charCode=R,X.position.x=s.x+S.xOffset+this._letterSpacing/2,X.position.y=s.y+S.yOffset,X.prevSpaces=_,r.push(X),d=X.position.x+Math.max(S.xAdvance-S.xOffset,S.texture.orig.width),s.x+=S.xAdvance+this._letterSpacing,T=Math.max(T,S.yOffset+S.texture.height),u=R,g!==-1&&l>0&&s.x>l&&(++x,Ae(r,1+g-x,1+w-g),w=g,g=-1,n.push(m),o.push(r.length>0?r[r.length-1].prevSpaces:0),f=Math.max(f,m),p++,s.x=0,s.y+=i.lineHeight,u=null,_=0)}const y=h[h.length-1];y!=="\r"&&y!==`
`&&(/(?:\s)/.test(y)&&(d=m),n.push(d),f=Math.max(f,d),o.push(-1));const E=[];for(let w=0;w<=p;w++){let D=0;this._align==="right"?D=f-n[w]:this._align==="center"?D=(f-n[w])/2:this._align==="justify"&&(D=o[w]<0?0:(f-n[w])/o[w]),E.push(D)}const B=r.length,A={},b=[],I=this._activePagesMeshData;c.push(...I);for(let w=0;w<B;w++){const D=r[w].texture,R=D.baseTexture.uid;if(!A[R]){let S=c.pop();if(!S){const z=new Ci;let k,J;i.distanceFieldType==="none"?(k=new Fe(L.EMPTY),J=C.NORMAL):(k=new Fe(L.EMPTY,{program:Qt.from(tm,Jp),uniforms:{uFWidth:0}}),J=C.NORMAL_NPM);const st=new Mt(z,k);st.blendMode=J,S={index:0,indexCount:0,vertexCount:0,uvsCount:0,total:0,mesh:st,vertices:null,uvs:null,indices:null}}S.index=0,S.indexCount=0,S.vertexCount=0,S.uvsCount=0,S.total=0;const{_textureCache:X}=this;X[R]=X[R]||new L(D.baseTexture),S.mesh.texture=X[R],S.mesh.tint=this._tint,b.push(S),A[R]=S}A[R].total++}for(let w=0;w<I.length;w++)b.includes(I[w])||this.removeChild(I[w].mesh);for(let w=0;w<b.length;w++)b[w].mesh.parent!==this&&this.addChild(b[w].mesh);this._activePagesMeshData=b;for(const w in A){const D=A[w],R=D.total;if(!(((O=D.indices)==null?void 0:O.length)>6*R)||D.vertices.length<Mt.BATCHABLE_SIZE*2)D.vertices=new Float32Array(4*2*R),D.uvs=new Float32Array(4*2*R),D.indices=new Uint16Array(6*R);else{const S=D.total,X=D.vertices;for(let z=S*4*2;z<X.length;z++)X[z]=0}D.mesh.size=6*R}for(let w=0;w<B;w++){const D=r[w];let R=D.position.x+E[D.line]*(this._align==="justify"?D.prevSpaces:1);this._roundPixels&&(R=Math.round(R));const S=R*e,X=D.position.y*e,z=D.texture,k=A[z.baseTexture.uid],J=z.frame,st=z._uvs,M=k.index++;k.indices[M*6+0]=0+M*4,k.indices[M*6+1]=1+M*4,k.indices[M*6+2]=2+M*4,k.indices[M*6+3]=0+M*4,k.indices[M*6+4]=2+M*4,k.indices[M*6+5]=3+M*4,k.vertices[M*8+0]=S,k.vertices[M*8+1]=X,k.vertices[M*8+2]=S+J.width*e,k.vertices[M*8+3]=X,k.vertices[M*8+4]=S+J.width*e,k.vertices[M*8+5]=X+J.height*e,k.vertices[M*8+6]=S,k.vertices[M*8+7]=X+J.height*e,k.uvs[M*8+0]=st.x0,k.uvs[M*8+1]=st.y0,k.uvs[M*8+2]=st.x1,k.uvs[M*8+3]=st.y1,k.uvs[M*8+4]=st.x2,k.uvs[M*8+5]=st.y2,k.uvs[M*8+6]=st.x3,k.uvs[M*8+7]=st.y3}this._textWidth=f*e,this._textHeight=(s.y+i.lineHeight)*e;for(const w in A){const D=A[w];if(this.anchor.x!==0||this.anchor.y!==0){let z=0;const k=this._textWidth*this.anchor.x,J=this._textHeight*this.anchor.y;for(let st=0;st<D.total;st++)D.vertices[z++]-=k,D.vertices[z++]-=J,D.vertices[z++]-=k,D.vertices[z++]-=J,D.vertices[z++]-=k,D.vertices[z++]-=J,D.vertices[z++]-=k,D.vertices[z++]-=J}this._maxLineHeight=T*e;const R=D.mesh.geometry.getBuffer("aVertexPosition"),S=D.mesh.geometry.getBuffer("aTextureCoord"),X=D.mesh.geometry.getIndex();R.data=D.vertices,S.data=D.uvs,X.data=D.indices,R.update(),S.update(),X.update()}for(let w=0;w<r.length;w++)Th.push(r[w]);this._font=i,this.dirty=!1}updateTransform(){this.validate(),this.containerUpdateTransform()}_render(i){this._autoResolution&&this._resolution!==i.resolution&&(this._resolution=i.resolution,this.dirty=!0);const{distanceFieldRange:t,distanceFieldType:e,size:s}=St.available[this._fontName];if(e!=="none"){const{a:r,b:n,c:o,d:a}=this.worldTransform,h=Math.sqrt(r*r+n*n),l=Math.sqrt(o*o+a*a),c=(Math.abs(h)+Math.abs(l))/2,u=this.fontSize/s,d=i._view.resolution;for(const f of this._activePagesMeshData)f.mesh.shader.uniforms.uFWidth=c*t*u*d}super._render(i)}getLocalBounds(){return this.validate(),super.getLocalBounds()}validate(){const i=St.available[this._fontName];if(!i)throw new Error(`Missing BitmapFont "${this._fontName}"`);this._font!==i&&(this.dirty=!0),this.dirty&&this.updateText()}get tint(){return this._tint}set tint(i){if(this._tint!==i){this._tint=i;for(let t=0;t<this._activePagesMeshData.length;t++)this._activePagesMeshData[t].mesh.tint=i}}get align(){return this._align}set align(i){this._align!==i&&(this._align=i,this.dirty=!0)}get fontName(){return this._fontName}set fontName(i){if(!St.available[i])throw new Error(`Missing BitmapFont "${i}"`);this._fontName!==i&&(this._fontName=i,this.dirty=!0)}get fontSize(){var i;return(i=this._fontSize)!=null?i:St.available[this._fontName].size}set fontSize(i){this._fontSize!==i&&(this._fontSize=i,this.dirty=!0)}get anchor(){return this._anchor}set anchor(i){typeof i=="number"?this._anchor.set(i):this._anchor.copyFrom(i)}get text(){return this._text}set text(i){i=String(i==null?"":i),this._text!==i&&(this._text=i,this.dirty=!0)}get maxWidth(){return this._maxWidth}set maxWidth(i){this._maxWidth!==i&&(this._maxWidth=i,this.dirty=!0)}get maxLineHeight(){return this.validate(),this._maxLineHeight}get textWidth(){return this.validate(),this._textWidth}get letterSpacing(){return this._letterSpacing}set letterSpacing(i){this._letterSpacing!==i&&(this._letterSpacing=i,this.dirty=!0)}get roundPixels(){return this._roundPixels}set roundPixels(i){i!==this._roundPixels&&(this._roundPixels=i,this.dirty=!0)}get textHeight(){return this.validate(),this._textHeight}get resolution(){return this._resolution}set resolution(i){this._autoResolution=!1,this._resolution!==i&&(this._resolution=i,this.dirty=!0)}destroy(i){const{_textureCache:t}=this,s=St.available[this._fontName].distanceFieldType==="none"?yh:xh;s.push(...this._activePagesMeshData);for(const r of this._activePagesMeshData)this.removeChild(r.mesh);this._activePagesMeshData=[],s.filter(r=>t[r.mesh.texture.baseTexture.uid]).forEach(r=>{r.mesh.texture=L.EMPTY});for(const r in t)t[r].destroy(),delete t[r];this._font=null,this._textureCache=null,super.destroy(i)}};let Eh=bh;Eh.styleDefaults={align:"left",tint:16777215,maxWidth:0,letterSpacing:0};const em=[".xml",".fnt"],wh={extension:{type:F.LoadParser,priority:Yt.Normal},test(i){return em.includes(yt.extname(i))},async testParse(i){return Mi.test(i)||Ns.test(i)},async parse(i,t,e){const s=Mi.test(i)?Mi.parse(i):Ns.parse(i),{src:r}=t,{page:n}=s,o=[];for(let l=0;l<n.length;++l){const c=n[l].file,u=yt.join(yt.dirname(r),c);o.push(u)}const a=await e.load(o),h=o.map(l=>a[l]);return St.install(s,h,!0)},async load(i,t){return(await P.ADAPTER.fetch(i)).text()},unload(i){i.destroy()}};U.add(wh);const im={AlphaFilter:Sf,BlurFilter:Mf,BlurFilterPass:ln,ColorMatrixFilter:cn,DisplacementFilter:Ff,FXAAFilter:Uf,NoiseFilter:Gf};class Fn{constructor(t){this.renderer=t}render(t){const e=this.renderer,s=t.worldTransform;e.canvasContext.activeContext.globalAlpha=t.worldAlpha,e.canvasContext.setBlendMode(t.blendMode),e.canvasContext.setContextTransform(s,t.roundPixels),t.drawMode!==Bt.TRIANGLES?this._renderTriangleMesh(t):this._renderTriangles(t)}_renderTriangleMesh(t){const e=t.geometry.buffers[0].data.length;for(let s=0;s<e-2;s++){const r=s*2;this._renderDrawTriangle(t,r,r+2,r+4)}}_renderTriangles(t){const e=t.geometry.getIndex().data,s=e.length;for(let r=0;r<s;r+=3){const n=e[r]*2,o=e[r+1]*2,a=e[r+2]*2;this._renderDrawTriangle(t,n,o,a)}}_renderDrawTriangle(t,e,s,r){var J;const n=this.renderer.canvasContext.activeContext,o=t.geometry.buffers[0].data,{uvs:a,texture:h}=t;if(!h.valid)return;const l=t.tint!==16777215,c=h.baseTexture,u=c.width,d=c.height;t._cachedTexture&&t._cachedTexture.baseTexture!==c&&(t._cachedTint=16777215,(J=t._cachedTexture)==null||J.destroy(),t._cachedTexture=null,t._tintedCanvas=null),l&&t._cachedTint!==t.tint&&(t._cachedTint=t.tint,t._cachedTexture=t._cachedTexture||new L(c),t._tintedCanvas=pt.getTintedCanvas({texture:t._cachedTexture},t.tint));const f=l?t._tintedCanvas:c.getDrawableSource(),p=a[e]*c.width,g=a[s]*c.width,m=a[r]*c.width,x=a[e+1]*c.height,T=a[s+1]*c.height,_=a[r+1]*c.height;let y=o[e],E=o[s],B=o[r],A=o[e+1],b=o[s+1],I=o[r+1];const O=t.canvasPadding/this.renderer.canvasContext.activeResolution;if(O>0){const{a:st,b:M,c:V,d:Y}=t.worldTransform,q=(y+E+B)/3,K=(A+b+I)/3;let tt=y-q,Q=A-K,it=st*tt+V*Q,ht=M*tt+Y*Q,gt=Math.sqrt(it*it+ht*ht),rt=1+O/gt;y=q+tt*rt,A=K+Q*rt,tt=E-q,Q=b-K,it=st*tt+V*Q,ht=M*tt+Y*Q,gt=Math.sqrt(it*it+ht*ht),rt=1+O/gt,E=q+tt*rt,b=K+Q*rt,tt=B-q,Q=I-K,it=st*tt+V*Q,ht=M*tt+Y*Q,gt=Math.sqrt(it*it+ht*ht),rt=1+O/gt,B=q+tt*rt,I=K+Q*rt}n.save(),n.beginPath(),n.moveTo(y,A),n.lineTo(E,b),n.lineTo(B,I),n.closePath(),n.clip();const w=p*T+x*m+g*_-T*m-x*g-p*_,D=y*T+x*B+E*_-T*B-x*E-y*_,R=p*E+y*m+g*B-E*m-y*g-p*B,S=p*T*B+x*E*m+y*g*_-y*T*m-x*g*B-p*E*_,X=A*T+x*I+b*_-T*I-x*b-A*_,z=p*b+A*m+g*I-b*m-A*g-p*I,k=p*T*I+x*b*m+A*g*_-A*T*m-x*g*I-p*b*_;n.transform(D/w,X/w,R/w,z/w,S/w,k/w),n.drawImage(f,0,0,u*c.resolution,d*c.resolution,0,0,u,d),n.restore(),this.renderer.canvasContext.invalidateBlendMode()}renderMeshFlat(t){const e=this.renderer.canvasContext.activeContext,s=t.geometry.getBuffer("aVertexPosition").data,r=s.length/2;e.beginPath();for(let n=1;n<r-2;++n){const o=n*2,a=s[o],h=s[o+1],l=s[o+2],c=s[o+3],u=s[o+4],d=s[o+5];e.moveTo(a,h),e.lineTo(l,c),e.lineTo(u,d)}e.fillStyle="#FF0000",e.fill(),e.closePath()}destroy(){this.renderer=null}}Fn.extension={name:"mesh",type:F.CanvasRendererPlugin},U.add(Fn),P.MESH_CANVAS_PADDING=0,Fe.prototype._renderCanvas=function(t,e){t.plugins.mesh.render(e)},Ri.prototype._cachedTint=16777215,Ri.prototype._tintedCanvas=null,Ri.prototype._canvasUvs=null,Ri.prototype._renderCanvas=function(t){const e=t.canvasContext.activeContext,s=this.worldTransform,r=this.tint!==16777215,n=this.texture;if(!n.valid)return;r&&this._cachedTint!==this.tint&&(this._cachedTint=this.tint,this._tintedCanvas=pt.getTintedCanvas(this,this.tint));const o=r?this._tintedCanvas:n.baseTexture.getDrawableSource();this._canvasUvs||(this._canvasUvs=[0,0,0,0,0,0,0,0]);const a=this.vertices,h=this._canvasUvs,l=r?0:n.frame.x,c=r?0:n.frame.y,u=l+n.frame.width,d=c+n.frame.height;h[0]=l,h[1]=l+this._leftWidth,h[2]=u-this._rightWidth,h[3]=u,h[4]=c,h[5]=c+this._topHeight,h[6]=d-this._bottomHeight,h[7]=d;for(let f=0;f<8;f++)h[f]*=n.baseTexture.resolution;e.globalAlpha=this.worldAlpha,t.canvasContext.setBlendMode(this.blendMode),t.canvasContext.setContextTransform(s,this.roundPixels);for(let f=0;f<3;f++)for(let p=0;p<3;p++){const g=p*2+f*8,m=Math.max(1,h[p+1]-h[p]),x=Math.max(1,h[f+5]-h[f+4]),T=Math.max(1,a[g+10]-a[g]),_=Math.max(1,a[g+11]-a[g+1]);e.drawImage(o,h[p],h[f+4],m,x,a[g],a[g+1],T,_)}};let Sh=!1;Mt.prototype._cachedTint=16777215,Mt.prototype._tintedCanvas=null,Mt.prototype._cachedTexture=null,Mt.prototype._renderCanvas=function(t){this.shader.uvMatrix&&(this.shader.uvMatrix.update(),this.calculateUvs()),this.material._renderCanvas?this.material._renderCanvas(t,this):Sh||(Sh=!0,globalThis.console&&console.warn("Mesh with custom shaders are not supported in CanvasRenderer."))},Mt.prototype._canvasPadding=null,Object.defineProperty(Mt.prototype,"canvasPadding",{get(){return this._canvasPadding!==null?this._canvasPadding:P.MESH_CANVAS_PADDING},set(i){this._canvasPadding=i}}),dh.prototype._renderCanvas=function(t){this.autoUpdate&&this.geometry.getBuffer("aVertexPosition").update(),this.shader.update&&this.shader.update(),this.calculateUvs(),this.material._renderCanvas(t,this)},ch.prototype._renderCanvas=function(t){(this.autoUpdate||this.geometry._width!==this.shader.texture.height)&&(this.geometry._width=this.shader.texture.height,this.geometry.update()),this.shader.update&&this.shader.update(),this.calculateUvs(),this.material._renderCanvas(t,this)};class Bi{static offsetPolygon(t,e){const s=[],r=t.length;e=Bi.isPolygonClockwise(t)?e:-1*e;for(let n=0;n<r;n+=2){let o=n-2;o<0&&(o+=r);const a=(n+2)%r;let h=t[n]-t[o],l=t[n+1]-t[o+1],c=Math.sqrt(h*h+l*l);h/=c,l/=c,h*=e,l*=e;const u=-l,d=h,f=[t[o]+u,t[o+1]+d],p=[t[n]+u,t[n+1]+d];let g=t[a]-t[n],m=t[a+1]-t[n+1];c=Math.sqrt(g*g+m*m),g/=c,m/=c,g*=e,m*=e;const x=-m,T=g,_=[t[n]+x,t[n+1]+T],y=[t[a]+x,t[a+1]+T],E=Bi.findIntersection(f[0],f[1],p[0],p[1],_[0],_[1],y[0],y[1]);E&&s.push(...E)}return s}static findIntersection(t,e,s,r,n,o,a,h){const l=(h-o)*(s-t)-(a-n)*(r-e),c=(a-n)*(e-o)-(h-o)*(t-n),u=(s-t)*(e-o)-(r-e)*(t-n);if(l===0)return c===0&&u===0?[(t+s)/2,(e+r)/2]:null;const d=c/l;return[t+d*(s-t),e+d*(r-e)]}static isPolygonClockwise(t){let e=0;for(let s=0,r=t.length-2;s<t.length;r=s,s+=2)e+=(t[s]-t[r])*(t[s+1]+t[r+1]);return e>0}}class On{constructor(t){this._svgMatrix=null,this._tempMatrix=new Z,this.renderer=t}_calcCanvasStyle(t,e){let s;return t.texture&&t.texture.baseTexture!==L.WHITE.baseTexture?t.texture.valid?(s=pt.getTintedPattern(t.texture,e),this.setPatternTransform(s,t.matrix||Z.IDENTITY)):s="#808080":s=`#${`00000${(e|0).toString(16)}`.slice(-6)}`,s}render(t){const e=this.renderer,s=e.canvasContext.activeContext,r=t.worldAlpha,n=t.transform.worldTransform;e.canvasContext.setContextTransform(n),e.canvasContext.setBlendMode(t.blendMode);const o=t.geometry.graphicsData;let a,h;const l=(t.tint>>16&255)/255,c=(t.tint>>8&255)/255,u=(t.tint&255)/255;for(let d=0;d<o.length;d++){const f=o[d],p=f.shape,g=f.fillStyle,m=f.lineStyle,x=f.fillStyle.color|0,T=f.lineStyle.color|0;if(f.matrix&&e.canvasContext.setContextTransform(n.copyTo(this._tempMatrix).append(f.matrix)),g.visible){const _=((x>>16&255)/255*l*255<<16)+((x>>8&255)/255*c*255<<8)+(x&255)/255*u*255;a=this._calcCanvasStyle(g,_)}if(m.visible){const _=((T>>16&255)/255*l*255<<16)+((T>>8&255)/255*c*255<<8)+(T&255)/255*u*255;h=this._calcCanvasStyle(m,_)}if(s.lineWidth=m.width,s.lineCap=m.cap,s.lineJoin=m.join,s.miterLimit=m.miterLimit,f.type===et.POLY){s.beginPath();const _=p;let y=_.points;const E=f.holes;let B,A,b,I,O;s.moveTo(y[0],y[1]);for(let w=2;w<y.length;w+=2)s.lineTo(y[w],y[w+1]);if(_.closeStroke&&s.closePath(),E.length>0){O=[],B=0,b=y[0],I=y[1];for(let w=2;w+2<y.length;w+=2)B+=(y[w]-b)*(y[w+3]-I)-(y[w+2]-b)*(y[w+1]-I);for(let w=0;w<E.length;w++)if(y=E[w].shape.points,!!y){A=0,b=y[0],I=y[1];for(let D=2;D+2<y.length;D+=2)A+=(y[D]-b)*(y[D+3]-I)-(y[D+2]-b)*(y[D+1]-I);if(A*B<0){s.moveTo(y[0],y[1]);for(let D=2;D<y.length;D+=2)s.lineTo(y[D],y[D+1])}else{s.moveTo(y[y.length-2],y[y.length-1]);for(let D=y.length-4;D>=0;D-=2)s.lineTo(y[D],y[D+1])}E[w].shape.closeStroke&&s.closePath(),O[w]=A*B<0}}g.visible&&(s.globalAlpha=g.alpha*r,s.fillStyle=a,s.fill()),m.visible&&this.paintPolygonStroke(_,m,h,E,O,r,s)}else if(f.type===et.RECT){const _=p;if(g.visible&&(s.globalAlpha=g.alpha*r,s.fillStyle=a,s.fillRect(_.x,_.y,_.width,_.height)),m.visible){const y=m.width*(.5-(1-m.alignment)),E=_.width+2*y,B=_.height+2*y;s.globalAlpha=m.alpha*r,s.strokeStyle=h,s.strokeRect(_.x-y,_.y-y,E,B)}}else if(f.type===et.CIRC){const _=p;if(s.beginPath(),s.arc(_.x,_.y,_.radius,0,2*Math.PI),s.closePath(),g.visible&&(s.globalAlpha=g.alpha*r,s.fillStyle=a,s.fill()),m.visible){if(m.alignment!==.5){const y=m.width*(.5-(1-m.alignment));s.beginPath(),s.arc(_.x,_.y,_.radius+y,0,2*Math.PI),s.closePath()}s.globalAlpha=m.alpha*r,s.strokeStyle=h,s.stroke()}}else if(f.type===et.ELIP){const _=p,y=m.alignment===1;if(y||this.paintEllipse(_,g,m,a,r,s),m.visible){if(m.alignment!==.5){const E=.5522848,B=m.width*(.5-(1-m.alignment)),A=(_.width+B)*2,b=(_.height+B)*2,I=_.x-A/2,O=_.y-b/2,w=A/2*E,D=b/2*E,R=I+A,S=O+b,X=I+A/2,z=O+b/2;s.beginPath(),s.moveTo(I,z),s.bezierCurveTo(I,z-D,X-w,O,X,O),s.bezierCurveTo(X+w,O,R,z-D,R,z),s.bezierCurveTo(R,z+D,X+w,S,X,S),s.bezierCurveTo(X-w,S,I,z+D,I,z),s.closePath()}s.globalAlpha=m.alpha*r,s.strokeStyle=h,s.stroke()}y&&this.paintEllipse(_,g,m,a,r,s)}else if(f.type===et.RREC){const _=p,y=m.alignment===1;if(y||this.paintRoundedRectangle(_,g,m,a,r,s),m.visible){if(m.alignment!==.5){const E=_.width,B=_.height,A=m.width*(.5-(1-m.alignment)),b=_.x-A,I=_.y-A,O=_.width+2*A,w=_.height+2*A,D=A*(m.alignment>=1?Math.min(O/E,w/B):Math.min(E/O,B/w));let R=_.radius+D;const S=Math.min(O,w)/2;R=R>S?S:R,s.beginPath(),s.moveTo(b,I+R),s.lineTo(b,I+w-R),s.quadraticCurveTo(b,I+w,b+R,I+w),s.lineTo(b+O-R,I+w),s.quadraticCurveTo(b+O,I+w,b+O,I+w-R),s.lineTo(b+O,I+R),s.quadraticCurveTo(b+O,I,b+O-R,I),s.lineTo(b+R,I),s.quadraticCurveTo(b,I,b,I+R),s.closePath()}s.globalAlpha=m.alpha*r,s.strokeStyle=h,s.stroke()}y&&this.paintRoundedRectangle(_,g,m,a,r,s)}}}paintPolygonStroke(t,e,s,r,n,o,a){if(e.alignment!==.5){const h=e.width*(.5-(1-e.alignment));let l=Bi.offsetPolygon(t.points,h),c;a.beginPath(),a.moveTo(l[0],l[1]);for(let u=2;u<l.length;u+=2)a.lineTo(l[u],l[u+1]);t.closeStroke&&a.closePath();for(let u=0;u<r.length;u++){if(c=r[u].shape.points,l=Bi.offsetPolygon(c,h),n[u]){a.moveTo(l[0],l[1]);for(let d=2;d<l.length;d+=2)a.lineTo(l[d],l[d+1])}else{a.moveTo(l[l.length-2],l[l.length-1]);for(let d=l.length-4;d>=0;d-=2)a.lineTo(l[d],l[d+1])}r[u].shape.closeStroke&&a.closePath()}}a.globalAlpha=e.alpha*o,a.strokeStyle=s,a.stroke()}paintEllipse(t,e,s,r,n,o){const a=t.width*2,h=t.height*2,l=t.x-a/2,c=t.y-h/2,u=.5522848,d=a/2*u,f=h/2*u,p=l+a,g=c+h,m=l+a/2,x=c+h/2;s.alignment===0&&o.save(),o.beginPath(),o.moveTo(l,x),o.bezierCurveTo(l,x-f,m-d,c,m,c),o.bezierCurveTo(m+d,c,p,x-f,p,x),o.bezierCurveTo(p,x+f,m+d,g,m,g),o.bezierCurveTo(m-d,g,l,x+f,l,x),o.closePath(),s.alignment===0&&o.clip(),e.visible&&(o.globalAlpha=e.alpha*n,o.fillStyle=r,o.fill()),s.alignment===0&&o.restore()}paintRoundedRectangle(t,e,s,r,n,o){const a=t.x,h=t.y,l=t.width,c=t.height;let u=t.radius;const d=Math.min(l,c)/2;u=u>d?d:u,s.alignment===0&&o.save(),o.beginPath(),o.moveTo(a,h+u),o.lineTo(a,h+c-u),o.quadraticCurveTo(a,h+c,a+u,h+c),o.lineTo(a+l-u,h+c),o.quadraticCurveTo(a+l,h+c,a+l,h+c-u),o.lineTo(a+l,h+u),o.quadraticCurveTo(a+l,h,a+l-u,h),o.lineTo(a+u,h),o.quadraticCurveTo(a,h,a,h+u),o.closePath(),s.alignment===0&&o.clip(),e.visible&&(o.globalAlpha=e.alpha*n,o.fillStyle=r,o.fill()),s.alignment===0&&o.restore()}setPatternTransform(t,e){if(this._svgMatrix!==!1){if(!this._svgMatrix){const s=document.createElementNS("http://www.w3.org/2000/svg","svg");if(s!=null&&s.createSVGMatrix&&(this._svgMatrix=s.createSVGMatrix()),!this._svgMatrix||!t.setTransform){this._svgMatrix=!1;return}}this._svgMatrix.a=e.a,this._svgMatrix.b=e.b,this._svgMatrix.c=e.c,this._svgMatrix.d=e.d,this._svgMatrix.e=e.tx,this._svgMatrix.f=e.ty,t.setTransform(this._svgMatrix.inverse())}}destroy(){this.renderer=null,this._svgMatrix=null,this._tempMatrix=null}}On.extension={name:"graphics",type:F.CanvasRendererPlugin},U.add(On);let Ln;const Di=new Z;Ne.prototype.generateCanvasTexture=function(t,e=1){const s=this.getLocalBounds(),r=Ot.create({width:s.width,height:s.height,scaleMode:t,resolution:e});Ln||(Ln=new ge),this.transform.updateLocalTransform(),this.transform.localTransform.copyTo(Di),Di.invert(),Di.tx-=s.x,Di.ty-=s.y,Ln.render(this,{renderTexture:r,clear:!0,transform:Di});const n=L.from(r.baseTexture._canvasRenderTarget.canvas,{scaleMode:t});return n.baseTexture.setResolution(e),n},Ne.prototype.cachedGraphicsData=[],Ne.prototype._renderCanvas=function(t){this.isMask!==!0&&(this.finishPoly(),t.plugins.graphics.render(this))};const Ah=new Z;class Un{constructor(t){this.renderer=t}render(t){const e=t._texture,s=this.renderer,r=s.canvasContext.activeContext,n=s.canvasContext.activeResolution;if(!e.valid)return;const o=e._frame.width,a=e._frame.height;let h=e._frame.width,l=e._frame.height;e.trim&&(h=e.trim.width,l=e.trim.height);let c=t.transform.worldTransform,u=0,d=0;const f=e.baseTexture.getDrawableSource();if(e.orig.width<=0||e.orig.height<=0||!e.valid||!f)return;s.canvasContext.setBlendMode(t.blendMode,!0),r.globalAlpha=t.worldAlpha;const p=e.baseTexture.scaleMode===Dt.LINEAR,g=s.canvasContext.smoothProperty;g&&r[g]!==p&&(r[g]=p),e.trim?(u=e.trim.width/2+e.trim.x-t.anchor.x*e.orig.width,d=e.trim.height/2+e.trim.y-t.anchor.y*e.orig.height):(u=(.5-t.anchor.x)*e.orig.width,d=(.5-t.anchor.y)*e.orig.height),e.rotate&&(c.copyTo(Ah),c=Ah,nt.matrixAppendRotationInv(c,e.rotate,u,d),u=0,d=0),u-=h/2,d-=l/2,s.canvasContext.setContextTransform(c,t.roundPixels,1),t.roundPixels&&(u=u|0,d=d|0);const m=e.baseTexture.resolution,x=s.canvasContext._outerBlend;x&&(r.save(),r.beginPath(),r.rect(u*n,d*n,h*n,l*n),r.clip()),t.tint!==16777215?((t._cachedTint!==t.tint||t._tintedCanvas.tintId!==t._texture._updateID)&&(t._cachedTint=t.tint,t._tintedCanvas=pt.getTintedCanvas(t,t.tint)),r.drawImage(t._tintedCanvas,0,0,Math.floor(o*m),Math.floor(a*m),Math.floor(u*n),Math.floor(d*n),Math.floor(h*n),Math.floor(l*n))):r.drawImage(f,e._frame.x*m,e._frame.y*m,Math.floor(o*m),Math.floor(a*m),Math.floor(u*n),Math.floor(d*n),Math.floor(h*n),Math.floor(l*n)),x&&r.restore(),s.canvasContext.setBlendMode(C.NORMAL)}destroy(){this.renderer=null}}Un.extension={name:"sprite",type:F.CanvasRendererPlugin},U.add(Un),jt.prototype._tintedCanvas=null,jt.prototype._renderCanvas=function(t){t.plugins.sprite.render(this)};const Ch=new j;class kn{constructor(t){this.renderer=t}async image(t,e,s){const r=new Image;return r.src=await this.base64(t,e,s),r}async base64(t,e,s){const r=this.canvas(t);if(r.toDataURL!==void 0)return r.toDataURL(e,s);if(r.convertToBlob!==void 0){const n=await r.convertToBlob({type:e,quality:s});return await new Promise(o=>{const a=new FileReader;a.onload=()=>o(a.result),a.readAsDataURL(n)})}throw new Error("CanvasExtract.base64() requires ICanvas.toDataURL or ICanvas.convertToBlob to be implemented")}canvas(t,e){const s=this.renderer;let r,n,o;t&&(t instanceof Ot?o=t:o=s.generateTexture(t)),o?(r=o.baseTexture._canvasRenderTarget.context,n=o.baseTexture._canvasRenderTarget.resolution,e=e!=null?e:o.frame):(r=s.canvasContext.rootContext,n=s._view.resolution,e||(e=Ch,e.width=s.width,e.height=s.height));const a=Math.round(e.x*n),h=Math.round(e.y*n),l=Math.round(e.width*n),c=Math.round(e.height*n),u=new Ge(l,c,1),d=r.getImageData(a,h,l,c);return u.context.putImageData(d,0,0),u.canvas}pixels(t,e){const s=this.renderer;let r,n,o;t&&(t instanceof Ot?o=t:o=s.generateTexture(t)),o?(r=o.baseTexture._canvasRenderTarget.context,n=o.baseTexture._canvasRenderTarget.resolution,e=e!=null?e:o.frame):(r=s.canvasContext.rootContext,n=s.resolution,e||(e=Ch,e.width=s.width,e.height=s.height));const a=Math.round(e.x*n),h=Math.round(e.y*n),l=Math.round(e.width*n),c=Math.round(e.height*n);return r.getImageData(a,h,l,c).data}destroy(){this.renderer=null}}kn.extension={name:"extract",type:F.CanvasRendererSystem},U.add(kn);const Rh=16;function sm(i,t){const e=i;if(t instanceof $){const s=t.source,r=s.width===0?e.canvas.width:Math.min(e.canvas.width,s.width),n=s.height===0?e.canvas.height:Math.min(e.canvas.height,s.height);return e.ctx.drawImage(s,0,0,r,n,0,0,e.canvas.width,e.canvas.height),!0}return!1}class Gn extends Bn{constructor(t){super(t),this.uploadHookHelper=this,this.canvas=P.ADAPTER.createCanvas(Rh,Rh),this.ctx=this.canvas.getContext("2d"),this.registerUploadHook(sm)}destroy(){super.destroy(),this.ctx=null,this.canvas=null}}return Gn.extension={name:"prepare",type:F.CanvasRendererSystem},U.add(Gn),v.ALPHA_MODES=Nt,v.AbstractMultiResource=ar,v.AccessibilityManager=fn,v.AnimatedSprite=Bs,v.Application=mn,v.ArrayResource=Po,v.Assets=xi,v.AssetsClass=wa,v.Attribute=oi,v.BLEND_MODES=C,v.BUFFER_BITS=At,v.BUFFER_TYPE=Xt,v.BackgroundSystem=Gr,v.BaseImageResource=le,v.BasePrepare=Bn,v.BaseRenderTexture=is,v.BaseTexture=$,v.BatchDrawCall=ds,v.BatchGeometry=$r,v.BatchRenderer=ci,v.BatchShaderGenerator=ha,v.BatchSystem=_r,v.BatchTextureArray=fs,v.BitmapFont=St,v.BitmapFontData=Pi,v.BitmapText=Eh,v.BlobResource=ka,v.Bounds=ui,v.BrowserAdapter=zn,v.Buffer=dt,v.BufferResource=Xe,v.BufferSystem=qr,v.CLEAR_MODES=Ht,v.COLOR_MASK_BITS=Vn,v.Cache=je,v.CanvasContextSystem=rn,v.CanvasExtract=kn,v.CanvasGraphicsRenderer=On,v.CanvasMaskSystem=tn,v.CanvasMeshRenderer=Fn,v.CanvasObjectRendererSystem=en,v.CanvasPrepare=Gn,v.CanvasRenderer=ge,v.CanvasResource=hr,v.CanvasSpriteRenderer=Un,v.Circle=ki,v.CompressedTextureResource=_e,v.Container=wt,v.ContextSystem=vr,v.CountLimiter=fh,v.CubeResource=lr,v.DEG_TO_RAD=ro,v.DRAW_MODES=Bt,v.DisplayObject=at,v.ENV=lt,v.Ellipse=Gi,v.EventBoundary=va,v.EventSystem=dn,v.ExtensionType=F,v.Extract=wn,v.FORMATS=N,v.FORMATS_TO_COMPONENTS=Wa,v.FederatedDisplayObject=ya,v.FederatedEvent=We,v.FederatedMouseEvent=_i,v.FederatedPointerEvent=kt,v.FederatedWheelEvent=ys,v.FillStyle=Ei,v.Filter=Ut,v.FilterState=No,v.FilterSystem=gr,v.Framebuffer=es,v.FramebufferSystem=yr,v.GC_MODES=Oi,v.GLFramebuffer=Oo,v.GLProgram=sa,v.GLTexture=us,v.GRAPHICS_CURVES=$e,v.GenerateTextureSystem=kr,v.Geometry=ce,v.GeometrySystem=Tr,v.Graphics=Ne,v.GraphicsData=Ai,v.GraphicsGeometry=In,v.IGLUniformData=Vd,v.INSTALLED=Qi,v.INTERNAL_FORMATS=Ct,v.INTERNAL_FORMAT_TO_BYTES_PER_PIXEL=bi,v.ImageBitmapResource=Re,v.ImageResource=cr,v.LINE_CAP=ve,v.LINE_JOIN=fe,v.LineStyle=Rs,v.LoaderParserPriority=Yt,v.MASK_TYPES=mt,v.MIPMAP_MODES=Gt,v.MSAA_QUALITY=ft,v.MaskData=Lo,v.MaskSystem=Ir,v.Matrix=Z,v.Mesh=Mt,v.MeshBatchUvs=nh,v.MeshGeometry=Ci,v.MeshMaterial=Fe,v.MultisampleSystem=Kr,v.NineSlicePlane=Ri,v.ObjectRenderer=ai,v.ObjectRendererSystem=Zr,v.ObservablePoint=oe,v.PI_2=Ke,v.PRECISION=Rt,v.ParticleContainer=da,v.ParticleRenderer=nn,v.PlaneGeometry=hh,v.PluginSystem=Xr,v.Point=W,v.Polygon=xe,v.Prepare=Dn,v.Program=Qt,v.ProjectionSystem=Br,v.Quad=Do,v.QuadUv=pr,v.RAD_TO_DEG=so,v.RENDERER_TYPE=xt,v.Rectangle=j,v.RenderTexture=Ot,v.RenderTexturePool=fr,v.RenderTextureSystem=Dr,v.Renderer=Me,v.ResizePlugin=gn,v.Resource=He,v.RopeGeometry=lh,v.RoundedRectangle=Hi,v.Runner=It,v.SAMPLER_TYPES=Fi,v.SCALE_MODES=Dt,v.SHAPES=et,v.SVGResource=Ve,v.ScissorSystem=Pr,v.Shader=Wt,v.ShaderSystem=Nr,v.SimpleMesh=dh,v.SimplePlane=uh,v.SimpleRope=ch,v.Sprite=jt,v.SpriteMaskFilter=qo,v.Spritesheet=Ms,v.StartupSystem=zr,v.State=Jt,v.StateSystem=Or,v.StencilSystem=Mr,v.SystemManager=Vr,v.TARGETS=ye,v.TEXT_GRADIENT=mi,v.TYPES=H,v.TYPES_TO_BYTES_PER_COMPONENT=bn,v.TYPES_TO_BYTES_PER_PIXEL=ja,v.TemporaryDisplayObject=la,v.Text=gi,v.TextFormat=Mi,v.TextMetrics=G,v.TextStyle=ue,v.Texture=L,v.TextureGCSystem=Lr,v.TextureMatrix=hs,v.TextureSystem=Ur,v.TextureUvs=dr,v.Ticker=ot,v.TickerPlugin=Xs,v.TilingSprite=ms,v.TilingSpriteRenderer=Qr,v.TimeLimiter=$p,v.Transform=Ze,v.TransformFeedback=af,v.TransformFeedbackSystem=Wr,v.UPDATE_PRIORITY=pe,v.UniformGroup=Lt,v.VERSION=lf,v.VideoResource=ts,v.ViewSystem=Hr,v.ViewableBuffer=ps,v.WRAP_MODES=Kt,v.XMLFormat=Ds,v.XMLStringFormat=Ns,v.accessibleTarget=_a,v.autoDetectFormat=_h,v.autoDetectRenderer=oa,v.autoDetectResource=or,v.cacheTextureArray=Aa,v.canUseNewCanvasBlendModes=sn,v.canvasUtils=pt,v.checkDataUrl=_n,v.checkExtension=vi,v.checkMaxIfStatementsInShader=Yo,v.convertToList=ee,v.createStringVariations=Ea,v.createTexture=Ti,v.createUBOElements=ta,v.defaultFilterVertex=jr,v.defaultVertex=aa,v.detectAvif=Fa,v.detectCompressedTextures=Ha,v.detectDefaults=Ua,v.detectWebp=Oa,v.extensions=U,v.filters=im,v.generateProgram=ra,v.generateUniformBufferSync=ia,v.getFontFamilyName=Ia,v.getTestContext=Xo,v.getUBOData=ea,v.graphicsUtils=Op,v.groupD8=nt,v.isMobile=Vt,v.isSingleItem=yi,v.loadBitmapFont=wh,v.loadDDS=$a,v.loadImageBitmap=Da,v.loadJson=Ca,v.loadKTX=qa,v.loadSVG=cp,v.loadTextures=Ts,v.loadTxt=Ra,v.loadWebFont=Pa,v.parseDDS=Va,v.parseKTX=Ya,v.resolveCompressedTextureUrl=Ka,v.resolveTextureUrl=Na,v.settings=P,v.spritesheetAsset=gh,v.uniformParsers=Ie,v.utils=Io,Object.defineProperty(v,"__esModule",{value:!0}),v}({});
//# sourceMappingURL=pixi-legacy.min.js.map
