// hooks/useLivePlayers.js
import { useEffect, useState } from 'react';
import { livePlayerSocket } from '@/utils/socket';
import usePublicChatsStore from '@/store/usePublicChatStore';
import useAuthStore from '@/store/useAuthStore';
import usePlayerStore from '@/store/usePlayerStore';
import { useGetAllLivePlayersQuery } from '@/reactQuery/generalQueries';
import useActivePlayerStore from '@/store/useActivePlayeStore';
import {
  useActivePlayerMyFriends,
  useActivePlayerPublic,
} from '@/reactQuery/gamesQuery';
import { useQueryClient } from '@tanstack/react-query';

const useActivePlayers = () => {
  const queryClient = useQueryClient();
  const activeFriendsData = queryClient.getQueryData([
    'GET_FRIEND_LIST_QUERY',
    '',
    'AVAILABLE'
  ]);

  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    setMyFriends,
    setMyPlayerLoading,
    setPublicUsers,
    setPublicUsersLoading,
    setActiveTab,
  } = useActivePlayerStore((state) => state);

  const { data: myfriends, isLoading: myPlayerLoading, refetch: refetchMyFriends } =
    useActivePlayerMyFriends({enabled:isAuthenticated});

  const {
    data: publicUsers,
    isLoading: publicUsersLoading,
    refetch: refetchPublicUsers,
  } = useActivePlayerPublic({enabled:isAuthenticated});

  useEffect(() => {
    if (myfriends) {
      setMyFriends(myfriends);
      setMyPlayerLoading(myPlayerLoading);
      if (activeFriendsData && activeFriendsData?.data?.myfriends?.count == 0)
        setActiveTab(1);
    }
  }, [myfriends]);

  useEffect(() => {
    if (publicUsers) {
      setPublicUsers(publicUsers);
      setPublicUsersLoading(publicUsersLoading);
    }
  }, [publicUsers]);

  return {
    myPlayerLoading,
    publicUsersLoading,
    refetchPublicUsers,
    refetchMyFriends
  };
};

export default useActivePlayers;
