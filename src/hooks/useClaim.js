import { useState, useRef } from 'react';
import ClaimResult from '@/components/ChestAndCards/ClaimResult';
import { useClaimChestMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import { toast } from 'react-hot-toast';

export default function useClaim(updateChestCount) {
  const { isAuthenticated, userWallet } = useAuthStore((state) => state);
  const [claimResult, setClaimResult] = useState(null);
  const { openModal, closeModal } = useModalStore((state) => state);
  const remainingChestRef = useRef(1);
  const [timeDelayToClaim, setTimeDelayToClaim] = useState(1000);

  const { mutate: claimChest } = useClaimChestMutation({
    onSuccess: (response) => {
      if (!response.data.success) {
        toast.error(response.data.message);
        remainingChestRef.current = 0;
      } else {
        const {
          withdraw,
          acAmount,
          cardImage,
          remainingChest: remaining,
        } = response.data.data;

        remainingChestRef.current = remaining;

        if (updateChestCount) {
          updateChestCount(remaining);
        }

        if (cardImage) {
          setTimeDelayToClaim(5000);
        } else {
          setTimeDelayToClaim(1000);
        }

        if (withdraw === 'ac') {
          toast.success(`${acAmount} AC claimed successfully!`);
        } else if (withdraw === 'card' && cardImage) {
          setClaimResult(response.data.data);
          toast.success('Card Claimed Successfully!');
          openModal(<ClaimResult claimResult={response?.data?.data} />);
        }
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to claim';
      toast.error(message);
    },
  });

  const claimMultipleChests = async (coinsNeededToClaimAll) => {
    if (userWallet?.acCoin < coinsNeededToClaimAll) {
      toast.error('You do not have enough balace to open All Chests');
      return;
    }
    const results = [];
    while (remainingChestRef.current > 0) {
      await new Promise((resolve) => {
        setTimeout(() => {
          claimChest(undefined, {
            onSuccess: (response) => {
              results.push(response.data.data);
              resolve();
            },
            onError: (error) => {
              resolve();
            },
          });
        }, timeDelayToClaim);
      });
    }
    return results;
  };

  return {
    claimChest,
    claimMultipleChests,
    claimResult,
  };
}
