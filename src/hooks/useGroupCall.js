import GroupCallPopup from '@/components/Models/GroupCallPopup';
import {
  useDeclinedCall,
  useGenerateAgoraToken,
} from '@/reactQuery/chatWindowQuery';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken } from '@/utils/helper';
import { chatRoomSocket, voiceCallConnected } from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import { useQueryClient } from '@tanstack/react-query';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { useEffect, useRef, useState } from 'react';
import useConnectedPlay from './useConnecedPlay';
import { usePathname } from 'next/navigation';
import useCallStore from '@/store/useCallStore';
export const rtc = {
  client: AgoraRTC.createClient({ mode: 'rtc', codec: 'h264' }),
  localAudioTrack: null,
  remoteAudioTrack: null,
};

const useGroupCall = () => {
  const queryClient = useQueryClient();
  // AgoraRTC.setLogLevel(4);
  // const { setIsPrivateChatOpen, setUserId, isCallActive,
  //     setIsCallActive, userId, searchUserName, setSearchUserName } = usePrivateChatStore(
  //         (state) => state,
  //     );
  const {
    setIsPrivateChatOpen,
    setUserId,
    isCallActive,
    setIsCallActive,
    userId,
    searchUserName,
    setSearchUserName,
    groupCallDetails,
    setGroupCallDetails,
    callInitialized,
    setCallInitialized,
  } = useGroupChatStore((state) => state);
  const { toggleMuted, setToggleMuted } = useCallStore();

  const accessToken = getAccessToken();
  const {
    closeModal: closeCallModal,
    setIsMinimized,
    isMinimized,
    openModal: openCallModal,
  } = useCallModalStore((state) => state);
  const { setConnectedPlay } = useActiveGroupStore();
  const [isMuted, setIsMuted] = useState(true);
  const retryCounterRef = useRef(0);
  const pathname = usePathname();
  const tokenGenRetryLimit = 3; // Set retry limit
  useEffect(() => {
    chatRoomSocket.auth = { token: accessToken };
    chatRoomSocket.connect();
    voiceCallConnected.auth = { token: accessToken };
    voiceCallConnected.connect();
  }, []);
  const [channelName, setChannelName] = useState('');
  const { clearModals, openModal } = useModalStore((state) => state);
  const { setVoiceCall, voiceCall, updateGroupCallMembers } = useVoiceCallStore(
    (state) => state,
  );
  const { audio, pauseAudio } = useAudioPlayer();
  const { userDetails } = useAuthStore((state) => state);
  const { handleConnectedPlay } = useConnectedPlay();

  //Call decline on page referesh or close
  const declineCallOnPageRefreshOrClose = () => {
    const payload = {
      channelName: voiceCall?.channelName,
      callLogId: voiceCall?.callLogId,
      otherCallerId: voiceCall?.userId,
      isOneToOneCall: 'true',
      groupId: voiceCall?.groupId,
      notAttended: !isCallActive ? 'true' : undefined,
    };
    fetch(`${process.env.NEXT_PUBLIC_API_URL}/agora/decline-call`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Authorization: `Bearer ${accessToken}`,
        accesstoken: accessToken,
      },
      body: JSON.stringify(payload),
      keepalive: true,
    });
    if (pathname.startsWith('/game'))
      fetch(`${process.env.NEXT_PUBLIC_API_URL}/user/graceful-exit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Authorization: `Bearer ${accessToken}`,
          accesstoken: accessToken,
        },
        body: JSON.stringify({
          userId: userDetails?.userId,
        }),
        keepalive: true,
      });
  };

  useEffect(() => {
    const handleUnload = () => {
      if (isCallActive) {
        declineCallOnPageRefreshOrClose();
      }
    };
    window.addEventListener('beforeunload', handleUnload);
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, [isCallActive]);

  const token_genration = useGenerateAgoraToken({
    onSuccess: async (response, variables) => {
      if (!response?.data?.oldCallLog) {
        setVoiceCall({
          ...voiceCall,
          callLogId: response?.data?.callLogId,
          channelName: variables.channelName,
          groupId: variables.groupId,
          groupCallMembers: [
            {
              username: userDetails?.username,
              profileImage: userDetails?.profileImage,
              firstName: userDetails?.firstName,
              lastName: userDetails?.lastName,
            },
          ],
        });
        await rtc.client.join(
          process.env.NEXT_PUBLIC_AGORA_APP_ID,
          channelName,
          response.data.token,
          userDetails.uniqueId,
        );

        rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
        await rtc.client.publish(rtc.localAudioTrack);
        rtc.localAudioTrack.setEnabled(false);

        chatRoomSocket.emit('USER_GROUP_VOICE_CALL_CONNECTED', {
          isConnected: true,
          callLogId: response?.data?.callLogId,
          channelName,
          userId: userDetails.uniqueId,
          token: response.data.token,
        });
        setIsMuted(true);
        setToggleMuted(true);
        setCallInitialized({});
        openCallModal(<GroupCallPopup />);
        setIsCallActive(true);
        retryCounterRef.current = 0;
        setIsMinimized(true);
        handleConnectedPlay({
          groupId: variables.groupId,
          callLogId: response?.data?.callLogId,
        });
      } else {
        handleJoinCall({
          channelName: variables.channelName,
          callLogId: response?.data?.callLogId,
          groupId: variables.groupId,
          isConnectedPlay: false,
        });
      }
    },
    onError: (error, variables) => {
      console.error('Error generating token:', error, variables);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        // setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration.mutate({
          channelName,
          role: 'publisher',
          callReceiverId: userId,
          groupId: variables.groupId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const initiateCall = async ({ groupId, groupName }) => {
    setUserId(groupId);
    setCallInitialized({ groupId: groupId });
    setGroupCallDetails({
      groupName,
      groupId,
    });
    // let createChannel = [userDetails?.userId, userID]
    //     .sort((a, b) => a - b)
    //     .join('-')
    setChannelName(groupName);
    try {
      token_genration.mutate({
        channelName: groupName,
        role: 'publisher',
        groupId: groupId + '',
      });
    } catch (error) {
      console.error('Error initiating call:', error);
    }
  };
  const declineCall = useDeclinedCall({
    onSuccess: async () => {
      clearModals();
    },
    onError: (error) => {
      console.error('Error declining call:', error);
    },
  });
  const disconnectCall = async (data) => {
    try {
      pauseAudio();
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }

      const payload = {
        channelName,
        callLogId: voiceCall?.callLogId,
        isOneToOneCall: 'true',
      };

      if (!isCallActive) {
        payload.notAttended = 'true';
      }

      if (voiceCall?.callLogId) {
        await declineCall.mutateAsync(payload); // use mutateAsync if you're awaiting
      }

      rtc.localAudioTrack?.close();
      await rtc.client?.leave?.();

      setIsCallActive(false);
      // setVoiceCall(null);
    } catch (error) {
      console.error('Error disconnecting call:', error);
    } finally {
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
      setVoiceCall(null);

      console.log('Disconnected call cleanup done');
    }
  };

  const token_genration_accept_call = useGenerateAgoraToken({
    onSuccess: async (response) => {
      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        voiceCall.channelName,
        response.data.token,
        userDetails.uniqueId,
      );

      rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      await rtc.client.publish(rtc.localAudioTrack);
      rtc.localAudioTrack.setEnabled(false);
      // setCallStatus('Call Connected');
      setIsCallActive(true);
      pauseAudio();
      retryCounterRef.current = 0;
    },
    onError: (error) => {
      console.error('Error generating token:', error);
      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current += 1;
        // setRetryCount(retryCounterRef.current); // Optionally update state
        console.log(`Retry attempt: ${retryCounterRef.current}`);

        // Call the API again if retry count hasn't exceeded limit
        token_genration_accept_call.mutate({
          channelName: voiceCall.channelName,
          role: 'subscriber',
          callLogId: voiceCall.callLogId,
          answer: 'answer',
          groupId: voiceCall?.groupId,
        });
      } else {
        console.log('Max retry limit reached. No further retries.');
      }
    },
  });

  const handleAcceptCall = async () => {
    try {
      token_genration_accept_call.mutate({
        channelName: voiceCall.channelName,
        role: 'subscriber',
        callLogId: voiceCall.callLogId,
        answer: 'answer',
        groupId: voiceCall?.groupId,
      });
    } catch (error) {
      console.error('Error accepting call:', error);
    } finally {
      audio.pause();
      audio.currentTime = 0;
      pauseAudio();
    }
  };

  const handleDeclineCall = async () => {
    try {
      let payload = {
        channelName: voiceCall.channelName,
        callLogId: voiceCall.callLogId,
        otherCallerId: voiceCall.userId,
        isOneToOneCall: 'true',
        groupId: voiceCall?.groupId,
      };
      if (!isCallActive) {
        payload.notAttended = 'true';
      }
      if (voiceCall?.callLogId) declineCall.mutate(payload);

      rtc.localAudioTrack?.close();
      await rtc.client.leave();

      closeCallModal();
      setVoiceCall(null);
      setIsCallActive(false);
      pauseAudio();
      setConnectedPlay({});
    } catch (error) {
      console.error('Error declining call:', error);
    } finally {
      closeCallModal();

      setVoiceCall(null);
    }
  };

  const token_genration_join_call = useGenerateAgoraToken({
    onError: (error, variables) => {
      console.error('Token generation failed:', error);

      if (retryCounterRef.current < tokenGenRetryLimit) {
        retryCounterRef.current++;

        handleJoinCall({
          channelName: variables.channelName,
          role: 'subscriber',
          callLogId: variables.callLogId,
          answer: 'answer',
          groupId: variables.groupId,

          isConnectedPlay: variables.isConnectedPlay,
          groupCallMembers: variables.groupCallMembers,
        });
      }
    },
  });

  const handleJoinCall = async (props) => {
    const { channelName, groupId, callLogId, groupCallMembers } = props || {};

    setCallInitialized({ groupId });

    const attemptJoin = async () => {
      const response = await token_genration_join_call.mutateAsync({
        channelName,
        role: 'subscriber',
        callLogId,
        answer: 'answer',
        groupId,
      });

      await rtc.client.join(
        process.env.NEXT_PUBLIC_AGORA_APP_ID,
        channelName,
        response.data.token,
        userDetails.uniqueId,
      );
      console.log('rtccc', rtc);

      try {
        rtc.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
        rtc.localAudioTrack.setEnabled(false);
        await rtc.client.publish(rtc.localAudioTrack);
      } catch (err) {
        console.error('Microphone access failed:', err);

        // You can handle the error gracefully:
        // - Show a message to the user
        // - Fallback to audio-disabled mode
        // - Log for analytics/debugging
      }
      setIsCallActive(true);
      setIsMuted(true);
      setToggleMuted(true);
      setVoiceCall({
        channelName,
        role: 'subscriber',
        callLogId,
        userId: userDetails?.userId,
        username: userDetails?.username,
        profileImage: userDetails?.profileImage,
        groupId,
        groupCallMembers: groupCallMembers || response?.data?.allCallMembers,
      });

      openCallModal(<GroupCallPopup />);
      setIsMinimized(true);

      handleConnectedPlay({ groupId, callLogId });
    };

    let success = false;
    let lastError = null;

    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        await attemptJoin();
        success = true;
        break; // Exit loop on success
      } catch (error) {
        console.error(`Attempt ${attempt} failed:`, error);
        lastError = error;
      }
    }

    if (!success) {
      // Optional: show UI error or toast
      console.error('All 3 attempts failed to join the call.', lastError);
    }

    setCallInitialized({});
  };

  useEffect(() => {
    // async function deleteSession() {
    //   await rtc.client.leave();
    // }
    // deleteSession();

    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', () => {
      if (rtc.remoteAudioTrack) {
        rtc.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    });

    return () => {
      rtc.client.removeAllListeners();
      // handleDeclineCall()
    };
  }, []);
  const requestMicrophoneAccess = async () => {
    try {
      const micTrack = await AgoraRTC.createMicrophoneAudioTrack();
      console.log('Microphone access granted');
      return micTrack;
    } catch (err) {
      console.error('Microphone access failed:', err);

      const originalError = err?.message || err?.originalError?.name || '';

      if (
        originalError.includes('NotAllowedError') ||
        originalError.includes('PERMISSION_DENIED')
      ) {
        alert(
          "Microphone access is blocked. Please allow it from your browser's settings.",
        );
      } else if (
        originalError.includes('NotFoundError') ||
        originalError.includes('NOT_FOUND')
      ) {
        alert('No microphone device found.');
      } else {
        alert('An unexpected error occurred while accessing the microphone.');
      }

      return null;
    }
  };

  const toggleMicrophone = async () => {
    console.log(
      '🚀 ~ toggleMicrophone ~ rtc.localAudioTrack:',
      rtc.localAudioTrack,
    );

    const enableMic = async () => {
      try {
        await rtc.localAudioTrack.setEnabled(true);
      } catch (err) {
        console.warn('Failed to re-enable mic, requesting access again...');
        const micTrack = await requestMicrophoneAccess();
        if (!micTrack) return;

        rtc.localAudioTrack = micTrack;
        await rtc.client.publish(micTrack);
      }
    };

    const disableMic = async () => {
      await rtc.localAudioTrack.setEnabled(false);
    };

    const updateMicState = (muted) => {
      setIsMuted(muted);
      setToggleMuted(muted);
    };

    if (rtc.localAudioTrack) {
      if (isMuted) {
        await enableMic();
        updateMicState(false);
      } else {
        await disableMic();
        updateMicState(true);
      }
    } else {
      const micTrack = await requestMicrophoneAccess();
      if (!micTrack) return;

      rtc.localAudioTrack = micTrack;
      await rtc.client.publish(micTrack);
      updateMicState(false);
    }
  };

  return {
    initiateCall,
    disconnectCall,
    isCallActive,
    handleAcceptCall,
    handleDeclineCall,
    handleJoinCall,
    isMuted,
    setIsMuted,
    callInitialized,
    setCallInitialized,
    toggleMicrophone,
  };
};

export default useGroupCall;
