import { useCartQuery } from '@/reactQuery/inventoryQuery';
import useAuthStore from '@/store/useAuthStore';
import { useDebounce } from 'use-debounce';

export default function useCart() {
  const { isAuthenticated } = useAuthStore((state) => state);

  const {
    data: cartItems,
    isLoading: cartLoading,
    refetch,
  } = useCartQuery({
    enabled: isAuthenticated,
  });

  return {
    refetch,
    cartItems,
    cartLoading,
  };
}
