import { useNoticeDetailsQuery, useNotificationsQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import useNotificationStore from '@/store/useNoticeStore';
import useNoticeStore from '@/store/useNoticeStore';
import { walletSocket } from '@/utils/socket';
import React, { useEffect } from 'react';

function useNotifications() {
  const { setNotifications, addNotice, showNotificationPopup,page,setPage } = useNotificationStore();
  const {isAuthenticated}=useAuthStore()
//   const { data: noticeDetails, refetch } = useNoticeDetailsQuery({
//     enabled: true,
//   });
  const { data: notifications, refetch, isLoading: notificationLoading } = useNotificationsQuery({
    enabled: isAuthenticated,
    page
  });
/*   useEffect(() => {
    if (true) {
      walletSocket.connect();
      walletSocket.on('NOTIFICATION', (data) =>
        addNotice(data?.data?.checkNoticeExist),
      );
    }

    return () => {
      walletSocket.off('NOTIFICATION');
    };
  }, []);
 */
  useEffect(() => {
    if(showNotificationPopup)
    refetch();
  }, [showNotificationPopup,page]);

  useEffect(() => {
    if(notifications){
      setNotifications(notifications);
    }
    else{
      setNotifications();
    }
  }, [notifications]);

  return {
    refetch,
    notificationLoading
  }
}

export default useNotifications;
