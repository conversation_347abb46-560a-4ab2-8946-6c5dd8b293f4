import { useClaimTaskMutation } from '@/reactQuery/generalQueries';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

const useClaimUserTasks = () => {
  const queryClient = useQueryClient();
  const { mutate: claimTask, isLoading: isUserTasksLoading } =
    useClaimTaskMutation({
      onSuccess: (response) => {
        toast.success('Claimed Successfully!');
        queryClient.invalidateQueries(['userTasks']);
        queryClient.invalidateQueries(['userClaimedTasks']);
      },
      onError: (error) => {
        console.log('handleClaimTask', error);
        const message =
          error.response?.data?.errors?.[0]?.description || 'Unable to add';
        toast.error(message);
      },
    });

  return { claimTask, isUserTasksLoading };
};

export default useClaimUserTasks;
