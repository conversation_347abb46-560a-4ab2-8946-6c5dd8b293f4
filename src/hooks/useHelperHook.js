'use client';

import { useLogOutMutation } from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { destroyCookie } from 'nookies';
import { toast } from 'react-hot-toast';

export default function useHelperHook() {
  const user = useAuthStore((state) => state);
  const router = useRouter();
  const queryClient = useQueryClient();
  const mutation = useLogOutMutation({
    onSuccess: () => {
      user.logout();
      localStorage.clear();
      destroyCookie(null, 'accessToken');
      queryClient.clear(); 
      toast.success('Logout successfully')
    },
    onError: (error) => {
      toast.error('Logout failed')

    },
  });

  const logout = () => {
    mutation.mutate();
  };

  const clearUserAuth = () => {
    user.logout();
    destroyCookie(null, 'accessToken');
    localStorage.clear();
  };

  return {
    isLoading: mutation?.isPending,
    logout,
    clearUserAuth,
  };
}
