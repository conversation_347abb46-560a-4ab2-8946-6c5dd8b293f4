'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';
import useAuthStore from '@/store/useAuthStore';
import { useDiscordLoginMutation, useFacebookLoginMutation, useGoogleLoginMutation, useSignUpMutation, useTwitchLoginMutation } from '@/reactQuery/authQuery';
import useModalStore from '@/store/useModalStore';
import { useRouter } from 'next/navigation';
const useSignUp = () => {
  const [error, setError] = useState(null);
  const setAuthState = useAuthStore((state) => state.setIsAuthenticated);
  const setUserDetails = useAuthStore((state) => state.setUserDetails);
  const { closeModal, openModal,clearModals } = useModalStore((state) => state);
  const router = useRouter();

  const mutation = useSignUpMutation({
    onSuccess: (response) => {
      setUserDetails(response?.data?.user);
      setAuthState(true);
      router.push('/')
      localStorage.setItem('isAuthenticated', true);
      clearModals();
      toast.success('Registered Successfully!');
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to sign up';
      setError(message);
      toast.error(message);
    },
  });

    const googleMutation = useGoogleLoginMutation({
      onSuccess: (response) => {
        console.log(response, "::::::::::response for google login");
        if(response?.data?.success){
          setUserDetails(response?.data?.user);
          setAuthState(true);
          router.push('/')
          clearModals();
          localStorage.setItem('isAuthenticated', true);
          toast.success('Registered Successfully!');
        }
      },
      onError: (error) => {
        console.log(error, " :::google login");
        const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
        toast.error(message);
      },
    });

    const facebookMutation = useFacebookLoginMutation({
      onSuccess: (response) => {
        console.log(response, ":::::::::facebook response");
        if(response?.data?.success){
          setUserDetails(response?.data?.user);
          setAuthState(true);
          router.push('/')
          localStorage.setItem('isAuthenticated', true);
          toast.success('Registered Successfully!');
          clearModals();
          closeModal();
        }
      },
      onError: (error) => {
        console.log(error, " :::google login");
        const message =
        error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
        toast.error(message);
      }
    })

      const discordMutation =useDiscordLoginMutation({
        onSuccess:(response)=>{
          console.log(response, ":::::::::discord response");
          if(response?.data?.success){
            setUserDetails(response?.data?.user);
            setAuthState(true);
            router.push('/')
            localStorage.setItem('isAuthenticated', true);
            toast.success('Registered Successfully!');
            clearModals();
          }
        },
        onError:(error) =>{
          console.log(error, "::::::::::::error");
          const message =
          error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
          toast.error(message);
        }
      })

      const twitchMutation = useTwitchLoginMutation({
          onSuccess:(response)=>{
            console.log(response, ":::::::::twitch response");
            if(response?.data?.success){
              setUserDetails(response?.data?.user);
              setAuthState(true);
              router.push('/')
              localStorage.setItem('isAuthenticated', true);
              clearModals();
              toast.success('Registered Successfully!');
            }
          },
          onError:(error) =>{
            console.log(error, "::::::::::::error");
            const message =
            error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
            toast.error(message);
          }
        })

  const signUp = (authDetails) => {
    mutation.mutate({
      ...authDetails,
      // isTermsAccepted: authDetails.acceptTerms,
      browser: 'Chrome',
      platform: 'Windows',
    });
  };

  return { signUp, error, isLoading: mutation.isPending , googleMutation, facebookMutation,discordMutation,twitchMutation };
};

export default useSignUp;
