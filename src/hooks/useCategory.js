import { useGetCategoryGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

export default function useCategory(params) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: categoryGames, isLoading: iscategoryGamesLoading } =
    useGetCategoryGamesQuery({
      enabled: isAuthenticated,
      params,
    });

  return {
    categoryGames,
    iscategoryGamesLoading,
  };
}
