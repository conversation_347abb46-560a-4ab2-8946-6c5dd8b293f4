import { useTipsTransactionsMutation } from '@/reactQuery/generalQueries';
import { useEffect } from 'react';
import { toast } from 'react-hot-toast';

export const useTipsTransactions = ({ activeTab }) => {
  const {
    data: tipsTransactions,
    isLoading,
    isError,
    error,
    refetch,
  } = useTipsTransactionsMutation({
    onSuccess: (response) => {
      console.log('Tip action performed successfully:', response);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Failed';
      toast.error(message);
    },
  });

  return {
    tipsTransactions,
    isLoading,
    isError,
    error,
    refetch,
  };
};
