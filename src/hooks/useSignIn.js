'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';
import useAuthStore from '@/store/useAuthStore';
import { useDiscordLoginMutation, useFacebookLoginMutation, useGoogleLoginMutation, useSignInMutation, useTwitchLoginMutation } from '@/reactQuery/authQuery';
import { useRouter } from 'next/navigation';
import useModalStore from '@/store/useModalStore';
const useSignIn = () => {
  const [error, setError] = useState(null);
  const router = useRouter();
  const { setIsAuthenticated, setUserDetails } = useAuthStore((state) => state);
  const { clearModals } = useModalStore((state) => state);
      const mutation = useSignInMutation({
        onSuccess: (response) => {
          console.log(response, ":::::::::::::response")
          if (!response?.data?.authEnable) {
            setIsAuthenticated(true);
            localStorage.setItem('isAuthenticated', true);
            setUserDetails(response?.data?.user);
            // toast.success('Signed In Successfully!');
            clearModals()
            if (response?.data?.user?.chestReceived) {
              toast.success('Congratulations!! You have recieved Chest.');
            }
          } else {
            setUserDetails(response?.data);
          }
        },
        onError: (error) => {
          console.log(error, "::::::erorr")
          const message =
            error.response?.data?.errors?.[0]?.description || 'Failed to sign in';
          setError(message);
          toast.error(message);
        },
      });

      const googleMutation = useGoogleLoginMutation({
        onSuccess: (response) => {
          console.log(response, "::::::::::response for google login");
          if(response?.data?.success){
            setUserDetails(response?.data?.user);
            setIsAuthenticated(true);
            router.push('/')
            localStorage.setItem('isAuthenticated', true);
            toast.success('Registered Successfully!');
            clearModals()
          }
        },
        onError: (error) => {
          console.log(error, " :::google login");
          const message =
          error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
          toast.error(message);
        },
      });
  
      const facebookMutation = useFacebookLoginMutation({
        onSuccess: (response) => {
          console.log(response, ":::::::::facebook response");
          if(response?.data?.success){
            setUserDetails(response?.data?.user);
            setIsAuthenticated(true);
            router.push('/')
            localStorage.setItem('isAuthenticated', true);
            toast.success('Registered Successfully!');
            clearModals();
            closeModal();
          }
        },
        onError: (error) => {
          console.log(error, " :::google login");
          const message =
          error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
          toast.error(message);
        }
      })
  
        const discordMutation =useDiscordLoginMutation({
          onSuccess:(response)=>{
            console.log(response, ":::::::::discord response");
            if(response?.data?.success){
              setUserDetails(response?.data?.user);
              setIsAuthenticated(true);
              router.push('/')
              clearModals()
              localStorage.setItem('isAuthenticated', true);
              toast.success('Registered Successfully!');
            }
          },
          onError:(error) =>{
            console.log(error, "::::::::::::error");
            const message =
            error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
            toast.error(message);
          }
        })
  
        const twitchMutation = useTwitchLoginMutation({
            onSuccess:(response)=>{
              console.log(response, ":::::::::twitch response");
              if(response?.data?.success){
                setUserDetails(response?.data?.user);
                setIsAuthenticated(true);
                router.push('/');
                clearModals()
                localStorage.setItem('isAuthenticated', true);
                toast.success('Registered Successfully!');
              }
            },
            onError:(error) =>{
              console.log(error, "::::::::::::error");
              const message =
              error.response?.data?.errors?.[0]?.description || 'Failed to register with Google.';
              toast.error(message);
            }
          })

  const signIn = (userName, password) => {
    mutation.mutate({ userName, password: btoa(password) });
  };

  return { signIn, error, isLoading: mutation.isPending, googleMutation, facebookMutation,discordMutation,twitchMutation  };
};

export default useSignIn;
