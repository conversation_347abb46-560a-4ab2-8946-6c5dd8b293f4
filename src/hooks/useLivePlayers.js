// hooks/useLivePlayers.js
import { useEffect, useState } from 'react';
import { livePlayerSocket } from '@/utils/socket';
import usePublicChatsStore from '@/store/usePublicChatStore';
import useAuthStore from '@/store/useAuthStore';
import usePlayerStore from '@/store/usePlayerStore';
import { useGetAllLivePlayersQuery } from '@/reactQuery/generalQueries';

const useLivePlayers = () => {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { setLivePlayers,setPlayerLoading } = usePlayerStore((state) => state);

  const [onlinePlayers, setOnlinePlayers] = useState([]);
  const { setLivePlayersCount } = usePublicChatsStore((state) => state);
  const { data: livePlayers ,isLoading:isPlayerLoading} = useGetAllLivePlayersQuery({
    enabled: isAuthenticated,
  });
  useEffect(() => {
    livePlayerSocket.connect();

    livePlayerSocket.on('LIVE_PLAYERS', (players) => {
      setOnlinePlayers(players.data.livePlayers);
      setLivePlayers(players.data.livePlayers);
      if (players?.data?.livePlayersCount) {
        setLivePlayersCount(players?.data?.livePlayersCount);
      }
    });
  }, []);

  useEffect(() => {
    if (livePlayers) {
      setLivePlayers(livePlayers);
    }
  }, [livePlayers]);
  useEffect(() => {
  setPlayerLoading(isPlayerLoading)
  }, [isPlayerLoading]);

  return { onlinePlayers };
};

export default useLivePlayers;
