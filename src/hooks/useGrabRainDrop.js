// sendRainDrop

import { useGrabRainDropMutation } from '@/reactQuery/generalQueries';
import { toast } from 'react-hot-toast';

export const useGrabRainDrop = () => {
  const {
    mutate: grabRainDrop,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  } = useGrabRainDropMutation({
    onSuccess: (response) => {
      toast.success('Rain Drop Grabbed');
      console.log('Rain action performed successfully:', response);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Unable to Grab Rain Drop ';
      toast.error(message);
    },
  });

  return {
    grabRainDrop,
    isLoading,
    isError,
    isSuccess,
    data,
    error,
  };
};
