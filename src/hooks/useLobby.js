// import {
//   useGetLobbyGamesQuery,
//   useGetSubCategoryGamesQuery,
// } from '@/reactQuery/gamesQuery';
// import useAuthStore from '@/store/useAuthStore';

// export default function useLobby() {
//   const { isAuthenticated } = useAuthStore((state) => state);
//   const { data: lobbyGames, isLoading: isLobbyGamesLoading } =
//     useGetSubCategoryGamesQuery({
//       enabled: isAuthenticated,
//     });

//   return {
//     lobbyGames,
//     isLobbyGamesLoading,
//   };
// }

import { useEffect } from 'react';
import {
  useGetLobbyGamesQuery,
  useGetSubCategoryGamesQuery,
} from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useGameStore from '@/store/useGameStore';

export default function useLobby(searchTerm = '') {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: games, isLoading: isLobbyGamesLoading } =
    useGetSubCategoryGamesQuery({
      // enabled: isAuthenticated,
      searchTerm,
    });
  const { setLobbyGames, setSearchActive } = useGameStore((state) => state);

  useEffect(() => {
    if (games) {
      setLobbyGames(games);
      setSearchActive(false);
    }
  }, [games, setLobbyGames, setSearchActive]);

  return {
    isLobbyGamesLoading,
  };
}
