'use client';

import { toast } from 'react-hot-toast';
import { useState } from 'react';
import { useAddToCartMutation } from '@/reactQuery/inventoryQuery';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';

const useAddToCart = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  const { mutate: addInventoryToCart } = useAddToCartMutation({
    onSuccess: (response) => {
      toast.success('Added to Cart Successfully!');
      queryClient.invalidateQueries(['GET_CART']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to add';
      toast.error(message);
    },
  });

  const { mutate: addToCartAndRedirect } = useAddToCartMutation({
    onSuccess: (response) => {
      toast.success('Added to Cart Successfully!');
      queryClient.invalidateQueries(['GET_CART']);
      router.push('/inventory/checkout', {
        orderSessionId: response?.data?.data,
      });
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to add';
      toast.error(message);
    },
  });

  return {
    addInventoryToCart,
    addToCartAndRedirect,
  };
};

export default useAddToCart;
