import { useIgnoredUsersQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';

export default function useIgnoredUsers() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    data: ignoredUsers,
    isLoading: ignoredUsersLoading,
    refetch,
  } = useIgnoredUsersQuery({
    enabled: isAuthenticated,
  });

  return {
    refetch,
    ignoredUsers,
    ignoredUsersLoading,
  };
}
