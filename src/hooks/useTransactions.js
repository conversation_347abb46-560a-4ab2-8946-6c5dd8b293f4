import {
  useGetRainTransactions,
  useGetTransactions,
} from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';

export const usePlayerTransactions = (
  props
) => {
  const {
    data: transactions,
    isLoading,
    isError,
    error,
    refetch
  } = useGetTransactions({
    ...props
  });

  return {
    transactions,
    isLoading,
    isError,
    error,
    refetch
  };
};
