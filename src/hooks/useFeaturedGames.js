import { useFeaturedGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

export default function useFeaturedGames() {
  const { isAuthenticated } = useAuthStore((state) => state);

  const { data: featuredGames, isLoading: isFeaturedGamesLoading } =
    useFeaturedGamesQuery({
      enabled: isAuthenticated,
    });

  return {
    featuredGames,
    isFeaturedGamesLoading,
  };
}
