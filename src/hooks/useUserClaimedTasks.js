import { useUserClaimedTasksQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';

const useUserClaimedTasks = () => {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: userClaimedTasks, isLoading: isUserCalimedTasksLoading } =
    useUserClaimedTasksQuery({
      enabled: isAuthenticated,
    });

  return { userClaimedTasks, isUserCalimedTasksLoading };
};

export default useUserClaimedTasks;
