import { useEffect } from 'react';
import { liveBetsSocket, myBetsSocket } from '@/utils/socket';
import useAuthStore from '@/store/useAuthStore';
import { useAllBetsQuery } from '@/reactQuery/betsQuery';
import useBetStore from '@/store/useBetStore';

const useBets = ({ myBets,limit }) => {
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const { setAllBets, setMyBets, addLiveBet, addMyBet } = useBetStore(
    (state) => ({
      setAllBets: state.setAllBets,
      setMyBets: state.setMyBets,
      addLiveBet: state.addLiveBet,
      addMyBet: state.addMyBet,
    }),
  );

  const {
    data: betsData,
    isLoading: isBetsLoading,
    refetch: refetchAllBets,
  } = useAllBetsQuery({
    enabled: isAuthenticated,
    myBets,
    limit
  });

  useEffect(() => {
    if (myBets) {
      if (betsData) setMyBets(betsData?.allBets);
    } else {
      if (betsData) setAllBets(betsData?.allBets);
    }
  }, [betsData, setAllBets, setMyBets, myBets]);

  useEffect(() => {
    myBetsSocket.connect();
    liveBetsSocket.connect();

    myBetsSocket.on('MY_BETS', (bet) => {
      if (userDetails?.userId == bet.data?.userId) {
        addMyBet(bet.data);
      }
    });

    liveBetsSocket.on('LIVE_BETS', (bet) => {
      addLiveBet(bet.data);
    });
  }, [myBets, addLiveBet, addMyBet]);

  return {
    betsData,
    isBetsLoading,
    refetchAllBets,
  };
};

export default useBets;
