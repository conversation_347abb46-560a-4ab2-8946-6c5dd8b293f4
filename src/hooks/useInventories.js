import { useInventoriesQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { useDebounce } from 'use-debounce';

export default function useInventories(searchQuery) {
  const { isAuthenticated } = useAuthStore((state) => state);
  const [debouncedSearch] = useDebounce(searchQuery, 500);

  const {
    data: inventories,
    isLoading: inventoriesLoading,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInventoriesQuery({
    search: debouncedSearch,
    enabled: isAuthenticated,
  });

  return {
    refetch,
    inventories,
    inventoriesLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
}
