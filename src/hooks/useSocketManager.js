import CallPopup from '@/components/Models/CallPopup';
import GroupCallPopup from '@/components/Models/GroupCallPopup';
import { rtc as privateCallRTC } from '@/hooks/usePrivateCall';
import useAuthStore from '@/store/useAuthStore';
import useCallModalStore from '@/store/useCallModalStore';
import useCallStore from '@/store/useCallStore';
import useGreenBonusStore from '@/store/useGreenBonusStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { getAccessToken } from '@/utils/helper';
import {
  chatRoomSocket,
  livePlayerSocket,
  playerActivitySocket,
  walletSocket,
} from '@/utils/socket';
import useAudioPlayer from '@/utils/useAudioPlayer';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import toast from 'react-hot-toast';
import useActiveGroups from './useActiveGroup';
import useActivePlayers from './useActivePlayer';
import useConnectedPlay from './useConnecedPlay';
import useHelperHook from './useHelperHook';

export const useSocketManager = () => {
  const accessToken = getAccessToken();
  const { setCallStartTime, setCallDuration, setToggleMuted } = useCallStore();
  const { audio, playAudio } = useAudioPlayer();
  const {
    closeModal: closeCallModal,
    openModal: openCallModal,
    setIsMinimized,
  } = useCallModalStore((state) => state);
  const { handleConnectedPlay } = useConnectedPlay();
  const { setVoiceCall, voiceCall, updateGroupCallMembers } = useVoiceCallStore(
    (state) => state,
  );
  const {
    coin,
    setCoin,
    isAuthenticated,
    setUserDetails,
    setUserWallet,
    userWallet,
    userDetails,
  } = useAuthStore((state) => state);
  const router = useRouter();
  const { setIsCallActive } = usePrivateChatStore((state) => state);
  const { refetchPublicUsers, refetchMyFriends } = useActivePlayers();
  const { refetchPublicGroups, refetchMyGroups } = useActiveGroups();
  const { setGreenBonusData } = useGreenBonusStore((state) => state);
  const { clearUserAuth } = useHelperHook();
  const { clearModals } = useModalStore((state) => state);

  const onCoinUpdate = (data) => {
    setUserWallet(data?.data);
  };
  const onChatRoomCall = (data) => {
    const { isCallActive } = useGroupChatStore.getState();
    const { isCallActive: isPrivateCallActive } =
      usePrivateChatStore.getState();

    if (!isCallActive && !isPrivateCallActive) {
      setIsMinimized(false);
      // openModal(<CallPopup />)
      setCallStartTime(null), setCallDuration(0);
      setToggleMuted(false);
      playAudio();
      openCallModal(<CallPopup />);
      setVoiceCall({
        channelName: data?.data?.channelName,
        role: data?.data?.role,
        callLogId: data?.data?.callLogId,
        userId: data?.data?.userId,
        username: data?.data?.username,
        profileImage: data?.data?.profileImage,
      });
      return;
    } else {
      toast(`${data?.data?.username} is Calling you`, {
        icon: '📞', // or 📞 or 🟢
        duration: 3000,
        style: {
          background: 'yellow', // info-blue
          color: 'black',
          fontWeight: '500',
        },
      });
      return;
    }
  };

  const onGroupChatCall = (data) => {
    // const isPrivateCallActive = usePrivateChatStore.getState().isCallActive;
    // const { isCallActive } = useGroupChatStore.getState();
    // if (isCallActive || isPrivateCallActive) {
    //   toast(` Group Call Started: ${data?.data?.groupName}`, {
    //     icon: '📞', // or 📞 or 🟢
    //     duration: 3000,
    //     style: {
    //       background: 'yellow', // info-blue
    //       color: 'black',
    //       fontWeight: '500',
    //     },
    //   });
    //   return;
    // }
    // setToggleMuted(false);
    // const currentUserId = userDetails?.userMeta?.userId ?? userDetails?.userId;
    // if (currentUserId != data?.data?.userId) {
    //   playAudio();
    // }
    // setVoiceCall({
    //   channelName: data?.data?.channelName,
    //   role: data?.data?.role,
    //   callLogId: data?.data?.callLogId,
    //   userId: data?.data?.userId,
    //   username: data?.data?.username,
    //   profileImage: data?.data?.profileImage,
    //   groupId: data?.data?.groupId,
    //   groupCallMembers:
    //     data?.data?.userId == userDetails?.userId
    //       ? [
    //           {
    //             username: userDetails?.username,
    //             profileImage: userDetails?.profileImage,
    //             firstName: userDetails?.firstName,
    //             lastName: userDetails?.lastName,
    //           },
    //         ]
    //       : data?.data?.members,
    // });
    // openCallModal(<GroupCallPopup />);
    // setIsMinimized(false);
  };

  const onGroupCallMembers = (data) => {
    const voiceCall = useVoiceCallStore.getState().voiceCall;
    console.log('voice call', voiceCall, data?.data);
    if (voiceCall != null) {
      updateGroupCallMembers([...data?.data]);
    }
  };
  const onDeclineCall = async () => {
    const isCallActive = usePrivateChatStore.getState().isCallActive;
    const { isCallActive: isGroupCallActive } = useGroupChatStore.getState();
    if (!isGroupCallActive) {
      try {
        audio.pause();
        audio.currentTime = 0;

        if (!isCallActive) {
          closeCallModal();
          return;
        }

        privateCallRTC.localAudioTrack?.close();
        await privateCallRTC.client.leave();
      } catch (error) {
        console.error('Error disconnecting call:', error);
      } finally {
        usePrivateChatStore.getState().setIsCallActive(false);
        closeCallModal();
      }
    }
  };

  const onNotAttendedVoiceCall = async () => {
    const isCallActive = usePrivateChatStore.getState().isCallActive;
    const { isCallActive: isGroupCallActive } = useGroupChatStore.getState();
    if (!isGroupCallActive) {
      try {
        if (!isCallActive) {
          closeCallModal();
          audio.pause();
          audio.currentTime = 0;
          return;
        }
        privateCallRTC.localAudioTrack?.close();
        await privateCallRTC.client.leave();

        setVoiceCall(null);
        setIsCallActive(false);
      } catch (error) {
        console.log(error, 'Error in not attended voice call');
      } finally {
        audio.pause();
        audio.currentTime = 0;
        // audio.pause();
        // audio.currentTime = 0;
      }
    }
  };

  const onHandleActivePlayer = (data) => {
    refetchPublicUsers(), refetchMyFriends();
  };
  const onHandleActiveGroup = (data) => {
    const voiceCall = useVoiceCallStore.getState().voiceCall;
    const { isCallActive: isGroupCallActive } = useGroupChatStore.getState();

    // if (data?.data?.games?.length != 0) {
    refetchPublicGroups();
    refetchMyGroups();
    // }

    if (isGroupCallActive && voiceCall?.groupId && voiceCall?.callLogId) {
      handleConnectedPlay({
        groupId: voiceCall?.groupId,
        callLogId: voiceCall?.callLogId,
      });
    }
  };

  const onRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: true });
    toast.error('You are restricted, Please contact administrator');
    router.push('/');
  };
  const onUnRestrictUser = () => {
    setUserDetails({ ...userDetails, isRestrict: false });
    toast.error('You are now unrestricted');
    router.push('/');
  };

  const onLogout = () => {
    // logout();
    window.dispatchEvent(new Event('logout'));
    // window.location.reload();
    // router.push('/');
    clearUserAuth();
    localStorage.clear();
    clearModals();
    router.push('/');
  };

  const onRemainingthresold = (data) => {
    setGreenBonusData(data.data);
  };

  useEffect(() => {
    if (isAuthenticated) {
      // Set socket authentication
      walletSocket.auth = { token: accessToken };
      chatRoomSocket.auth = { token: accessToken };
      playerActivitySocket.auth = { token: accessToken };

      // Connect sockets
      walletSocket.connect();
      livePlayerSocket.connect();
      chatRoomSocket.connect();
      playerActivitySocket.connect();

      const handleRestrictUser = (data) => onRestrictUser(data);
      const handleRemainingThreshold = (data) => onRemainingthresold(data);
      const handleUnrestrictUser = (data) => onUnRestrictUser(data);
      const handleCoinUpdate = (data) => onCoinUpdate(data);
      const handleLogout = (data) => onLogout(data);
      const handleChatRoomCall = (data) => onChatRoomCall(data);
      const handleGroupChatCall = (data) => onGroupChatCall(data);
      const handleDeclinePersonalCall = (data) => onDeclineCall(data);
      const handleNotAttendedVoiceCall = (data) => onNotAttendedVoiceCall(data);
      const handleGroupGroupMembers = (data) => onGroupCallMembers(data);
      const handleActivePlayer = (data) => onHandleActivePlayer(data);
      const handleActiveGroup = (data) => onHandleActiveGroup(data);

      walletSocket.on('USER_RESTRICT', handleRestrictUser);
      walletSocket.on('REMAINING_THRESHOLD', handleRemainingThreshold);
      walletSocket.on('CLAIM_GREEN_BONUS', () => {}); // No handler was passed before
      walletSocket.on('USER_UNRESTRICT', handleUnrestrictUser);
      walletSocket.on('USER_WALLET_BALANCE', handleCoinUpdate);
      walletSocket.on('USER_BAN', handleLogout);
      walletSocket.on('DUPLICATE_LOGIN', handleLogout);

      chatRoomSocket.on('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
      chatRoomSocket.on('GROUP_VOICE_CHAT_CHANNEL', handleGroupChatCall);
      chatRoomSocket.on(
        'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
        handleDeclinePersonalCall,
      );
      chatRoomSocket.on('NOT_ATTENDED_VOICE_CALL', handleNotAttendedVoiceCall);
      chatRoomSocket.on('GROUP_VOICE_CHAT_MEMBER', handleGroupGroupMembers);

      playerActivitySocket.on('MY_FRIENDS_PLAYER_ACTIVITY', handleActivePlayer);
      playerActivitySocket.on('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);

      // ✅ Cleanup
      return () => {
        walletSocket.off('USER_RESTRICT', handleRestrictUser);
        walletSocket.off('REMAINING_THRESHOLD', handleRemainingThreshold);
        walletSocket.off('CLAIM_GREEN_BONUS'); // no handler to remove
        walletSocket.off('USER_UNRESTRICT', handleUnrestrictUser);
        walletSocket.off('USER_WALLET_BALANCE', handleCoinUpdate);
        walletSocket.off('USER_BAN', handleLogout);
        walletSocket.off('DUPLICATE_LOGIN', handleLogout);

        chatRoomSocket.off('PERSONAL_VOICE_CHAT_CHANNEL', handleChatRoomCall);
        chatRoomSocket.off('GROUP_VOICE_CHAT_CHANNEL', handleGroupChatCall);
        chatRoomSocket.off(
          'DECLINE_PERSONAL_VOICE_CHAT_CHANNEL',
          handleDeclinePersonalCall,
        );
        chatRoomSocket.off(
          'NOT_ATTENDED_VOICE_CALL',
          handleNotAttendedVoiceCall,
        );
        chatRoomSocket.off('GROUP_VOICE_CHAT_MEMBER', handleGroupGroupMembers);

        playerActivitySocket.off(
          'MY_FRIENDS_PLAYER_ACTIVITY',
          handleActivePlayer,
        );
        playerActivitySocket.off('PUBLIC_PLAYER_ACTIVITY', handleActiveGroup);

        // Disconnect all
        walletSocket.disconnect();
        livePlayerSocket.disconnect();
        chatRoomSocket.disconnect();
        playerActivitySocket.disconnect();
      };
    }
  }, [isAuthenticated, accessToken]);
};
