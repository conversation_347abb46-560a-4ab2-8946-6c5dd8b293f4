import { useSendRainDropMutation } from '@/reactQuery/generalQueries';
import { toast } from 'react-hot-toast';

export const useRainDrop = () => {
  const {
    mutate: sendRainDrop,
    isPending,
    isError,
    isSuccess,
    data,
    error,
  } = useSendRainDropMutation({
    onSuccess: (response) => {
      toast.success('Rain sent!');
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to send';
      toast.error(message);
    },
  });

  return {
    sendRainDrop,
    isPending,
    isError,
    isSuccess,
    data,
    error,
  };
};
