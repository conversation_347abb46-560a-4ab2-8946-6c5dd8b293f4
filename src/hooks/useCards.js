'use client';

import {
  useClaimChestMutation,
  useGetCardsQuery,
} from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';

export default function useCards() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: cardsData, isLoading: isCardsLoading } = useGetCardsQuery({
    enabled: isAuthenticated,
  });

  return {
    cardsData,
    isCardsLoading,
  };
}
