import { useGetVipTierRulesQuery } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';

export const useVipTierRules = () => {
  const { userDetails } = useAuthStore((state) => state);
  const {
    isLoading,
    isError,
    isSuccess,
    data: vipTierRules,
    error,
  } = useGetVipTierRulesQuery({ enabled: true ,userId: userDetails?.userId });
  return {
    isLoading,
    isError,
    isSuccess,
    vipTiers:vipTierRules?.vipTiers,
    userWagerAmount:vipTierRules?.userWagerAmount,
    vipTierRules,
    error,
  };
};
