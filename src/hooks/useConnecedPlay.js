import useActiveGroupStore from '@/store/useActiveGroupStore';
import { getConnectedPlay } from '@/utils/apiCalls';
import { useQueryClient } from '@tanstack/react-query';

const useConnectedPlay = () => {
  const queryClient = useQueryClient();
  const { setConnectedPlay } = useActiveGroupStore();

  const handleConnectedPlay = async (newParams) => {
    const result = await queryClient.fetchQuery({
      queryKey: ['GET_CONNECTED_PLAY', newParams, Date.now()],
      queryFn: () => getConnectedPlay({ enabled: true, ...newParams }),
      select: (data) => data?.data?.response,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      staleTime: 0,
    });
    setConnectedPlay(result?.data?.response);
    return result;
  };

  return {
    handleConnectedPlay,
  };
};

export default useConnectedPlay;
