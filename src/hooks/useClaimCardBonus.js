import { useClaimCardBonusMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';

export default function useClaimCardBonus() {
  const { isAuthenticated } = useAuthStore((state) => state);

  const { mutate: claimBonus } = useClaimCardBonusMutation({
    onSuccess: (response) => {
      if (!response.data.success) {
        toast.error(response.data.message);
        remainingChestRef.current = 0;
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to claim';
      toast.error(message);
    },
  });

  return {
    claimBonus,
  };
}
