import { useState } from 'react';
import { useForgotPasswordMutation } from '@/reactQuery/authQuery';

const useForgotPassword = () => {
  const [error, setError] = useState(null);

  const mutation = useForgotPasswordMutation({
    onSuccess: (response) => {
      console.log('Password reset link sent successfully:', response);
    },
    onError: (error) => {
      console.log('error**', error);
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Failed to send reset link';
      setError(message);
    },
  });

  const forgotPassword = (email) => {
    mutation.mutate({ email });
  };

  return { forgotPassword, error, isLoading: mutation.isLoading };
};

export default useForgotPassword;
