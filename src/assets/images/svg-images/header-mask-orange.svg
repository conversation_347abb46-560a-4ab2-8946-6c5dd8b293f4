<svg width="440" height="82" viewBox="0 0 440 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_55)">
<g filter="url(#filter0_i_1_55)">
<path d="M-20.1129 -42.9553C-29.1618 -60.2661 -16.6042 -81 2.92891 -81H437.234C456.489 -81 469.063 -60.8004 460.564 -43.5233L406.452 66.4767C402.078 75.3679 393.031 81 383.122 81H60.4292C50.7496 81 41.8714 75.6229 37.3873 67.0447L-20.1129 -42.9553Z" fill="#072843"/>
</g>
<path d="M2.92891 -80H437.234C455.748 -80 467.839 -60.5773 459.667 -43.9647L405.554 66.0353C401.349 74.5845 392.649 80 383.122 80H60.4292C51.1219 80 42.5852 74.8297 38.2736 66.5814L-19.2267 -43.4186C-27.9275 -60.0635 -15.853 -80 2.92891 -80Z" stroke="#C65E11" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_i_1_55" x="-23.1063" y="-81" width="486.373" height="166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.776471 0 0 0 0 0.368627 0 0 0 0 0.0666667 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_55"/>
</filter>
<clipPath id="clip0_1_55">
<rect width="440" height="82" fill="white"/>
</clipPath>
</defs>
</svg>
