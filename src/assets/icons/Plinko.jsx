// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function PlinkoIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <circle cx="6.429" cy="10" r="2.143" />
        <circle cx="13.57" cy="10" r="2.143" />
        <circle cx="2.143" cy="17.143" r="2.143" />
        <circle cx="10" cy="17.143" r="2.143" />
        <circle cx="17.857" cy="17.143" r="2.143" />
        <path d="M10,0.714c-1.184,0-2.143,0.959-2.143,2.143S8.816,5,10,5c1.184,0,2.143-0.96,2.143-2.144S11.184,0.714,10,0.714z M10,4C9.369,4,8.857,3.488,8.857,2.856c0-0.631,0.512-1.143,1.143-1.143s1.143,0.512,1.143,1.143C11.143,3.488,10.631,4,10,4z" />
      </g>
    </svg>
  );
}

export default PlinkoIcon;
