import React from 'react';

function VIPGolddenIcon(props) {
  return (
    <svg
      width="21"
      height="19"
      viewBox="0 0 21 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.4282 13.07C14.4512 12.9376 14.3982 12.8412 14.3498 12.7835C14.2745 12.694 14.1654 12.6427 14.0505 12.6427H13.6986V13.3719H14.0485C14.235 13.3719 14.3983 13.2421 14.4282 13.07Z"
        fill="url(#paint0_linear_8049_693)"
      />
      <path
        d="M20.1212 4.86007C19.828 4.63148 19.4367 4.58761 19.1001 4.7455L16.4253 6.0002C16.2196 6.09668 16.0882 6.30344 16.0882 6.53067V7.54486C16.0882 8.40322 15.3899 9.10159 14.5315 9.10159C13.6731 9.10159 12.9748 8.40326 12.9748 7.54486C12.9748 6.9936 13.4232 6.54516 13.9745 6.54516C14.1943 6.54516 14.3957 6.42208 14.496 6.22641C14.5962 6.03074 14.5785 5.79543 14.4501 5.61695L11.2975 1.23575C11.1134 0.979884 10.8152 0.827148 10.5 0.827148C10.1848 0.827148 9.8867 0.979884 9.7026 1.23575L6.54992 5.61699C6.42148 5.79543 6.40378 6.03078 6.50406 6.22645C6.60433 6.42211 6.80566 6.5452 7.02555 6.5452C7.5768 6.5452 8.02524 6.99364 8.02524 7.5449C8.02524 8.40326 7.32692 9.10163 6.46851 9.10163C5.61011 9.10163 4.91178 8.4033 4.91178 7.5449V6.53071C4.91178 6.30348 4.78041 6.09672 4.57467 6.00024L1.89992 4.74554C1.56332 4.58765 1.17206 4.63155 0.87878 4.86011C0.585535 5.08867 0.447409 5.45738 0.518308 5.82234L2.7368 17.2402H2.65454C2.33094 17.2402 2.06859 17.5026 2.06859 17.8262C2.06859 18.1498 2.33094 18.4121 2.65454 18.4121H18.3455C18.6691 18.4121 18.9314 18.1498 18.9314 17.8262C18.9314 17.5026 18.6691 17.2402 18.3455 17.2402H18.2632L20.4817 5.82231C20.5526 5.45734 20.4145 5.08863 20.1212 4.86007ZM8.93142 12.2477L7.83165 15.5583L7.83032 15.5623C7.73965 15.8289 7.48957 16.0092 7.20805 16.0111H7.2036C6.92387 16.0111 6.67359 15.8346 6.57984 15.5707L6.57843 15.5668L5.42682 12.249C5.32069 11.9433 5.48249 11.6095 5.78819 11.5034C6.09394 11.3972 6.42777 11.5591 6.53386 11.8647L7.19301 13.7636L7.8193 11.8783C7.9213 11.5712 8.25302 11.405 8.56009 11.507C8.8672 11.609 9.03345 11.9406 8.93142 12.2477ZM11.086 5.39859C11.086 5.72219 10.8236 5.98453 10.5 5.98453C10.1764 5.98453 9.91409 5.72219 9.91409 5.39859V4.93035C9.91409 4.60675 10.1764 4.3444 10.5 4.3444C10.8236 4.3444 11.086 4.60675 11.086 4.93035V5.39859ZM11.3387 12.0568V15.4252C11.3387 15.7488 11.0763 16.0111 10.7527 16.0111C10.4291 16.0111 10.1668 15.7488 10.1668 15.4252V12.0568C10.1668 11.7333 10.4291 11.4709 10.7527 11.4709C11.0764 11.4709 11.3387 11.7333 11.3387 12.0568ZM12.5268 12.0568C12.5268 11.7333 12.7891 11.4709 13.1127 11.4709H14.0506C14.5122 11.4709 14.9482 11.6744 15.2467 12.0293C15.5374 12.375 15.6599 12.8275 15.5828 13.2709C15.4546 14.0085 14.8093 14.5439 14.0485 14.5439H13.6986V15.4252C13.6986 15.7488 13.4363 16.0111 13.1127 16.0111C12.7891 16.0111 12.5268 15.7488 12.5268 15.4252V12.0568Z"
        fill="url(#paint1_linear_8049_693)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_8049_693"
          x1="10.5"
          y1="18.4121"
          x2="10.5"
          y2="0.821309"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_8049_693"
          x1="10.5"
          y1="18.4121"
          x2="10.5"
          y2="0.821309"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default VIPGolddenIcon;
