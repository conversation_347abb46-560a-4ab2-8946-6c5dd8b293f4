// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ChestCardIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M6.442,5.055c0.218-0.722,0.89-1.25,1.683-1.25h3.75c0.793,0,1.465,0.528,1.683,1.25l5.977,0.004c-0.523-2.541-2.778-4.458-5.472-4.458H5.938c-2.693,0-4.948,1.917-5.472,4.458L6.442,5.055z M0.586,8.648h5.781V6.227H0.586C0.263,6.227,0,6.489,0,6.813v1.25C0,8.386,0.263,8.648,0.586,8.648z M4.764,9.82h2.017c0.105,0.125,0.229,0.236,0.369,0.33l1.875,1.25c0.591,0.394,1.359,0.394,1.95,0l1.875-1.25c0.141-0.094,0.265-0.205,0.369-0.33h2.018l0.379,3.281H4.385L4.764,9.82zM4.034,16.148l0.216-1.875h11.5l0.217,1.875H4.034z M13.633,6.227h5.781C19.737,6.227,20,6.489,20,6.813v1.25c0,0.323-0.263,0.586-0.586,0.586h-5.781V6.227z M0.004,16.742c-0.042,0.35,0.23,0.656,0.582,0.656h1.602c0.298,0,0.548-0.223,0.582-0.519l0.814-7.06H0.836L0.004,16.742z M16.416,9.82h2.748l0.832,6.922c0.041,0.35-0.23,0.656-0.582,0.656h-1.602c-0.298,0-0.548-0.223-0.582-0.519L16.416,9.82z M10.325,10.425c-0.197,0.132-0.453,0.132-0.65,0L7.8,9.175C7.637,9.066,7.539,8.884,7.539,8.688V5.563c0-0.323,0.263-0.586,0.586-0.586h3.75c0.323,0,0.586,0.263,0.586,0.586v3.125c0,0.196-0.098,0.379-0.261,0.487L10.325,10.425z M10,6.539c-0.323,0-0.586,0.263-0.586,0.586V7.75c0,0.323,0.263,0.586,0.586,0.586s0.586-0.263,0.586-0.586V7.125C10.586,6.802,10.323,6.539,10,6.539z" />
    </svg>
  );
}

export default ChestCardIcon;
