// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function PlusCircleIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20C4.486,20,0,15.514,0,10S4.486,0,10,0s10,4.486,10,10S15.514,20,10,20z M10,0.625c-5.17,0-9.375,4.206-9.375,9.375c0,5.17,4.205,9.375,9.375,9.375S19.375,15.17,19.375,10C19.375,4.831,15.17,0.625,10,0.625z"/>
        <g>
          <path d="M14.121,9.121h-3.242V5.879C10.879,5.395,10.486,5,10,5S9.121,5.395,9.121,5.879v3.242H5.879C5.393,9.121,5,9.516,5,10s0.393,0.879,0.879,0.879h3.242v3.242C9.121,14.605,9.514,15,10,15s0.879-0.395,0.879-0.879v-3.242h3.242C14.607,10.879,15,10.484,15,10S14.607,9.121,14.121,9.121z"/>
        </g>
      </g>
    </svg>
  );
}

export default PlusCircleIcon;
