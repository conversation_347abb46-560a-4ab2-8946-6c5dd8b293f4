// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ProfileIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M14.475,4.474c0,2.471-2.003,4.474-4.475,4.474c-2.471,0-4.473-2.004-4.473-4.474C5.527,2.004,7.529,0,10,0C12.472,0,14.475,2.003,14.475,4.474z M2.105,17.895C2.105,13.535,5.642,10,10,10c4.359,0,7.895,3.535,7.895,7.895c0,1.162-0.94,2.105-2.104,2.105H4.211C3.048,20,2.105,19.057,2.105,17.895z"/>
    </svg>
  );
}

export default ProfileIcon;
