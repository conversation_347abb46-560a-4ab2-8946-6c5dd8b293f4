import React from 'react';

function MessageGoldenIcon(props) {
  return (
    <svg
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.125 2V12C17.125 12.3315 16.9933 12.6495 16.7589 12.8839C16.5245 13.1183 16.2065 13.25 15.875 13.25H5.4844L2.93752 15.45L2.93049 15.4555C2.70552 15.6462 2.41998 15.7506 2.12502 15.75C1.94164 15.7497 1.76057 15.7091 1.59455 15.6312C1.37862 15.5317 1.19592 15.3721 1.06832 15.1715C0.940717 14.9709 0.873609 14.7378 0.875022 14.5V2C0.875022 1.66848 1.00672 1.35054 1.24114 1.11612C1.47556 0.881696 1.7935 0.75 2.12502 0.75H15.875C16.2065 0.75 16.5245 0.881696 16.7589 1.11612C16.9933 1.35054 17.125 1.66848 17.125 2Z"
        fill="url(#paint0_linear_8049_9671)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_8049_9671"
          x1="9.00001"
          y1="15.75"
          x2="9.00001"
          y2="0.745019"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default MessageGoldenIcon;
