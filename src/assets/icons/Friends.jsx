// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FriendsIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M4.922,8.242c-1.831,0-3.32-1.489-3.32-3.32s1.489-3.32,3.32-3.32s3.32,1.489,3.32,3.32S6.753,8.242,4.922,8.242z" />
        <path d="M15.078,8.242c-1.831,0-3.32-1.489-3.32-3.32s1.489-3.32,3.32-3.32c1.83,0,3.32,1.489,3.32,3.32S16.908,8.242,15.078,8.242z"/>
        <path d="M15.898,11.074c0-0.761,0.301-1.45,0.789-1.959c-0.5,0.192-1.043,0.299-1.609,0.299c-1.359,0-2.578-0.606-3.402-1.563H8.367c1.349,1.036,2.219,2.665,2.219,4.492v6.056h5.664v-3.125c0-0.324,0.262-0.586,0.586-0.586c0.323,0,0.586,0.262,0.586,0.586v3.125h1.563c0.324,0,0.586-0.263,0.586-0.586v-3.906h-0.84C17.168,13.906,15.898,12.636,15.898,11.074z"/>
        <path d="M19.57,13.9v-0.002C19.566,13.898,19.566,13.9,19.57,13.9z" />
        <path d="M19.414,12.734H18.73c-0.916,0-1.66-0.745-1.66-1.66c0-0.916,0.744-1.66,1.66-1.66c0.699,0,1.27,0.568,1.27,1.27v1.465C20,12.473,19.738,12.734,19.414,12.734z"/>
        <path d="M7.45,8.633C6.729,9.125,5.858,9.414,4.922,9.414c-0.567,0-1.11-0.106-1.61-0.299c0.488,0.509,0.79,1.199,0.79,1.959c0,1.562-1.271,2.832-2.832,2.832H0.43v3.906c0,0.323,0.263,0.586,0.586,0.586h1.563v-3.125c0-0.324,0.263-0.586,0.586-0.586c0.323,0,0.586,0.262,0.586,0.586v3.125h5.664v-6.056C9.414,10.805,8.635,9.441,7.45,8.633z"/>
        <path d="M0.43,13.898V13.9C0.436,13.9,0.437,13.898,0.43,13.898z" />
        <path d="M0,12.148v-1.465c0-0.701,0.569-1.271,1.27-1.271c0.915,0,1.66,0.745,1.66,1.66c0,0.916-0.745,1.659-1.66,1.659H0.586C0.263,12.734,0,12.473,0,12.148z"/>
      </g>
    </svg>
  );
}

export default FriendsIcon;
