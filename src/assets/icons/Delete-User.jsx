// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function DeleteUserIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3678_14321)">
        <path d="M14.5665 18.4999H2.35163C1.29303 18.4999 0.539129 17.4687 0.863347 16.4609L2.1485 12.4453C2.56257 11.1562 3.97272 10.4882 5.23053 10.9765C6.06647 11.3007 7.14069 11.5546 8.4571 11.5546C9.7735 11.5546 10.8477 11.3007 11.6837 10.9765C11.7305 10.957 11.7813 10.9413 11.8321 10.9257V11.0234C11.8321 13.332 13.5469 15.246 15.7696 15.5585L16.0587 16.4609C16.379 17.4687 15.629 18.4999 14.5665 18.4999Z" />
        <path d="M8.46094 10.0547C10.6852 10.0547 12.4883 8.25158 12.4883 6.02734C12.4883 3.8031 10.6852 2 8.46094 2C6.2367 2 4.43359 3.8031 4.43359 6.02734C4.43359 8.25158 6.2367 10.0547 8.46094 10.0547Z" />
        <path d="M16.4103 7.22266C14.3126 7.22266 12.6095 8.92578 12.6095 11.0234C12.6095 13.1211 14.3126 14.8242 16.4103 14.8242C18.5079 14.8242 20.2111 13.125 20.2111 11.0234C20.2111 8.92187 18.5118 7.22266 16.4103 7.22266ZM18.4142 11.7813C18.7579 12.125 18.7579 12.6797 18.4142 13.0234C18.0704 13.3672 17.5157 13.3672 17.172 13.0234L16.4103 12.2617L15.6525 13.0234C15.3087 13.3672 14.754 13.3672 14.4103 13.0234C14.0665 12.6797 14.0665 12.125 14.4103 11.7813L15.172 11.0195L14.4103 10.2617C14.0665 9.91797 14.0665 9.36328 14.4103 9.01953C14.754 8.67578 15.3087 8.67578 15.6525 9.01953L16.4142 9.77734L17.1759 9.01953C17.5197 8.67578 18.0743 8.67578 18.4181 9.01953C18.7618 9.36328 18.7618 9.91797 18.4181 10.2617L17.6564 11.0195L18.4142 11.7813Z" />
      </g>
      <defs>
        <clipPath id="clip0_3678_14321">
          <rect width="20" height="20" transform="translate(0.5 0.25)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default DeleteUserIcon;
