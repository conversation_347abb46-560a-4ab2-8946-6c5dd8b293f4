// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function InfoCircleIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10.009,0C15.549,0.011,20.023,4.499,20,10.02c-0.023,5.539-4.49,9.992-10.009,9.98C4.453,19.989-0.023,15.499,0,9.979C0.024,4.442,4.49-0.011,10.009,0z M18.182,10c0-4.528-3.662-8.188-8.187-8.182C5.471,1.825,1.831,5.464,1.819,9.989c-0.012,4.521,3.646,8.186,8.175,8.192C14.518,18.188,18.182,14.527,18.182,10z"/>
        <path d="M10.938,9.335c-0.006,0.801-0.269,1.556-0.426,2.328c-0.104,0.514-0.208,1.026-0.309,1.542c-0.018,0.093-0.022,0.189-0.022,0.286c-0.005,0.588,0.28,0.817,0.865,0.706c0.097-0.02,0.259-0.053,0.28-0.014c0.07,0.121,0.096,0.272,0.126,0.416c0.005,0.024-0.041,0.077-0.075,0.092c-0.458,0.199-0.889,0.463-1.403,0.524c-1.063,0.128-1.807-0.493-1.737-1.562c0.047-0.717,0.23-1.426,0.365-2.135c0.108-0.565,0.249-1.125,0.348-1.692c0.039-0.22,0.028-0.457-0.003-0.681C8.911,8.885,8.776,8.778,8.51,8.759C8.365,8.748,8.209,8.742,8.071,8.781c-0.165,0.047-0.247-0.005-0.27-0.153C7.78,8.489,7.629,8.329,7.882,8.225C8.413,8.004,8.92,7.72,9.522,7.73C10.438,7.745,10.961,8.334,10.938,9.335z"/>
        <path d="M8.932,5.675c0.002-0.759,0.603-1.364,1.346-1.358c0.727,0.005,1.318,0.62,1.316,1.369c-0.001,0.75-0.591,1.353-1.327,1.354C9.517,7.041,8.929,6.44,8.932,5.675z"/>
      </g>
    </svg>
  );
}

export default InfoCircleIcon;
