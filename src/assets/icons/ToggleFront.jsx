// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ToggleFront(props) {
  return (
    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
<path d="M0.0502522 2.05025C1.02922 1.07129 2.2765 0.4046 3.63437 0.134503C4.99223 -0.135593 6.3997 0.00302969 7.67878 0.532843C8.95787 1.06266 10.0511 1.95986 10.8203 3.11101C11.5895 4.26215 12 5.61553 12 7C12 8.38447 11.5895 9.73785 10.8203 10.889C10.0511 12.0401 8.95787 12.9373 7.67879 13.4672C6.3997 13.997 4.99224 14.1356 3.63437 13.8655C2.2765 13.5954 1.02922 12.9287 0.0502541 11.9497L5 7L0.0502522 2.05025Z" fill="white"/>
</svg>

  );
}

export default ToggleFront;
