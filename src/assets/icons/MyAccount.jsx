// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function MyAccountIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M18.473,2.51h-0.752v9.373c0,0.47-0.184,0.911-0.516,1.242l-5.021,5.021c-0.332,0.332-0.773,0.516-1.243,0.516h-7.49v0.752C3.452,19.738,3.714,20,4.038,20h14.435c0.324,0,0.586-0.262,0.586-0.586V3.096C19.059,2.773,18.797,2.51,18.473,2.51z" />
        <path d="M11.256,6.193c-0.889,0-1.611,0.722-1.611,1.61v0.355h3.221V7.803C12.865,6.915,12.144,6.193,11.256,6.193z" />
        <path d="M11.256,5.021c0.368,0,0.668-0.3,0.668-0.669c-0.036-0.888-1.301-0.888-1.338,0C10.586,4.721,10.887,5.021,11.256,5.021z" />
        <path d="M10.355,11.883c0-0.324,0.262-0.586,0.586-0.586h5.607V0.586C16.549,0.262,16.285,0,15.963,0H1.527C1.204,0,0.941,0.262,0.941,0.586v16.318c0,0.322,0.262,0.586,0.586,0.586h8.828V11.883z M4.038,3.138h2.511c0.777,0.031,0.777,1.141,0,1.172H4.038C3.26,4.279,3.261,3.169,4.038,3.138z M4.038,5.648h2.511c0.777,0.031,0.777,1.141,0,1.172H4.038C3.26,6.79,3.261,5.679,4.038,5.648z M4.038,8.159h2.511c0.777,0.031,0.777,1.142,0,1.172H4.038C3.26,9.3,3.261,8.189,4.038,8.159z M8.431,14.352H4.038c-0.777-0.031-0.777-1.141,0-1.172h4.394C9.208,13.211,9.208,14.32,8.431,14.352z M8.431,11.842H4.038c-0.777-0.031-0.777-1.143,0-1.172h4.394C9.208,10.7,9.208,11.811,8.431,11.842z M8.473,8.745V7.803c0-0.991,0.521-1.863,1.304-2.355c-0.899-1.19-0.025-2.945,1.479-2.938c1.504-0.008,2.377,1.748,1.478,2.938c0.782,0.493,1.304,1.364,1.304,2.355v0.941c0,0.324-0.262,0.586-0.586,0.586H9.059C8.735,9.331,8.473,9.068,8.473,8.745z" />
        <path d="M16.205,12.469h-4.678v4.678L16.205,12.469z" />
      </g>
    </svg>
  );
}

export default MyAccountIcon;
