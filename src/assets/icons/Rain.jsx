// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function RainIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M16.988,5.361C16.727,3.681,15.27,2.39,13.516,2.39c-0.66,0-1.295,0.183-1.846,0.523c-0.834-1.405-2.34-2.28-4.014-2.28c-2.584,0-4.688,2.103-4.688,4.688c0,0.016,0,0.033,0,0.049C1.308,5.648,0,7.097,0,8.835c0,1.938,1.616,3.518,3.555,3.518h12.891c1.938,0,3.555-1.578,3.555-3.518C20,7.083,18.67,5.625,16.988,5.361z" />
        <path d="M5.363,18.932l2-5.041l1.09,0.432l-2,5.043L5.363,18.932z" />
        <path d="M8.861,18.936l2.002-5.037l1.088,0.434l-2,5.035L8.861,18.936z" />
        <path d="M12.395,18.93l2-5.045l1.092,0.436l-2.002,5.041L12.395,18.93z" />
        <path d="M1.831,18.932l2.001-5.035l1.088,0.432l-2,5.037L1.831,18.932z" />
      </g>
    </svg>
  );
}

export default RainIcon;
