// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ShoppigCartIcon(props) {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="#578495" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_5001_8119)">
        <path d="M6.48437 12.9298H17.0705C17.3326 12.9298 17.5625 12.7565 17.6335 12.5047L19.9774 4.30145C20.0278 4.1246 19.9933 3.93463 19.8823 3.78769C19.7713 3.6412 19.5985 3.55469 19.4142 3.55469H5.13168L4.71283 1.66992C4.65332 1.40152 4.41528 1.21094 4.14062 1.21094H0.585937C0.262146 1.21094 0 1.47308 0 1.79687C0 2.12082 0.262146 2.38281 0.585937 2.38281H3.67035L5.78628 11.9043C5.16372 12.175 4.72656 12.7946 4.72656 13.5158C4.72656 14.485 5.51513 15.2736 6.48437 15.2736H17.0705C17.3944 15.2736 17.6564 15.0116 17.6564 14.6876C17.6564 14.3639 17.3944 14.1017 17.0705 14.1017H6.48437C6.16165 14.1017 5.89843 13.8391 5.89843 13.5158C5.89843 13.1924 6.16165 12.9298 6.48437 12.9298Z" />
        <path d="M5.89844 17.0314C5.89844 18.0008 6.68701 18.7892 7.6564 18.7892C8.62564 18.7892 9.41421 18.0008 9.41421 17.0314C9.41421 16.0621 8.62564 15.2736 7.6564 15.2736C6.68701 15.2736 5.89844 16.0621 5.89844 17.0314Z" />
        <path d="M14.1406 17.0314C14.1406 18.0008 14.9292 18.7892 15.8984 18.7892C16.8678 18.7892 17.6562 18.0008 17.6562 17.0314C17.6562 16.0621 16.8678 15.2736 15.8984 15.2736C14.9292 15.2736 14.1406 16.0621 14.1406 17.0314Z" />
      </g>
      <defs>
        <clipPath id="clip0_5001_8119">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>

  );
}

export default ShoppigCartIcon;
