// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FavoriteStrokeIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M9.999,16.156l1.376,0.49v1.735l-0.455-0.344c-0.271-0.203-0.609-0.315-0.95-0.315c-0.346,0-0.688,0.116-0.965,0.329l-0.382,0.296v-1.701L9.999,16.156 M10,15.146c-0.093,0-0.186,0.016-0.274,0.047l-2.103,0.748V19.6c0,0.229,0.188,0.387,0.388,0.387c0.08,0,0.161-0.024,0.233-0.079l1.373-1.063c0.104-0.08,0.229-0.121,0.354-0.121c0.123,0,0.245,0.039,0.348,0.117l1.44,1.082C11.831,19.975,11.911,20,11.989,20c0.198,0,0.386-0.158,0.386-0.387v-3.672l-2.103-0.748C10.185,15.162,10.093,15.146,10,15.146L10,15.146z"/>
        <path d="M9.999,1.128l2.024,2.793c0.379,0.521,0.902,0.904,1.516,1.106l3.355,1.108l-2.051,2.698c-0.408,0.54-0.63,1.208-0.621,1.88l0.038,3.525l-0.309-0.109l-1.909-0.678l-1.021-0.363c-0.326-0.117-0.671-0.176-1.021-0.176c-0.351,0-0.695,0.059-1.022,0.176l-2.026,0.721l-1.213,0.431l0.039-3.525c0.008-0.677-0.213-1.345-0.622-1.881l-2.05-2.697L6.46,5.027c0.613-0.202,1.137-0.584,1.515-1.106L9.999,1.128 M10,0C9.75,0,9.5,0.114,9.336,0.339l-2.17,2.995c-0.254,0.35-0.605,0.607-1.018,0.743l-3.597,1.19C2.017,5.442,1.816,6.092,2.157,6.54L4.36,9.439c0.275,0.361,0.423,0.81,0.418,1.265l-0.042,3.785c-0.005,0.473,0.38,0.829,0.817,0.829c0.091,0,0.184-0.016,0.277-0.049c2.164-0.77,1.366-0.483,3.483-1.237c0.22-0.078,0.454-0.117,0.688-0.117c0.233,0,0.466,0.039,0.688,0.117c2.117,0.754,1.318,0.471,3.482,1.237c0.092,0.033,0.186,0.049,0.276,0.049c0.437,0,0.823-0.356,0.815-0.829l-0.041-3.785c-0.006-0.453,0.145-0.902,0.418-1.264l2.202-2.899c0.341-0.448,0.142-1.098-0.395-1.274l-3.597-1.188c-0.412-0.136-0.765-0.393-1.02-0.744L10.661,0.34C10.497,0.113,10.248,0,10,0L10,0z"/>
      </g>
    </svg>
  );
}

export default FavoriteStrokeIcon;
