// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function DeleteBinIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M16.25,2.344h-2.931V1.367C13.319,0.613,12.708,0,11.953,0H8.047C7.294,0,6.68,0.613,6.68,1.367v0.977H3.75c-1.187,0-2.148,0.964-2.148,2.148v1.25c0,0.323,0.264,0.586,0.586,0.586h0.649l1.344,11.768C4.306,19.182,5.224,20,6.314,20h7.369c1.092,0,2.01-0.818,2.133-1.904l1.347-11.768h0.649c0.322,0,0.586-0.263,0.586-0.586v-1.25C18.398,3.308,17.436,2.344,16.25,2.344z M7.852,1.367c0-0.107,0.088-0.195,0.195-0.195h3.906c0.106,0,0.194,0.088,0.194,0.195v0.977H7.852V1.367z M14.654,17.963c-0.059,0.493-0.475,0.865-0.971,0.865H6.314c-0.496,0-0.914-0.372-0.97-0.865L4.017,6.328h11.968L14.654,17.963z M17.227,5.156H2.772V4.492c0-0.538,0.438-0.977,0.978-0.977h12.5c0.538,0,0.977,0.438,0.977,0.977V5.156z" />
        <path d="M12.64,17.654c0.323,0.021,0.601-0.229,0.618-0.551l0.516-8.984c0.017-0.324-0.23-0.6-0.554-0.619s-0.6,0.229-0.618,0.551l-0.515,8.985C12.068,17.359,12.316,17.637,12.64,17.654z" />
        <path d="M10,17.656c0.323,0,0.586-0.264,0.586-0.586V8.086C10.586,7.763,10.323,7.5,10,7.5S9.414,7.763,9.414,8.086v8.984C9.414,17.395,9.677,17.656,10,17.656z" />
        <path d="M6.742,17.104c0.019,0.323,0.295,0.571,0.616,0.553c0.323-0.02,0.571-0.297,0.553-0.618L7.397,8.052c-0.02-0.323-0.295-0.57-0.618-0.551S6.21,7.797,6.229,8.12L6.742,17.104z" />
      </g>
    </svg>
  );
}

export default DeleteBinIcon;
