// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FavoriteIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M9.726,15.193l-2.103,0.748V19.6c0,0.321,0.368,0.502,0.621,0.306l1.373-1.062c0.206-0.159,0.494-0.161,0.702-0.004l1.44,1.082c0.254,0.191,0.616,0.01,0.616-0.309v-3.672l-2.102-0.748C10.098,15.131,9.903,15.131,9.726,15.193z" />
        <path d="M17.448,5.267l-3.597-1.188c-0.411-0.136-0.763-0.393-1.018-0.744l-2.171-2.995c-0.331-0.455-1-0.449-1.326,0l-2.17,2.995C6.912,3.685,6.561,3.942,6.148,4.078L2.552,5.267C2.017,5.443,1.817,6.093,2.157,6.54L4.36,9.439c0.275,0.361,0.423,0.81,0.418,1.264l-0.042,3.785C4.73,15.061,5.293,15.461,5.83,15.27c2.164-0.769,1.366-0.485,3.483-1.238c0.44-0.156,0.932-0.157,1.375,0c2.116,0.753,1.319,0.47,3.483,1.238c0.535,0.191,1.1-0.209,1.093-0.781l-0.042-3.785c-0.005-0.453,0.144-0.902,0.418-1.264l2.203-2.899C18.184,6.093,17.984,5.443,17.448,5.267z" />
      </g>
    </svg>
  );
}

export default FavoriteIcon;
