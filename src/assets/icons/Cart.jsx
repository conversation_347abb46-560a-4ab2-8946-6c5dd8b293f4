// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CartIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M3.029,2.383H0.586C0.262,2.383,0,2.121,0,1.797c0-0.324,0.262-0.586,0.586-0.586h2.886c0.261,0,0.491,0.173,0.563,0.425l0.548,1.918h14.831c0.184,0,0.355,0.086,0.468,0.233c0.111,0.147,0.146,0.337,0.097,0.514l-2.345,8.204c-0.071,0.252-0.302,0.425-0.563,0.425H6.847l-0.162,0.324c-0.195,0.391,0.09,0.848,0.524,0.848h9.861c0.324,0,0.586,0.263,0.586,0.586c0,0.324-0.262,0.586-0.586,0.586H7.209c-1.309,0-2.157-1.377-1.572-2.543l0.221-0.444L3.029,2.383z"/>
        <circle cx="8.828" cy="17.031" r="1.758"/>
        <circle cx="14.727" cy="17.031" r="1.758"/>
      </g>
    </svg>
  );
}

export default CartIcon;
