// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function VIPIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M13.928,13.45c0.023-0.132-0.029-0.229-0.078-0.286c-0.074-0.09-0.184-0.141-0.299-0.141h-0.352v0.729h0.35C13.734,13.752,13.898,13.623,13.928,13.45z"/>
        <path d="M19.621,5.24c-0.293-0.229-0.686-0.272-1.021-0.114L15.926,6.38c-0.205,0.097-0.338,0.304-0.338,0.531v1.014c0,0.858-0.697,1.557-1.557,1.557s-1.557-0.698-1.557-1.557c0-0.551,0.448-1,1-1c0.219,0,0.42-0.123,0.521-0.318s0.082-0.431-0.046-0.609l-3.153-4.381C10.613,1.36,10.315,1.207,10,1.207S9.387,1.36,9.203,1.616L6.05,5.997C5.921,6.176,5.904,6.411,6.004,6.606c0.101,0.195,0.302,0.318,0.521,0.318c0.551,0,1,0.449,1,1c0,0.858-0.698,1.557-1.557,1.557c-0.859,0-1.556-0.698-1.556-1.556V6.911c0-0.228-0.131-0.435-0.337-0.531L1.4,5.126C1.063,4.968,0.672,5.012,0.379,5.24c-0.293,0.229-0.432,0.598-0.36,0.962l2.218,11.419H2.154c-0.323,0-0.586,0.262-0.586,0.586c0,0.322,0.263,0.586,0.586,0.586h15.692c0.322,0,0.586-0.264,0.586-0.586c0-0.324-0.264-0.586-0.586-0.586h-0.082L19.98,6.202C20.053,5.838,19.914,5.469,19.621,5.24z M8.432,12.628l-1.1,3.312L7.33,15.943c-0.09,0.266-0.34,0.445-0.622,0.447H6.704c-0.28,0-0.53-0.177-0.624-0.439l-0.001-0.004l-1.152-3.318c-0.106-0.305,0.056-0.639,0.361-0.745c0.305-0.106,0.64,0.056,0.746,0.361l0.659,1.899l0.626-1.887c0.102-0.308,0.434-0.474,0.741-0.372C8.367,11.989,8.533,12.32,8.432,12.628z M10.586,5.778c0,0.324-0.262,0.586-0.586,0.586S9.414,6.103,9.414,5.778V5.311c0-0.323,0.262-0.586,0.586-0.586s0.586,0.263,0.586,0.586V5.778z M10.839,12.438v3.369c0,0.322-0.263,0.586-0.586,0.586c-0.324,0-0.586-0.264-0.586-0.586v-3.369c0-0.323,0.262-0.586,0.586-0.586C10.576,11.852,10.839,12.113,10.839,12.438zM12.027,12.438c0-0.323,0.262-0.586,0.586-0.586h0.938c0.461,0,0.897,0.203,1.195,0.56c0.291,0.346,0.414,0.798,0.337,1.241c-0.128,0.736-0.774,1.271-1.534,1.271h-0.35v0.883c0,0.322-0.264,0.586-0.586,0.586c-0.324,0-0.586-0.264-0.586-0.586V12.438z"/>
      </g>
    </svg>
  );
}

export default VIPIcon;
