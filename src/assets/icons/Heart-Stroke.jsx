'use client';

import React from 'react';

function HeartStrokeIcon(props) {
  const uniqueId = React.useId();

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <g clipPath={`url(#clip-${uniqueId})`}>
        <path
          d="M11.6099 1.49902C12.6644 1.49905 13.6179 1.93476 14.2886 2.72656L14.4028 2.86914C14.962 3.59919 15.2642 4.54803 15.2642 5.55859C15.2641 6.70843 14.8577 7.73127 13.9829 8.83301L13.8022 9.05469C13.1334 9.84872 12.2653 10.6429 11.2173 11.5488L10.1099 12.4961L8.63232 13.7617C8.544 13.8383 8.43427 13.8779 8.32373 13.8779C8.24094 13.8779 8.15867 13.8558 8.08545 13.8125L8.01514 13.7617L6.5376 12.4961C5.17715 11.338 4.05376 10.3822 3.19678 9.45215L2.84619 9.05469C1.84602 7.86737 1.38439 6.78507 1.38428 5.55859C1.38428 4.48094 1.72727 3.47345 2.35986 2.72656L2.48877 2.58203C3.14996 1.88175 4.05 1.49902 5.03857 1.49902C6.22284 1.49915 7.24869 2.06972 8.00244 3.12402L8.15088 3.34375C8.21409 3.44247 8.2711 3.54025 8.32373 3.63477C8.4151 3.47068 8.52114 3.29733 8.64502 3.12402L8.78955 2.93262C9.52783 2.00077 10.4992 1.49902 11.6099 1.49902ZM11.6099 2.47754C10.7993 2.47754 10.1047 2.82715 9.5376 3.54102L9.42529 3.68848C9.19731 4.00459 9.03635 4.32662 8.93213 4.57031C8.88027 4.69161 8.84331 4.79341 8.81885 4.86426L8.78467 4.9668C8.78397 4.96915 8.78402 4.97153 8.78369 4.97266L8.75635 5.04688C8.69063 5.18964 8.55984 5.2944 8.40381 5.32129L8.32373 5.32812C8.10674 5.3278 7.92232 5.17907 7.86475 4.97363L7.82861 4.8623C7.76744 4.68537 7.62545 4.31915 7.38135 3.92676L7.22217 3.68848C6.63553 2.87524 5.90294 2.47766 5.03857 2.47754C4.15186 2.47754 3.48293 2.86012 3.03174 3.43164C2.57702 4.00765 2.33936 4.78228 2.33936 5.55859C2.33947 6.51425 2.70663 7.3914 3.56885 8.41504L3.8999 8.78906C4.71061 9.66728 5.79926 10.5943 7.14893 11.7432L8.32373 12.748C8.73062 12.3974 9.12052 12.0658 9.49951 11.7432L10.5923 10.8086C11.6199 9.92046 12.452 9.1589 13.0786 8.41504L13.3794 8.03711C14.0271 7.17053 14.308 6.39469 14.3081 5.55859C14.3081 4.78226 14.0705 4.00766 13.6157 3.43164C13.1645 2.86024 12.4964 2.47756 11.6099 2.47754Z"
          fill={`url(#paint0-${uniqueId})`}
          stroke={`url(#paint1-${uniqueId})`}
          strokeWidth="0.375122"
        />
      </g>
      <defs>
        <linearGradient id={`paint0-${uniqueId}`} x1="8.32397" y1="13.6904" x2="8.32397" y2="1.68254" gradientUnits="userSpaceOnUse">
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <linearGradient id={`paint1-${uniqueId}`} x1="8.32397" y1="13.6904" x2="8.32397" y2="1.68254" gradientUnits="userSpaceOnUse">
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <clipPath id={`clip-${uniqueId}`}>
          <rect width="15.0049" height="15.0049" fill="white" transform="translate(0.821289 0.186035)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default HeartStrokeIcon;
