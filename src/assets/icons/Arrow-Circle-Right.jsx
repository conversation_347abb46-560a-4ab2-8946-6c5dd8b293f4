// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCircleRightIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20C4.486,20,0,15.516,0,10C0,4.486,4.486,0,10,0c5.516,0,10,4.486,10,10C20,15.516,15.516,20,10,20z M10,1.512C5.32,1.512,1.512,5.32,1.512,10c0,4.682,3.808,8.488,8.488,8.488c4.682,0,8.488-3.809,8.488-8.488C18.488,5.32,14.682,1.512,10,1.512z"/>
        <path d="M5.674,8.381h4.811L9.161,7.048c-0.3-0.302-0.466-0.704-0.466-1.131s0.166-0.83,0.466-1.131c0.618-0.624,1.628-0.624,2.249,0l4.033,4.063c0.301,0.301,0.468,0.702,0.468,1.131v0.037c0,0.426-0.165,0.828-0.466,1.129l-4.035,4.064c-0.303,0.303-0.7,0.471-1.125,0.471c-0.426,0-0.825-0.168-1.122-0.469c-0.302-0.302-0.468-0.704-0.468-1.132c0-0.429,0.166-0.828,0.466-1.132l1.323-1.332H5.674c-0.873,0-1.583-0.727-1.583-1.619C4.09,9.107,4.8,8.381,5.674,8.381z"/>
      </g>
    </svg>
  );
}

export default ArrowCircleRightIcon;
