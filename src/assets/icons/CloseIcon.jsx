// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CloseIcon(props) {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="#000000" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_128_2295)">
        <path d="M10 20C7.32891 20 4.8177 18.9598 2.92891 17.0711C1.0402 15.1823 0 12.6711 0 10C0 7.32891 1.0402 4.8177 2.92891 2.92891C4.8177 1.0402 7.32891 0 10 0C12.6711 0 15.1823 1.0402 17.0711 2.92891C18.9598 4.8177 20 7.32891 20 10C20 12.6711 18.9598 15.1823 17.0711 17.0711C15.1823 18.9598 12.6711 20 10 20ZM10 1.5625C7.74625 1.5625 5.62742 2.44016 4.03379 4.03379C2.44016 5.62742 1.5625 7.74625 1.5625 10C1.5625 12.2537 2.44016 14.3726 4.03379 15.9662C5.62742 17.5598 7.74625 18.4375 10 18.4375C12.2537 18.4375 14.3726 17.5598 15.9662 15.9662C17.5598 14.3726 18.4375 12.2537 18.4375 10C18.4375 7.74625 17.5598 5.62742 15.9662 4.03379C14.3726 2.44016 12.2537 1.5625 10 1.5625ZM13.3146 14.4194L10 11.1048L6.68543 14.4194L5.58059 13.3146L8.89516 10L5.58059 6.68543L6.68543 5.58059L10 8.89516L13.3146 5.58059L14.4195 6.68543L11.1048 10L14.4195 13.3146L13.3146 14.4194Z" />
      </g>
      <defs>
        <clipPath id="clip0_128_2295">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default CloseIcon;
