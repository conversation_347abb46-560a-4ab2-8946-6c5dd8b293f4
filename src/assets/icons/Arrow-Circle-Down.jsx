// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCircleDownIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20C4.486,20,0,15.514,0,10C0,4.485,4.486,0,10,0c5.514,0,10,4.485,10,10C20,15.514,15.514,20,10,20z M10,1.512C5.319,1.512,1.513,5.32,1.513,10c0,4.68,3.807,8.488,8.487,8.488c4.68,0,8.489-3.809,8.489-8.488C18.489,5.32,14.682,1.512,10,1.512z"/>
        <path d="M11.619,5.673v4.809l1.334-1.322c0.301-0.3,0.703-0.466,1.129-0.466c0.43,0,0.83,0.166,1.131,0.466c0.625,0.619,0.625,1.628,0,2.248l-4.063,4.035c-0.301,0.299-0.702,0.467-1.131,0.467H9.982c-0.427,0-0.828-0.166-1.129-0.465l-4.064-4.037c-0.304-0.301-0.471-0.699-0.471-1.125c0-0.426,0.167-0.824,0.468-1.122C5.087,8.858,5.49,8.692,5.917,8.692c0.429,0,0.829,0.164,1.132,0.466l1.333,1.321V5.673c0-0.873,0.728-1.583,1.619-1.583S11.619,4.8,11.619,5.673z"/>
      </g>
    </svg>
  );
}

export default ArrowCircleDownIcon;
