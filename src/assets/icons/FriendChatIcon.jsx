// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FriendChatIcon({fill}) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 20 20" fill="none">
      <path d="M8.41535 10.9131C8.99337 10.5652 9.46335 10.0636 9.77286 9.46416C10.0824 8.86472 10.2192 8.19107 10.1682 7.51838C10.0947 6.68386 9.71991 5.90462 9.1139 5.32617C8.5079 4.74773 7.71208 4.40962 6.87504 4.375C6.038 4.40962 5.24217 4.74773 4.63617 5.32617C4.03017 5.90462 3.65543 6.68386 3.58191 7.51838C3.53085 8.19107 3.66771 8.86472 3.97722 9.46416C4.28673 10.0636 4.75671 10.5652 5.33473 10.9131C4.36003 11.2365 3.51193 11.8586 2.91073 12.6912C2.30952 13.5238 1.98575 14.5245 1.98535 15.5515C1.98536 15.7367 2.05895 15.9144 2.18994 16.0454C2.32094 16.1764 2.49861 16.25 2.68386 16.25H11.0662C11.2515 16.25 11.4291 16.1764 11.5601 16.0454C11.6911 15.9144 11.7647 15.7367 11.7647 15.5515C11.7643 14.5245 11.4406 13.5238 10.8394 12.6912C10.2382 11.8586 9.39005 11.2365 8.41535 10.9131Z" fill={fill||"url(#paint0_linear_7628_16004)"} />
      <path d="M18.1251 13.5687C18.1257 13.6394 18.1121 13.7095 18.0853 13.775C18.0585 13.8404 18.0189 13.8999 17.9689 13.9499C17.9189 13.9999 17.8595 14.0395 17.794 14.0663C17.7286 14.0931 17.6585 14.1067 17.5877 14.1062H12.5189C12.2837 13.1791 11.8195 12.326 11.1689 11.6249C11.6339 10.8666 12.3506 10.2958 13.1938 10.0124C12.7688 9.72739 12.4228 9.33958 12.1879 8.88503C11.953 8.43046 11.8368 7.9239 11.8501 7.4124C11.8501 5.78115 12.9814 4.4624 14.3751 4.4624C15.7689 4.4624 16.9001 5.78115 16.9001 7.4124C16.9135 7.9239 16.7972 8.43047 16.5623 8.88503C16.3274 9.33959 15.9814 9.7274 15.5565 10.0124C16.3036 10.2607 16.9538 10.7377 17.4148 11.3759C17.8758 12.0142 18.1243 12.7813 18.1251 13.5687Z" fill={fill ||"url(#paint1_linear_7628_16004)"} />
      <defs>
        <linearGradient id="paint0_linear_7628_16004" x1="6.87505" y1="16.25" x2="6.87505" y2="4.37106" gradientUnits="userSpaceOnUse">
          <stop stop-color="#BB7733" />
          <stop offset="0.07" stop-color="#C17F37" />
          <stop offset="0.2" stop-color="#D19844" />
          <stop offset="0.33" stop-color="#EABB58" />
          <stop offset="0.47" stop-color="#EECE6C" />
          <stop offset="0.61" stop-color="#F2DD7B" />
          <stop offset="0.82" stop-color="#ECC764" />
          <stop offset="0.99" stop-color="#EABB58" />
        </linearGradient>
        <linearGradient id="paint1_linear_7628_16004" x1="14.647" y1="14.1062" x2="14.647" y2="4.4592" gradientUnits="userSpaceOnUse">
          <stop stop-color="#BB7733" />
          <stop offset="0.07" stop-color="#C17F37" />
          <stop offset="0.2" stop-color="#D19844" />
          <stop offset="0.33" stop-color="#EABB58" />
          <stop offset="0.47" stop-color="#EECE6C" />
          <stop offset="0.61" stop-color="#F2DD7B" />
          <stop offset="0.82" stop-color="#ECC764" />
          <stop offset="0.99" stop-color="#EABB58" />
        </linearGradient>
      </defs>
    </svg>);
}

export default FriendChatIcon;
