// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function GIFIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M16.202,1.748H3.798C1.704,1.748,0,3.452,0,5.546v8.908c0,2.094,1.704,3.798,3.798,3.798h12.404c2.095,0,3.798-1.704,3.798-3.798V5.546C20,3.452,18.297,1.748,16.202,1.748z M8.388,12.718c-0.628,0.769-1.474,1.189-2.382,1.189c-0.907,0-1.752-0.423-2.381-1.189C3.026,11.986,2.696,11.021,2.696,10c0-1.021,0.33-1.987,0.929-2.718c0.629-0.768,1.474-1.19,2.381-1.19c0.953,0,1.859,0.482,2.486,1.323c0.201,0.27,0.146,0.651-0.125,0.853S7.715,8.414,7.515,8.144C7.113,7.607,6.578,7.312,6.006,7.312c-1.152,0-2.09,1.206-2.09,2.688c0,1.48,0.938,2.688,2.09,2.688c0.99,0,1.822-0.888,2.036-2.078H7.208c-0.338,0-0.61-0.271-0.61-0.608s0.272-0.61,0.61-0.61h1.499c0.337,0,0.609,0.273,0.609,0.61C9.316,11.021,8.987,11.986,8.388,12.718z M11.801,13.298c0,0.337-0.271,0.609-0.609,0.609s-0.609-0.272-0.609-0.609V6.702c0-0.337,0.271-0.61,0.609-0.61s0.609,0.273,0.609,0.61V13.298z M16.693,8.611c0.336,0,0.609,0.272,0.609,0.609c0,0.337-0.273,0.61-0.609,0.61h-2.408v3.468c0,0.337-0.273,0.609-0.61,0.609c-0.336,0-0.608-0.272-0.608-0.609V6.702c0-0.337,0.272-0.61,0.608-0.61h3.019c0.338,0,0.611,0.273,0.611,0.61s-0.273,0.609-0.611,0.609h-2.407v1.3H16.693z"/>
    </svg>
  );
}

export default GIFIcon;
