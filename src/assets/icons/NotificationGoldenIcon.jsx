import React from 'react';

function NotificationGoldenIcon(props) {
  return (
    <svg
      width="18"
      height="20"
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.00013 20.0085C10.199 20.0085 11.2315 19.2844 11.6845 18.2507H6.3158C6.7688 19.2844 7.80126 20.0085 9.00013 20.0085Z"
        fill="url(#paint0_linear_8246_2348)"
      />
      <path
        d="M15.0547 9.6909V8.40723C15.0547 5.6798 13.2418 3.36812 10.7579 2.61309V1.7666C10.7579 0.797344 9.9693 0.00878906 9.00004 0.00878906C8.03079 0.00878906 7.24223 0.797344 7.24223 1.7666V2.61309C4.75825 3.36812 2.94536 5.67977 2.94536 8.40723V9.6909C2.94536 12.0867 2.03215 14.3584 0.373991 16.0876C0.211491 16.2571 0.165827 16.5071 0.257936 16.723C0.350046 16.939 0.562155 17.0791 0.796921 17.0791H17.2032C17.4379 17.0791 17.65 16.939 17.7421 16.723C17.8342 16.5071 17.7886 16.2571 17.6261 16.0876C15.9679 14.3584 15.0547 12.0866 15.0547 9.6909ZM9.58598 2.38098C9.39313 2.36238 9.1977 2.35254 9.00004 2.35254C8.80239 2.35254 8.60696 2.36238 8.41411 2.38098V1.7666C8.41411 1.44352 8.67696 1.18066 9.00004 1.18066C9.32313 1.18066 9.58598 1.44352 9.58598 1.7666V2.38098Z"
        fill="url(#paint1_linear_8246_2348)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_8246_2348"
          x1="9.00003"
          y1="20.0085"
          x2="9.00003"
          y2="0.00214817"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_8246_2348"
          x1="9.00003"
          y1="20.0085"
          x2="9.00003"
          y2="0.00214817"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default NotificationGoldenIcon;
