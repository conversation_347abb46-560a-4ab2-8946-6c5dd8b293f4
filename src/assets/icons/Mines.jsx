// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function MinesIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M19.811,6.533l-0.436-0.435c-0.255-0.254-0.667-0.254-0.923,0c-0.254,0.255-0.254,0.667,0,0.922l0.437,0.435c0.127,0.127,0.293,0.191,0.461,0.191c0.166,0,0.334-0.064,0.461-0.191C20.064,7.201,20.064,6.788,19.811,6.533z"/>
        <path d="M17.201,6.098c-0.256-0.255-0.667-0.255-0.922,0l-0.436,0.435c-0.255,0.255-0.255,0.668,0,0.922c0.127,0.127,0.295,0.191,0.461,0.191s0.334-0.064,0.461-0.191l0.436-0.435C17.456,6.766,17.456,6.353,17.201,6.098z"/>
        <path d="M19.811,3.49c-0.127-0.127-0.295-0.191-0.461-0.191c-0.168,0-0.334,0.064-0.461,0.191l-0.437,0.435c-0.254,0.255-0.254,0.667,0,0.922c0.256,0.254,0.668,0.254,0.923,0l0.436-0.435C20.064,4.157,20.064,3.744,19.811,3.49z"/>
        <path d="M17.253,4.083l-0.922-0.922c-0.763-0.763-2.004-0.763-2.769,0l-0.307,0.308l-0.922-0.922c-0.256-0.255-0.668-0.255-0.923,0L9.67,4.287C8.822,3.928,7.902,3.74,6.953,3.74c-1.86,0-3.606,0.723-4.919,2.034c-2.713,2.712-2.713,7.125,0,9.837c1.313,1.313,3.059,2.033,4.919,2.033c1.86,0,3.607-0.721,4.92-2.033c1.313-1.312,2.034-3.06,2.034-4.92c0-0.948-0.188-1.868-0.548-2.715l1.742-1.741c0.254-0.254,0.254-0.667,0-0.922L14.18,4.39l0.307-0.307c0.255-0.254,0.668-0.254,0.924,0l0.922,0.922c0.127,0.127,0.295,0.191,0.461,0.191c0.167,0,0.334-0.064,0.461-0.191C17.508,4.75,17.508,4.337,17.253,4.083z M10.03,13.762c-0.822,0.822-1.914,1.275-3.074,1.275s-2.251-0.453-3.072-1.275c-0.256-0.254-0.256-0.666,0-0.922c0.254-0.254,0.667-0.254,0.922,0c0.575,0.576,1.34,0.893,2.15,0.893c0.813,0,1.576-0.315,2.151-0.893c0.255-0.254,0.667-0.254,0.923,0C10.284,13.096,10.284,13.508,10.03,13.762z"/>
      </g>
    </svg>
  );
}

export default MinesIcon;
