// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCircleUpIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20C4.486,20,0,15.516,0,10C0,4.486,4.486,0,10,0c5.516,0,10,4.486,10,10C20,15.516,15.516,20,10,20z M10,1.512C5.32,1.512,1.512,5.32,1.512,10c0,4.682,3.808,8.488,8.488,8.488c4.682,0,8.488-3.809,8.488-8.488C18.488,5.32,14.682,1.512,10,1.512z"/>
        <path d="M8.381,14.325v-4.81l-1.333,1.322c-0.302,0.301-0.704,0.467-1.131,0.467s-0.83-0.166-1.131-0.467c-0.624-0.619-0.624-1.627,0-2.247L8.85,4.556C9.15,4.256,9.552,4.09,9.98,4.09h0.037c0.426,0,0.828,0.165,1.129,0.464l4.064,4.037c0.303,0.3,0.471,0.699,0.471,1.125c0,0.427-0.168,0.824-0.469,1.122c-0.302,0.301-0.704,0.469-1.132,0.469c-0.429,0-0.828-0.166-1.132-0.467l-1.332-1.322v4.808c0,0.874-0.727,1.584-1.619,1.584C9.107,15.909,8.381,15.199,8.381,14.325z"/>
      </g>
    </svg>
  );
}

export default ArrowCircleUpIcon;
