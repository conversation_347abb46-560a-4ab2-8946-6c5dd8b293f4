// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CopyIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M9.414,8.203h4.297L9.414,4.98V8.203z"/>
        <path d="M2.969,20h10.547c0.324,0,0.586-0.262,0.586-0.586V9.375H8.828c-0.324,0-0.586-0.262-0.586-0.586V4.688H2.969c-0.324,0-0.586,0.262-0.586,0.586v14.141C2.383,19.738,2.645,20,2.969,20z"/>
        <path d="M17.617,14.688v-10h-5.273c-0.324,0-0.586-0.262-0.586-0.586V0H6.484C6.16,0,5.898,0.262,5.898,0.586v2.93h2.93c0.378,0,0.753,0.125,1.055,0.353l4.688,3.515c0.438,0.329,0.703,0.855,0.703,1.406v6.483h1.758C17.355,15.273,17.617,15.012,17.617,14.688z"/>
        <path d="M12.93,3.516h4.297L12.93,0.293V3.516z"/>
      </g>
    </svg>
  );
}

export default CopyIcon;
