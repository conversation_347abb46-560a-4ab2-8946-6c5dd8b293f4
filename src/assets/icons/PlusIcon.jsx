// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function PlusIcon(props) {
  return (
    <svg width="10" height="11" viewBox="0 0 10 11" fill="#578495" xmlns="http://www.w3.org/2000/svg"  {...props}>
      <g clipPath="url(#clip0_3112_11369)">
        <path d="M8.92053 4.66377H5.83607V1.57931C5.83607 1.11752 5.46172 0.743164 4.99992 0.743164C4.53813 0.743164 4.16377 1.11752 4.16377 1.57931V4.66377H1.07931C0.617517 4.66377 0.243164 5.03813 0.243164 5.49992C0.243164 5.96172 0.617517 6.33607 1.07931 6.33607H4.16377V9.42053C4.16377 9.88232 4.53813 10.2567 4.99992 10.2567C5.46172 10.2567 5.83607 9.88232 5.83607 9.42053V6.33607H8.92053C9.38232 6.33607 9.75668 5.96172 9.75668 5.49992C9.75668 5.03813 9.38232 4.66377 8.92053 4.66377Z" />
      </g>
      <defs>
        <clipPath id="clip0_3112_11369">
          <rect width="9.51351" height="9.51351" fill="white" transform="translate(0.243164 0.743164)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default PlusIcon;
