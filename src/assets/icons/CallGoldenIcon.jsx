import React from 'react';

function CallGoldenIcon(props) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.1156 12.678C15.9763 13.7366 15.4564 14.7083 14.6531 15.4116C13.8497 16.1149 12.8177 16.5018 11.75 16.4998C5.54688 16.4998 0.500007 11.453 0.500007 5.24984C0.498093 4.1821 0.884919 3.15017 1.58824 2.34679C2.29155 1.54341 3.26326 1.02352 4.32188 0.884217C4.58958 0.85153 4.86067 0.906297 5.09468 1.04034C5.3287 1.17438 5.51309 1.38052 5.62032 1.62797L7.27032 5.31156V5.32094C7.35242 5.51035 7.38633 5.71715 7.36901 5.92287C7.3517 6.12859 7.2837 6.32681 7.1711 6.49984C7.15704 6.52094 7.14219 6.54047 7.12657 6.56L5.50001 8.48812C6.08516 9.67719 7.32891 10.91 8.5336 11.4967L10.4352 9.87875C10.4538 9.86305 10.4734 9.84844 10.4938 9.835C10.6666 9.71969 10.8655 9.6493 11.0725 9.6302C11.2794 9.61111 11.4878 9.6439 11.6789 9.72562L11.6891 9.73031L15.3695 11.3795C15.6174 11.4864 15.8241 11.6706 15.9585 11.9047C16.093 12.1387 16.1481 12.41 16.1156 12.678Z"
        fill="url(#paint0_linear_8049_11094)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_8049_11094"
          x1="8.31237"
          y1="16.4998"
          x2="8.31237"
          y2="0.869812"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default CallGoldenIcon;
