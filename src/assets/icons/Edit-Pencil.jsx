// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function EditPencilIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M6.4,19.733l8.447-8.447L8.715,5.151l-8.448,8.449C0.097,13.771,0,14.003,0,14.243v4.849c0,0.502,0.407,0.909,0.909,0.909h4.849C5.998,20.001,6.23,19.905,6.4,19.733z"/>
        <path d="M19.734,6.4c0.354-0.355,0.354-0.931,0-1.286l-4.851-4.849c-0.354-0.355-0.93-0.355-1.284,0l-3.601,3.6L16.135,10L19.734,6.4z"/>
      </g>
    </svg>
  );
}

export default EditPencilIcon;
