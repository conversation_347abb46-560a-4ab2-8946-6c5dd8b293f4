// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function EmojiIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M5.355,11.626C5.648,13.928,7.62,15.714,10,15.714s4.352-1.786,4.645-4.088H5.355z"/>
        <path d="M17.072,2.929C15.184,1.04,12.671,0,10,0S4.816,1.04,2.928,2.929C1.04,4.817,0,7.328,0,9.999c0,2.672,1.04,5.183,2.928,7.071C4.816,18.959,7.329,20,10,20s5.184-1.041,7.072-2.93C18.96,15.182,20,12.671,20,9.999C20,7.328,18.96,4.817,17.072,2.929z M12.163,7.358c0.976-0.975,2.562-0.975,3.536,0c0.232,0.232,0.232,0.609,0,0.842c-0.116,0.116-0.27,0.175-0.42,0.175c-0.152,0-0.307-0.059-0.424-0.175c-0.51-0.511-1.34-0.511-1.852,0c-0.23,0.232-0.609,0.232-0.842,0C11.93,7.968,11.932,7.591,12.163,7.358z M4.301,7.358c0.976-0.975,2.561-0.975,3.536,0c0.232,0.232,0.232,0.609,0,0.842C7.72,8.316,7.567,8.375,7.416,8.375c-0.152,0-0.305-0.059-0.421-0.175c-0.511-0.51-1.342-0.511-1.852,0C4.909,8.432,4.532,8.432,4.3,8.2C4.068,7.968,4.068,7.591,4.301,7.358z M10,16.904c-3.238,0-5.874-2.635-5.874-5.873c0-0.329,0.266-0.596,0.595-0.596h10.558c0.328,0,0.596,0.267,0.596,0.596C15.874,14.27,13.238,16.904,10,16.904z"/>
      </g>
    </svg>
  );
}

export default EmojiIcon;
