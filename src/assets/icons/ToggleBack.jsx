// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ToggleBack(props) {
  return (
    <svg
      width="18"
      height="20"
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.0711 17.0799C15.6725 18.4784 13.8907 19.4308 11.9509 19.8166C10.0111 20.2025 8.00043 20.0045 6.17317 19.2476C4.3459 18.4907 2.78412 17.209 1.6853 15.5645C0.58649 13.92 1.21811e-07 11.9866 0 10.0088C-1.21811e-07 8.03098 0.586489 6.09758 1.6853 4.45309C2.78412 2.8086 4.3459 1.52687 6.17316 0.769994C8.00043 0.0131176 10.0111 -0.184916 11.9509 0.200936C13.8907 0.586788 15.6725 1.5392 17.0711 2.93772L10 10.0088L17.0711 17.0799Z"
        fill="#072843"
      />
    </svg>
  );
}

export default ToggleBack;
