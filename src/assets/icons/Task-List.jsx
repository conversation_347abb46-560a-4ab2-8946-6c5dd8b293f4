// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function TaskListIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10.023,15.908c1.938,0,3.516-1.577,3.516-3.516c0-1.939-1.576-3.517-3.516-3.517c-1.939,0-3.516,1.577-3.516,3.517C6.508,14.331,8.085,15.908,10.023,15.908z M7.853,11.979c0.229-0.229,0.6-0.229,0.828,0l0.758,0.758l1.931-1.93c0.229-0.229,0.6-0.229,0.827,0c0.229,0.229,0.229,0.601,0,0.828l-2.344,2.344c-0.114,0.114-0.264,0.172-0.414,0.172s-0.3-0.058-0.414-0.172l-1.172-1.172C7.624,12.578,7.624,12.207,7.853,11.979z" />
        <path d="M2.968,1.172c-0.324,0-0.586,0.262-0.586,0.586v17.656C2.382,19.738,2.645,20,2.968,20h14.063c0.324,0,0.586-0.262,0.586-0.586V1.758c0-0.324-0.262-0.586-0.586-0.586h-2.93v2.93c0,0.323-0.262,0.586-0.586,0.586H6.483c-0.324,0-0.586-0.263-0.586-0.586v-2.93H2.968z M10.023,7.666c2.586,0,4.688,2.142,4.688,4.727c0,2.584-2.102,4.688-4.688,4.688c-2.584,0-4.688-2.104-4.688-4.688C5.336,9.808,7.439,7.666,10.023,7.666z" />
        <path d="M11.658,1.172C11.416,0.49,10.764,0,10,0S8.583,0.49,8.341,1.172H7.069v2.344h5.86V1.172H11.658z" />
      </g>
    </svg>
  );
}

export default TaskListIcon;
