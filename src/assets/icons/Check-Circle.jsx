// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CheckCircleIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M10,0C4.507,0,0,4.507,0,10c0,5.492,4.507,10,10,10s10-4.508,10-10C20,4.507,15.493,0,10,0z M8.79,14.548L4.411,10.17l1.657-1.656l2.798,2.799l5.618-5.108l1.578,1.733L8.79,14.548z"/>
    </svg>
  );
}

export default CheckCircleIcon;
