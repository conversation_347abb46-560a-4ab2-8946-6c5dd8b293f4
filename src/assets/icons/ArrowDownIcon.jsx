// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowDownIcon(props) {
  return (
    <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
    >
      <path d="M10.0011 13.6639C10.2316 13.6639 10.462 13.5759 10.6376 13.4003L16.1659 7.87199C16.5176 7.52031 16.5176 6.95014 16.1659 6.59861C15.8144 6.24708 15.2443 6.24708 14.8926 6.59861L10.0011 11.4904L5.10958 6.59878C4.75791 6.24725 4.1879 6.24725 3.8364 6.59878C3.48456 6.95031 3.48456 7.52049 3.8364 7.87216L9.36462 13.4005C9.54039 13.5761 9.77078 13.6639 10.0011 13.6639Z" fill="#CECAB4" />
    </svg>
  );
}

export default ArrowDownIcon;
