// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function BonusPlayIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M20,1.786C20,0.799,19.201,0,18.215,0H1.786C0.799,0,0,0.799,0,1.786v16.429C0,19.201,0.799,20,1.786,20h16.429C19.201,20,20,19.201,20,18.215V1.786z M13.49,10.859l-5.658,3.692C7.109,15.026,6.5,14.706,6.5,13.842V6.158c0-0.865,0.608-1.185,1.333-0.711l5.647,3.692C14.203,9.612,14.213,10.387,13.49,10.859z"/>
    </svg>
  );
}

export default BonusPlayIcon;
