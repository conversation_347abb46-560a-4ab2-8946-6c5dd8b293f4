// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FriendChat(props) {
  return (
    <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.06278 0C11.7123 10.5201 2.16116 15.3333 2.16116 15.3333L4.03399 9.77976L0 5.84708L5.57693 5.63408L8.06278 0ZM3.1742 3.82619C3.1742 3.18459 3.69371 2.66508 4.3353 2.66508C4.9769 2.66508 5.49641 3.18459 5.49641 3.82619C5.49641 4.46778 4.9769 4.98729 4.3353 4.98729C3.69371 4.98729 3.1742 4.46778 3.1742 3.82619Z"
      fill="white"
    />
    <path
      d="M12.2863 4.98713C12.9279 4.98713 13.4474 4.46762 13.4474 3.82603C13.4474 3.18443 12.9279 2.66492 12.2863 2.66492C11.6447 2.66492 11.1252 3.18443 11.1252 3.82603C11.1252 4.46762 11.6447 4.98713 12.2863 4.98713ZM16.4762 5.84692L10.8993 5.63392L8.42639 0.0258179C11.3668 6.88334 8.4082 11.0498 8.4082 11.0498L14.5254 15.3332L12.4448 9.7796L16.4788 5.84692H16.4762Z"
      fill="white"
    />
  </svg>
);
}

export default FriendChat;
