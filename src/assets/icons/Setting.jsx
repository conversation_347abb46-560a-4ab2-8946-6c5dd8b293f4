// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function SettingIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M20,10c0-0.744-0.536-1.537-1.197-1.771c-0.66-0.234-1.301-0.807-1.486-1.258c-0.188-0.452-0.139-1.311,0.163-1.944s0.118-1.573-0.407-2.099s-1.466-0.708-2.1-0.407c-0.633,0.301-1.491,0.351-1.941,0.164c-0.451-0.186-1.023-0.828-1.259-1.488S10.744,0,10,0C9.256,0,8.462,0.536,8.227,1.196C7.992,1.857,7.419,2.499,6.969,2.685C6.519,2.871,5.66,2.822,5.027,2.521S3.454,2.402,2.928,2.928S2.219,4.394,2.521,5.027S2.87,6.519,2.683,6.97S1.857,7.996,1.196,8.229S0,9.256,0,10c0,0.744,0.536,1.537,1.196,1.772s1.3,0.81,1.487,1.259c0.187,0.451,0.138,1.309-0.163,1.941c-0.301,0.634-0.119,1.574,0.407,2.1s1.466,0.709,2.099,0.407s1.492-0.351,1.942-0.163c0.451,0.187,1.023,0.83,1.258,1.488C8.462,19.465,9.256,20,10,20c0.744,0,1.537-0.535,1.772-1.195c0.235-0.658,0.808-1.301,1.258-1.488s1.31-0.139,1.942,0.163c0.634,0.302,1.574,0.118,2.1-0.407s0.709-1.466,0.407-2.099c-0.302-0.634-0.351-1.491-0.165-1.942c0.187-0.45,0.828-1.023,1.489-1.258C19.465,11.537,20,10.744,20,10zM10,13.67c-2.029,0-3.669-1.641-3.669-3.666c0-2.029,1.641-3.669,3.669-3.669c2.024,0,3.666,1.64,3.666,3.669C13.666,12.029,12.025,13.67,10,13.67z" />
    </svg>
  );
}

export default SettingIcon;
