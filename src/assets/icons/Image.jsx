// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ImageIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M11.484,7.813c1.291,0,2.344-1.051,2.344-2.344c-0.129-3.109-4.561-3.108-4.688,0C9.141,6.761,10.191,7.813,11.484,7.813zM11.484,4.688c0.431,0,0.781,0.351,0.781,0.781c-0.043,1.037-1.521,1.036-1.563,0C10.703,5.038,11.055,4.688,11.484,4.688zM19.219,0H0.781C0.35,0,0,0.35,0,0.781v18.438C0,19.65,0.35,20,0.781,20h18.438C19.65,20,20,19.65,20,19.219V0.781C20,0.35,19.648,0,19.219,0z M18.438,1.563v12.259l-3.766-3.75c-0.304-0.305-0.798-0.305-1.104,0L11.6,12.034L7.165,7.721C6.858,7.424,6.371,7.427,6.069,7.729l-4.506,4.492V1.563H18.438z M1.563,18.438v-4.012l5.065-5.048l3.865,3.761L8.081,15.54c-0.306,0.306-0.308,0.8-0.002,1.104c0.305,0.307,0.8,0.308,1.104,0.002l4.938-4.918l4.316,4.297v2.412H1.563L1.563,18.438z"/>
    </svg>
  );
}

export default ImageIcon;
