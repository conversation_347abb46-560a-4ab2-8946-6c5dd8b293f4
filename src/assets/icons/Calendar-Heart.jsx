// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CalendarHeartIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M18.043,1.739h-2.391V0.652C15.652,0.292,15.359,0,15,0c-0.361,0-0.652,0.292-0.652,0.652v1.087H5.652V0.652C5.652,0.292,5.359,0,5,0C4.639,0,4.348,0.292,4.348,0.652v1.087H1.956C0.877,1.739,0,2.617,0,3.696v2.826h20V3.696C20,2.617,19.121,1.739,18.043,1.739z"/>
        <path d="M0,7.826v10.217C0,19.123,0.877,20,1.956,20h16.087C19.121,20,20,19.123,20,18.043V7.826H0z M10.387,17.07c-0.225,0.189-0.551,0.189-0.775,0c-2.26-1.893-3.756-2.809-3.756-4.375c0-1.248,0.91-2.225,2.072-2.225c0.631,0,1.141,0.26,1.559,0.795L10,11.922l0.514-0.656c0.418-0.535,0.928-0.795,1.559-0.795c1.16,0,2.07,0.977,2.07,2.225C14.143,14.264,12.646,15.18,10.387,17.07z"/>
      </g>
    </svg>
  );
}

export default CalendarHeartIcon;
