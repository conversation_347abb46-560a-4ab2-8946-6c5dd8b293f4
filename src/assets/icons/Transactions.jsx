// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function TransactionsIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10.586,1.211H20v2.344h-9.414V1.211z"/>
        <path d="M9.414,4.727V1.211H0v17.578h20V4.727H9.414z M4.141,3.555h1.172v1.172H4.141V3.555z M1.797,3.555h1.172v1.172H1.797V3.555z M7.07,9.414H5.313c-0.324,0-0.586,0.263-0.586,0.586s0.263,0.586,0.586,0.586c0.969,0,1.758,0.789,1.758,1.758c0,0.764-0.491,1.407-1.172,1.65v1.279H4.727v-1.172H3.555V12.93h1.758c0.323,0,0.586-0.263,0.586-0.586c0-0.322-0.263-0.586-0.586-0.586c-0.969,0-1.758-0.789-1.758-1.758c0-0.763,0.491-1.407,1.172-1.65V7.07h1.172v1.172H7.07V9.414zM7.656,4.727H6.484V3.555h1.172V4.727z M10,9.414h4.445l-0.758-0.758l0.828-0.828l2.758,2.758H10V9.414z M17.617,14.102h-4.445l0.758,0.758l-0.828,0.828l-2.758-2.758h7.274L17.617,14.102L17.617,14.102z"/>
      </g>
    </svg>
  );
}

export default TransactionsIcon;
