// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function HeartFillIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M1.859,11.536l7.059,6.59c0.293,0.273,0.68,0.426,1.082,0.426s0.789-0.152,1.082-0.426l7.059-6.59C19.328,10.431,20,8.88,20,7.259V7.032c0-2.73-1.973-5.059-4.664-5.508c-1.781-0.297-3.594,0.285-4.867,1.559L10,3.552L9.531,3.083C8.258,1.81,6.445,1.228,4.664,1.524C1.973,1.974,0,4.302,0,7.032v0.227C0,8.88,0.672,10.431,1.859,11.536z"/>
    </svg>
  );
}

export default HeartFillIcon;
