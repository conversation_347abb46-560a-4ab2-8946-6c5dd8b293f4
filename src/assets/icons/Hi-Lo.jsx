// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function HiLoIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M6.286,0.405C5.741-0.14,4.857-0.134,4.319,0.418L1.241,3.581C0.389,4.457,1.007,5.926,2.23,5.926h1.09v12.707C3.32,19.387,3.934,20,4.688,20h1.328c0.754,0,1.367-0.613,1.367-1.367V5.926h1.09c1.228,0,1.846-1.489,0.977-2.358L6.286,0.405z"/>
        <path d="M17.77,14.074h-1.09V1.367C16.68,0.613,16.066,0,15.313,0h-1.328c-0.754,0-1.366,0.613-1.366,1.367v12.707h-1.091c-1.229,0-1.845,1.488-0.977,2.357l3.163,3.164c0.544,0.543,1.429,0.538,1.968-0.015l3.078-3.163C19.611,15.543,18.992,14.074,17.77,14.074z"/>
      </g>
    </svg>
  );
}

export default HiLoIcon;
