// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCollapseRightIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M0,10c0,0.477,0.504,0.953,1.009,0.953h12.706L11.9,12.667c-0.403,0.38-0.403,0.952,0,1.333c0.201,0.189,0.504,0.286,0.705,0.286s0.504-0.097,0.705-0.286l3.529-3.333c0.403-0.382,0.403-0.952,0-1.334L13.311,6c-0.401-0.381-1.009-0.381-1.41,0c-0.403,0.381-0.403,0.952,0,1.333l1.814,1.714H1.009C0.504,9.047,0,9.523,0,10z"/>
        <path d="M19.001,19.001C19.602,19.001,20,18.6,20,18V2c0-0.601-0.398-1.001-0.999-1.001S18,1.399,18,2v16C18,18.6,18.4,19.001,19.001,19.001z"/>
      </g>
    </svg>
  );
}

export default ArrowCollapseRightIcon;
