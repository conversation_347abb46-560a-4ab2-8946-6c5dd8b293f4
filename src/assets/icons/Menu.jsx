// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function MenuIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,11.758c0.971,0,1.758-0.787,1.758-1.758S10.971,8.242,10,8.242S8.242,9.029,8.242,10S9.029,11.758,10,11.758z"/>
        <path d="M18.242,5.898h-4.141v-4.14C14.102,0.789,13.313,0,12.344,0H7.656C6.688,0,5.897,0.789,5.897,1.758v4.141h-4.14C0.789,5.898,0,6.687,0,7.656v4.688c0,0.969,0.789,1.758,1.758,1.758h4.141v4.142C5.897,19.211,6.688,20,7.656,20h4.688c0.969,0,1.758-0.789,1.758-1.758v-4.141h4.141c0.969,0,1.758-0.789,1.758-1.759V7.656C20,6.687,19.211,5.898,18.242,5.898zM4.555,10.758c0.229,0.229,0.229,0.6,0,0.828c-0.229,0.229-0.6,0.229-0.828,0l-1.172-1.172c-0.229-0.229-0.229-0.6,0-0.828l1.172-1.172c0.229-0.229,0.601-0.229,0.828,0c0.229,0.229,0.229,0.6,0,0.828L3.797,10L4.555,10.758z M8.414,3.727l1.172-1.172c0.229-0.229,0.6-0.229,0.828,0l1.172,1.172c0.229,0.229,0.229,0.6,0,0.828c-0.229,0.229-0.6,0.229-0.828,0L10,3.797L9.242,4.554c-0.229,0.229-0.6,0.229-0.828,0C8.186,4.326,8.186,3.955,8.414,3.727z M11.586,16.273l-1.172,1.172C10.3,17.561,10.148,17.617,10,17.617s-0.3-0.057-0.414-0.172l-1.172-1.172c-0.229-0.229-0.229-0.601,0-0.828c0.229-0.229,0.6-0.229,0.828,0L10,16.203l0.758-0.758c0.229-0.229,0.6-0.229,0.828,0C11.814,15.674,11.814,16.045,11.586,16.273zM10,12.93c-1.615,0-2.931-1.313-2.931-2.93S8.385,7.07,10,7.07s2.93,1.314,2.93,2.93S11.615,12.93,10,12.93z M17.445,10.414l-1.172,1.172c-0.23,0.229-0.602,0.229-0.828,0c-0.229-0.229-0.23-0.6,0-0.828L16.203,10l-0.758-0.758c-0.23-0.229-0.23-0.6,0-0.828c0.229-0.229,0.6-0.229,0.828,0l1.172,1.172C17.674,9.814,17.674,10.186,17.445,10.414z"/>
      </g>
    </svg>
  );
}

export default MenuIcon;
