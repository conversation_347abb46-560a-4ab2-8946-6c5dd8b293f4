// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function SearchIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_5001_8107)">
        <path
          d="M7.28533 0C11.3009 0 14.5677 3.26679 14.5677 7.28202C14.5677 9.03514 13.9482 10.6879 12.8322 12.0008L13.9814 13.1504L14.3959 12.7355C14.6248 12.507 14.9959 12.507 15.2244 12.7355L19.8314 17.3425C20.0599 17.5711 20.0603 17.9422 19.8314 18.1711L18.174 19.8285C17.9451 20.057 17.5744 20.0574 17.3455 19.8285L12.7388 15.2215C12.5099 14.9929 12.5099 14.6218 12.7388 14.3929L13.1529 13.9789L12.0037 12.8293C10.6877 13.948 9.03376 14.5648 7.28533 14.5648C3.26971 14.5648 0.00292206 11.298 0.00292206 7.28202C0.00292206 3.26679 3.26971 0 7.28533 0ZM7.28533 3.51562C9.36228 3.51562 11.0521 5.20546 11.0521 7.28241C11.0521 7.60584 11.3146 7.86834 11.6381 7.86834C11.9619 7.86834 12.224 7.60584 12.224 7.28241C12.224 4.55937 10.0084 2.34375 7.28533 2.34375C6.96189 2.34375 6.69939 2.60624 6.69939 2.92968C6.69939 3.25312 6.96189 3.51562 7.28533 3.51562Z"
          fill="#578495"
        />
      </g>
      <defs>
        <clipPath id="clip0_5001_8107">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="matrix(-1 0 0 1 20.0029 0)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default SearchIcon;
