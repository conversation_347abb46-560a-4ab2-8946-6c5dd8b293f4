// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ShareIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M10,0C4.47,0,0,4.468,0,10c0,5.53,4.468,10,10,10c5.53,0,10-4.469,10-10C20,4.47,15.531,0,10,0z M8.242,10c0,0.221-0.031,0.435-0.089,0.637l3.112,1.816c0.426-0.43,1.014-0.695,1.664-0.695c1.292,0,2.344,1.052,2.344,2.344s-1.052,2.344-2.344,2.344s-2.344-1.052-2.344-2.344c0-0.221,0.031-0.435,0.089-0.637l-3.113-1.816c-0.425,0.43-1.014,0.695-1.664,0.695c-1.292,0-2.344-1.052-2.344-2.344s1.052-2.344,2.344-2.344c0.65,0,1.239,0.266,1.664,0.695l3.113-1.815c-0.058-0.203-0.089-0.417-0.089-0.637c0-1.292,1.052-2.344,2.344-2.344s2.344,1.052,2.344,2.344s-1.052,2.344-2.344,2.344c-0.65,0-1.238-0.266-1.664-0.695L8.153,9.363C8.211,9.565,8.242,9.779,8.242,10z" />
    </svg>
  );
}

export default ShareIcon;
