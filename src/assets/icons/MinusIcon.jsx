// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function MinusIcon(props) {
  return (
    <svg width="10" height="11" viewBox="0 0 10 11" fill="#578495" xmlns="http://www.w3.org/2000/svg"  {...props}>
      <g clipPath="url(#clip0_3112_11365)">
        <path d="M8.92053 4.66382H1.07931C0.617517 4.66382 0.243164 5.03817 0.243164 5.49997C0.243164 5.96176 0.617517 6.33612 1.07931 6.33612H8.92053C9.38232 6.33612 9.75668 5.96176 9.75668 5.49997C9.75668 5.03817 9.38232 4.66382 8.92053 4.66382Z" />
      </g>
      <defs>
        <clipPath id="clip0_3112_11365">
          <rect width="9.51351" height="9.51351" fill="white" transform="translate(0.243164 0.743164)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default MinusIcon;
