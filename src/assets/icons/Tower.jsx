// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function TowerIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M8.828,15.273V20h2.344v-4.727c0-0.646-0.525-1.172-1.172-1.172S8.828,14.627,8.828,15.273z"/>
        <path d="M16.445,0H12.93v2.344h-1.172V0H8.242v2.344H7.069V0H3.555v4.727h12.891V0L16.445,0z"/>
        <path d="M16.221,5.898H3.777L4.364,7.07h11.271L16.221,5.898z"/>
        <path d="M15.273,20V8.242H4.727V20h2.931v-4.727c0-1.293,1.051-2.344,2.344-2.344s2.344,1.051,2.344,2.344V20H15.273zM11.758,11.758H8.242v-1.172h3.516V11.758z"/>
      </g>
    </svg>
  );
}

export default TowerIcon;
