// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function SortIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M18.677,3.354l-3.126-3.125C15.4,0.078,15.198,0,14.999,0c-0.197,0-0.401,0.079-0.554,0.229l-3.124,3.125c-0.306,0.305-0.306,0.8,0,1.105c0.305,0.305,0.799,0.305,1.104,0l1.791-1.792v16.552c0,0.432,0.35,0.781,0.781,0.781c0.434,0,0.779-0.35,0.779-0.781V2.667l1.793,1.792c0.306,0.305,0.801,0.305,1.104,0C18.982,4.153,18.982,3.659,18.677,3.354z"/>
        <path d="M7.571,15.542l-1.792,1.791V0.781C5.779,0.349,5.431,0,5,0C4.567,0,4.219,0.349,4.219,0.781v16.552l-1.791-1.791c-0.307-0.308-0.801-0.308-1.104,0c-0.307,0.304-0.307,0.799,0,1.104l3.124,3.125C4.589,19.913,4.791,20,5,20c0.195,0,0.402-0.078,0.552-0.229l3.126-3.127c0.306-0.305,0.306-0.799,0-1.104C8.373,15.234,7.878,15.234,7.571,15.542z"/>
      </g>
    </svg>
  );
}

export default SortIcon;
