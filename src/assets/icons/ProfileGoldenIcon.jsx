import React from 'react';

function ProfileGoldenIcon(props) {
  return (
    <svg
      width="21"
      height="21"
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_8049_10439)">
        <path
          d="M10.5004 9.56746C12.9711 9.56746 14.974 7.56453 14.974 5.09379C14.974 2.62305 12.9711 0.620117 10.5004 0.620117C8.02962 0.620117 6.02669 2.62305 6.02669 5.09379C6.02669 7.56453 8.02962 9.56746 10.5004 9.56746Z"
          fill="url(#paint0_linear_8049_10439)"
        />
        <path
          d="M10.5002 10.6201C6.14008 10.6201 2.60547 14.1548 2.60547 18.5148C2.60547 19.6775 3.54805 20.6201 4.71074 20.6201H16.2897C17.4524 20.6201 18.395 19.6775 18.395 18.5148C18.3949 14.1548 14.8603 10.6201 10.5002 10.6201Z"
          fill="url(#paint1_linear_8049_10439)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_8049_10439"
          x1="10.5002"
          y1="20.6201"
          x2="10.5002"
          y2="0.613476"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_8049_10439"
          x1="10.5002"
          y1="20.6201"
          x2="10.5002"
          y2="0.613476"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#BB7733" />
          <stop offset="0.07" stopColor="#C17F37" />
          <stop offset="0.2" stopColor="#D19844" />
          <stop offset="0.33" stopColor="#EABB58" />
          <stop offset="0.47" stopColor="#EECE6C" />
          <stop offset="0.61" stopColor="#F2DD7B" />
          <stop offset="0.82" stopColor="#ECC764" />
          <stop offset="0.99" stopColor="#EABB58" />
        </linearGradient>
        <clipPath id="clip0_8049_10439">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.5 0.620117)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default ProfileGoldenIcon;
