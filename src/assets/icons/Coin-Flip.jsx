// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CoinFlipIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,3.525C6.43,3.525,3.525,6.43,3.525,10S6.43,16.475,10,16.475S16.475,13.57,16.475,10S13.57,3.525,10,3.525z M12.922,10L10,12.922L7.079,10L10,7.079L12.922,10z"/>
        <path d="M17.071,2.929C15.182,1.04,12.671,0,10,0S4.817,1.04,2.929,2.929C1.04,4.818,0,7.329,0,10s1.04,5.182,2.929,7.072C4.817,18.961,7.329,20,10,20s5.182-1.039,7.071-2.928C18.96,15.182,20,12.671,20,10S18.96,4.818,17.071,2.929z M10,17.646c-4.216,0-7.646-3.43-7.646-7.646c0-4.216,3.431-7.646,7.646-7.646c4.217,0,7.646,3.431,7.646,7.646C17.646,14.217,14.217,17.646,10,17.646z"/>
      </g>
    </svg>
  );
}

export default CoinFlipIcon;
