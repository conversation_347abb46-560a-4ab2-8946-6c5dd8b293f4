import React from 'react';

function FriendGoldenIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 522 512"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M304.994 228.345C316.671 239.91 326.84 253.138 335.447 268.007C357.207 253.645 382.912 245.327 410.43 245.327C422.086 245.327 433.412 246.828 444.257 249.638C426.821 225.598 405.819 207.8 379.258 194.621C359.343 213.76 333.492 226.154 304.994 228.345Z"
        fill="url(#paint0_linear_340_1574)"
      />
      <path
        d="M268.799 394.514C268.799 352.872 285.087 315.168 311.296 288.078C296.531 261.123 276.382 240.816 250.207 226.527C228.639 242.167 202.532 251.337 174.426 251.337C146.401 251.337 120.363 242.22 98.8319 226.663C44.2975 257.73 10 322.385 10 397.042V511.571H322.722C289.898 484.224 268.799 441.917 268.799 394.514Z"
        fill="url(#paint1_linear_340_1574)"
      />
      <path
        d="M308.403 110.211C308.403 140.44 299.33 168.477 283.906 191.466L284.684 195.864C288.393 196.331 292.14 196.565 295.894 196.565C347.341 196.565 389.197 152.476 389.197 98.2831C389.197 44.0892 347.341 0 295.894 0C285.048 0 274.429 1.96009 264.41 5.73997C291.419 31.5778 308.403 68.8443 308.403 110.211Z"
        fill="url(#paint2_linear_340_1574)"
      />
      <path
        d="M198.213 216.188C253.816 202.343 288.237 143.639 275.093 85.0688C261.95 26.499 206.22 -9.7577 150.617 4.08732C95.0134 17.9323 60.593 76.6362 73.7365 135.206C86.8799 193.776 142.61 230.033 198.213 216.188Z"
        fill="url(#paint3_linear_340_1574)"
      />
      <path
        d="M522 394.944C522 330.399 472.149 277.888 410.874 277.888C349.599 277.888 299.748 330.399 299.748 394.944C299.748 459.489 349.599 512 410.874 512C472.149 512.001 522 459.49 522 394.944ZM346.148 411.01V378.878H395.621V326.765H426.126V378.878H475.598V411.01H426.126V463.124H395.621V411.01H346.148Z"
        fill="url(#paint4_linear_340_1574)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_340_1574"
          x1="266"
          y1="0"
          x2="266"
          y2="512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#CE9341" />
          <stop offset="0.222423" stopColor="#E5C063" />
          <stop offset="0.622749" stopColor="#EFD572" />
          <stop offset="1" stopColor="#D09744" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_340_1574"
          x1="266"
          y1="0"
          x2="266"
          y2="512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#CE9341" />
          <stop offset="0.222423" stopColor="#E5C063" />
          <stop offset="0.622749" stopColor="#EFD572" />
          <stop offset="1" stopColor="#D09744" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_340_1574"
          x1="266"
          y1="0"
          x2="266"
          y2="512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#CE9341" />
          <stop offset="0.222423" stopColor="#E5C063" />
          <stop offset="0.622749" stopColor="#EFD572" />
          <stop offset="1" stopColor="#D09744" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_340_1574"
          x1="266"
          y1="0"
          x2="266"
          y2="512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#CE9341" />
          <stop offset="0.222423" stopColor="#E5C063" />
          <stop offset="0.622749" stopColor="#EFD572" />
          <stop offset="1" stopColor="#D09744" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_340_1574"
          x1="266"
          y1="0"
          x2="266"
          y2="512"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#CE9341" />
          <stop offset="0.222423" stopColor="#E5C063" />
          <stop offset="0.622749" stopColor="#EFD572" />
          <stop offset="1" stopColor="#D09744" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default FriendGoldenIcon;
