// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function HeartIcon(props) {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" {...props}>
            <g clip-path="url(#clip0_8338_22454)">
                <path d="M22.0718 3.96959C19.4616 1.3601 15.2154 1.3601 12.6059 3.96959L11.9998 4.57534L11.394 3.96959C8.78456 1.35975 4.53796 1.35975 1.92847 3.96959C-0.628033 6.52609 -0.644634 10.5784 1.88997 13.396C4.20171 15.9648 11.0196 21.5147 11.3089 21.7496C11.5053 21.9093 11.7416 21.987 11.9765 21.987C11.9842 21.987 11.992 21.987 11.9994 21.9866C12.2424 21.9979 12.4872 21.9146 12.69 21.7496C12.9792 21.5147 19.7979 15.9648 22.1103 13.3956C24.6446 10.5784 24.628 6.52609 22.0718 3.96959ZM20.535 11.9782C18.7326 13.9805 13.7782 18.1102 11.9994 19.5756C10.2207 18.1105 5.26733 13.9812 3.46527 11.9785C1.69712 10.0133 1.68052 7.21449 3.42677 5.46824C4.31862 4.57675 5.48985 4.13065 6.66108 4.13065C7.83231 4.13065 9.00354 4.57639 9.89539 5.46824L11.2277 6.80053C11.3863 6.95912 11.5862 7.05378 11.796 7.08698C12.1365 7.1601 12.5059 7.06508 12.7708 6.80089L14.1038 5.46824C15.8879 3.6849 18.7898 3.68526 20.5728 5.46824C22.3191 7.21449 22.3025 10.0133 20.535 11.9782Z" fill="white" />
            </g>
            <defs>
                <clipPath id="clip0_8338_22454">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>

    );
}

export default HeartIcon;
