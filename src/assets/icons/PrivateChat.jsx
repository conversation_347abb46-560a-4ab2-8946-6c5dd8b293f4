// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function PrivateChatIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3146_46677)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.23672 12.5519C8.23672 11.1678 7.11063 10.0415 5.72635 10.0415C4.34207 10.0415 3.21582 11.1678 3.21582 12.5519C3.21582 13.9362 4.34207 15.0624 5.72635 15.0624C7.11063 15.0624 8.23672 13.9362 8.23672 12.5519Z"
          // fill="#578495"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.7265 15.0626C3.32324 15.0626 1.375 17.0108 1.375 19.4141C1.375 19.7377 1.6373 20 1.96094 20H9.49222C9.81586 20 10.0782 19.7377 10.0782 19.4141C10.0782 17.0108 8.12991 15.0626 5.7265 15.0626Z"
          // fill="#578495"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.7679 10.0415C14.3836 10.0415 13.2573 11.1678 13.2573 12.5519C13.2573 13.9362 14.3836 15.0624 15.7679 15.0624C17.1521 15.0624 18.2782 13.9362 18.2782 12.5519C18.2782 11.1678 17.1521 10.0415 15.7679 10.0415Z"
          // fill="#578495"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.7684 15.0626C13.3651 15.0626 11.4167 17.0108 11.4167 19.4141C11.4167 19.7377 11.6792 20 12.0027 20H19.534C19.8576 20 20.1199 19.7377 20.1199 19.4141C20.1199 17.0108 18.1717 15.0626 15.7684 15.0626Z"
          // fill="#578495"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.7677 8.70316C16.7829 8.70316 17.6088 7.8772 17.6088 6.86203V1.84113C17.6088 0.815277 16.7765 0 15.7677 0H5.72604C4.69943 0 3.88477 0.832977 3.88477 1.84113V6.86203C3.88477 7.8772 4.71072 8.70316 5.72604 8.70316H8.66443L10.2228 11.8198C10.322 12.0183 10.5249 12.1437 10.7468 12.1437C10.9688 12.1437 11.1716 12.0183 11.2709 11.8198L12.8292 8.70316H15.7677ZM12.002 2.51038C12.3255 2.51038 12.5879 2.77298 12.5879 3.09631C12.5879 3.4198 12.3255 3.68225 12.002 3.68225C11.6786 3.68225 11.416 3.4198 11.416 3.09631C11.416 2.77298 11.6786 2.51038 12.002 2.51038ZM9.49161 2.51038C9.81509 2.51038 10.0775 2.77298 10.0775 3.09631C10.0775 3.4198 9.81509 3.68225 9.49161 3.68225C9.16812 3.68225 8.90567 3.4198 8.90567 3.09631C8.90567 2.77298 9.16812 2.51038 9.49161 2.51038ZM10.7468 6.5065C9.82623 6.5065 8.94702 6.06689 8.39511 5.33066C8.20102 5.07172 8.25366 4.70444 8.5126 4.51035C8.77155 4.31625 9.13882 4.36874 9.33276 4.62769C9.66464 5.07034 10.1932 5.33463 10.7468 5.33463C11.3004 5.33463 11.8289 5.07034 12.1608 4.62769C12.3549 4.36874 12.7222 4.3161 12.9811 4.51035C13.2401 4.70444 13.2926 5.07172 13.0985 5.33066C12.5466 6.06689 11.6674 6.5065 10.7468 6.5065Z"
          // fill="#578495"
        />
      </g>
    </svg>
  );
}

export default PrivateChatIcon;
