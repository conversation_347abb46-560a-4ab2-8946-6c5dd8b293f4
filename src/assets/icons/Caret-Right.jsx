// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CaretRightIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M15.699,9.999c0,0.358-0.139,0.716-0.41,0.991l-8.598,8.6c-0.547,0.547-1.434,0.547-1.98,0s-0.547-1.436,0-1.982l7.607-7.608L4.711,2.39c-0.547-0.547-0.547-1.433,0-1.98s1.434-0.547,1.98,0l8.598,8.599C15.563,9.282,15.699,9.641,15.699,9.999z" />
    </svg>
  );
}

export default CaretRightIcon;
