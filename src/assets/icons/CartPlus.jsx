// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CartPlus(props) {
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_5009_8140)">
        <path d="M4.83573 12.6235C4.83573 12.2557 5.13501 11.9517 5.50288 11.9517H14.5883L16.4942 3.28125H3.68316L3.12254 0.5H0.504883V1.4375H2.35516L4.37935 11.4793C4.0827 11.7707 3.89823 12.1759 3.89823 12.6235C3.89823 13.4474 4.52245 14.128 5.32276 14.2178C5.10654 14.4601 4.97498 14.7795 4.97498 15.1297C4.97498 15.8865 5.58848 16.5 6.34526 16.5C7.10204 16.5 7.71554 15.8865 7.71554 15.1297C7.71554 14.7844 7.58754 14.4692 7.37673 14.2282H11.8454C11.6347 14.4692 11.5066 14.7844 11.5066 15.1297C11.5066 15.8865 12.1201 16.5 12.8769 16.5C13.6337 16.5 14.2472 15.8865 14.2472 15.1297C14.2472 14.7844 14.1192 14.4692 13.9084 14.2282H14.8173V13.2907H5.50288C5.13501 13.2907 4.83573 12.9914 4.83573 12.6235ZM11.8799 7.15625V8.09375H10.0674V9.90625H9.12988V8.09375H7.31738V7.15625H9.12988V5.34375H10.0674V7.15625H11.8799Z" fill="white" />
      </g>
      <defs>
        <clipPath id="clip0_5009_8140">
          <rect width="16" height="16" fill="white" transform="translate(0.5 0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default CartPlus;
