// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ArrowCollapseLeftIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M18.991,9.047H6.285L8.1,7.333C8.502,6.952,8.502,6.381,8.1,6c-0.401-0.381-1.01-0.381-1.41,0L3.161,9.333c-0.403,0.382-0.403,0.952,0,1.334L6.689,14c0.201,0.189,0.504,0.286,0.705,0.286S7.898,14.189,8.1,14c0.403-0.381,0.403-0.953,0-1.333l-1.814-1.714h12.706C19.496,10.953,20,10.477,20,10S19.496,9.047,18.991,9.047z"/>
        <path d="M2,18V2c0-0.601-0.4-1.001-1.001-1.001S0,1.399,0,2v16c0,0.6,0.398,1.001,0.999,1.001S2,18.6,2,18z"/>
      </g>
    </svg>
  );
}

export default ArrowCollapseLeftIcon;
