// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FAQIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M19.496,16.448l-0.484-1.689c-0.023-0.136,0.003-0.274,0.072-0.393c0.6-1.021,0.916-2.19,0.916-3.382c0-3.426-2.589-6.259-5.913-6.646c0.304,0.836,0.47,1.737,0.47,2.677c0,0.165-0.006,0.328-0.016,0.49h2.191c0.315,0,0.587,0.243,0.602,0.558c0.016,0.336-0.252,0.614-0.585,0.614h-2.37c-0.131,0.602-0.329,1.179-0.589,1.721h2.942c0.315,0,0.587,0.243,0.602,0.559c0.016,0.336-0.252,0.613-0.585,0.613h-3.65c-0.468,0.656-1.034,1.237-1.676,1.723h5.31c0.315,0,0.587,0.242,0.602,0.558c0.016,0.337-0.252,0.614-0.585,0.614H9.871c-0.129,0-0.249-0.043-0.346-0.114c-0.544,0.211-1.118,0.362-1.712,0.448c1.21,1.738,3.223,2.878,5.496,2.878c1.189,0,2.359-0.316,3.381-0.916c0.118-0.069,0.258-0.095,0.393-0.072l1.689,0.484C19.213,17.3,19.623,16.891,19.496,16.448z" />
        <path d="M6.692,13.707c3.69,0,6.692-3.002,6.692-6.692c0-3.705-2.987-6.692-6.691-6.692C3.002,0.323,0,3.325,0,7.015c0,1.19,0.317,2.359,0.916,3.381c0.069,0.118,0.095,0.257,0.073,0.393l-0.484,1.689c-0.127,0.441,0.282,0.852,0.725,0.725l1.689-0.484c0.032-0.006,0.064-0.008,0.096-0.008c0.104,0,0.206,0.027,0.296,0.08C4.333,13.391,5.501,13.707,6.692,13.707zM6.692,11.328c-0.329,0-0.572-0.27-0.586-0.586s0.28-0.586,0.586-0.586c0.329,0,0.572,0.268,0.586,0.586C7.292,11.059,6.999,11.328,6.692,11.328z M4.448,4.946c0-1.237,1.007-2.244,2.244-2.244c1.237,0,2.244,1.006,2.244,2.244c0,1.034-0.704,1.907-1.658,2.166v1.433c0,0.323-0.262,0.586-0.586,0.586S6.106,8.869,6.106,8.545V6.604c0-0.323,0.262-0.586,0.586-0.586c0.591,0,1.072-0.48,1.072-1.071c0-0.591-0.481-1.072-1.072-1.072S5.62,4.355,5.62,4.946c0,0.323-0.262,0.586-0.586,0.586C4.711,5.532,4.448,5.27,4.448,4.946z" />
      </g>
    </svg>
  );
}

export default FAQIcon;
