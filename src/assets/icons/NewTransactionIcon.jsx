import React from 'react';

function NewTransactionIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="white"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.1592 4.41015C11.2237 3.62675 10.0543 3.11596 8.77295 3V4.73081C9.58265 4.83298 10.3252 5.15442 10.9398 5.63367L12.1592 4.41015Z"
        fill="#999999"
      />
      <path
        d="M7.64406 4.73084V3C6.36273 3.11596 5.19328 3.62676 4.25781 4.41017L5.47716 5.63367C6.09178 5.15441 6.83436 4.833 7.64406 4.73084Z"
        fill="#999999"
      />
      <path
        d="M12.8198 5.25745L11.5947 6.47676C12.0746 7.09136 12.3965 7.83392 12.4988 8.64359H13.894C14.0048 8.63006 14.1162 8.61868 14.2285 8.61012C14.1065 7.34199 13.5974 6.18473 12.8198 5.25745Z"
        fill="#999999"
      />
      <path
        d="M4.63423 6.47676L3.4107 5.25745C2.62726 6.19292 2.11646 7.36231 2.00049 8.64359H3.73138C3.83354 7.83392 4.15497 7.0914 4.63423 6.47676Z"
        fill="#999999"
      />
      <path
        d="M3.73138 9.77228H2.00049C2.11646 11.0536 2.62726 12.223 3.4107 13.1584L4.63423 11.9391C4.15497 11.3245 3.83354 10.582 3.73138 9.77228Z"
        fill="#999999"
      />
      <path
        d="M7.64406 13.498C6.83436 13.3957 6.09178 13.0739 5.47716 12.594L4.25781 13.8191C5.18512 14.5966 6.34242 15.1057 7.61059 15.2277C7.61911 15.1154 7.63053 15.004 7.64406 14.8932V13.498Z"
        fill="#999999"
      />
      <path
        d="M14.8872 12.5938C16.7052 12.5939 18.179 14.0678 18.1792 15.8857C18.1792 17.7038 16.7053 19.1776 14.8872 19.1777C13.069 19.1777 11.5942 17.7039 11.5942 15.8857C11.5944 14.0677 13.0691 12.5938 14.8872 12.5938ZM14.354 15.1152L13.0005 15.1748L13.98 16.2842L13.5249 17.8516C13.548 17.838 15.8383 16.4782 14.9575 13.5254L14.354 15.1152ZM15.0454 13.5332C15.7586 15.4675 15.0415 16.6436 15.0415 16.6436L16.5259 17.8516L16.021 16.2852L17.0005 15.1748H16.9995L15.646 15.1152L15.0454 13.5332ZM14.0522 14.2773C13.8968 14.2776 13.7712 14.4238 13.771 14.6045C13.7712 14.7852 13.8968 14.9324 14.0522 14.9326C14.2076 14.9323 14.3343 14.7851 14.3345 14.6045C14.3343 14.4239 14.2076 14.2776 14.0522 14.2773ZM15.9829 14.2773C15.8272 14.2773 15.7007 14.4244 15.7007 14.6055C15.7011 14.7861 15.8275 14.9326 15.9829 14.9326C16.138 14.9321 16.2637 14.7858 16.2642 14.6055C16.2642 14.4247 16.1383 14.2778 15.9829 14.2773Z"
        fill="#999999"
      />
      <path
        d="M18.3662 18.5233L19.5897 19.7426C20.3732 18.8072 20.884 17.6377 21 16.3564H19.2691C19.1669 17.1661 18.8455 17.9086 18.3662 18.5233Z"
        fill="#999999"
      />
      <path
        d="M15.3569 20.2692V22C16.6383 21.884 17.8077 21.3732 18.7432 20.5898L17.5238 19.3663C16.9092 19.8456 16.1666 20.167 15.3569 20.2692Z"
        fill="#999999"
      />
      <path
        d="M18.7432 11.1824C17.8077 10.399 16.6383 9.88824 15.3569 9.77228V11.5031C16.1666 11.6053 16.9092 11.9267 17.5238 12.4059L18.7432 11.1824Z"
        fill="#999999"
      />
      <path
        d="M14.228 9.77228C12.9467 9.88824 11.7773 10.399 10.8418 11.1824L12.0611 12.4059C12.6758 11.9267 13.4183 11.6053 14.228 11.5031V9.77228Z"
        fill="#999999"
      />
      <path
        d="M19.2691 15.2277H21C20.884 13.9464 20.3732 12.777 19.5897 11.8416L18.3662 13.0609C18.8455 13.6755 19.1669 14.418 19.2691 15.2277Z"
        fill="#999999"
      />
      <path
        d="M11.4067 13.0609L10.1832 11.8416C9.39973 12.777 8.88892 13.9464 8.77295 15.2277H10.5038C10.606 14.418 10.9274 13.6755 11.4067 13.0609Z"
        fill="#999999"
      />
      <path
        d="M10.5038 16.3564H8.77295C8.88892 17.6377 9.39973 18.8072 10.1832 19.7426L11.4067 18.5233C10.9275 17.9087 10.606 17.1661 10.5038 16.3564Z"
        fill="#999999"
      />
      <path
        d="M10.8418 20.5898C11.7773 21.3732 12.9467 21.884 14.228 22V20.2692C13.4183 20.167 12.6758 19.8456 12.0611 19.3663L10.8418 20.5898Z"
        fill="#999999"
      />
      <path
        d="M8.11475 5.82129C9.92994 5.82145 11.4067 7.29911 11.4067 9.11426C11.4067 9.18465 11.4043 9.25492 11.3999 9.32422C10.7743 9.65082 10.2023 10.0661 9.70068 10.5537L9.36572 9.59375L10.5542 8.34961L8.91064 8.28223L8.18115 6.50781C9.03907 8.6549 8.19382 9.96823 8.17627 9.99512L9.37256 10.8945C8.96303 11.3479 8.60988 11.8532 8.32471 12.3994C8.25535 12.4038 8.1852 12.4062 8.11475 12.4062C6.29956 12.4062 4.82202 10.9293 4.82178 9.11426C4.82178 7.29898 6.29941 5.82129 8.11475 5.82129ZM7.28662 8.29004L5.64209 8.35742L6.83154 9.60156L6.27881 11.3584C6.27881 11.3584 9.09505 9.83609 8.01904 6.50781L7.28662 8.29004ZM6.92041 7.35059C6.7314 7.35059 6.57788 7.51501 6.57764 7.71777C6.57764 7.92076 6.73125 8.08594 6.92041 8.08594C7.10952 8.08586 7.26221 7.92071 7.26221 7.71777C7.26196 7.51506 7.10936 7.35066 6.92041 7.35059ZM9.31885 7.34277C9.12969 7.34277 8.97609 7.50699 8.97607 7.70996C8.97607 7.91295 9.12968 8.07715 9.31885 8.07715C9.50781 8.07689 9.66064 7.91279 9.66064 7.70996C9.66063 7.50715 9.5078 7.34303 9.31885 7.34277Z"
        fill="#999999"
      />
    </svg>
  );
}

export default NewTransactionIcon;
