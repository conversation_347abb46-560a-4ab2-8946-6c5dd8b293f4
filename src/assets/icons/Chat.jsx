// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function ChatIcon(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="16" viewBox="0 0 18 16" fill="none" {...props}>
      <path d="M17.116 1.41357V11.4136C17.116 11.7451 16.9843 12.063 16.7499 12.2975C16.5155 12.5319 16.1975 12.6636 15.866 12.6636H5.47536L2.92849 14.8636L2.92146 14.869C2.69648 15.0598 2.41094 15.1642 2.11599 15.1636C1.93261 15.1633 1.75153 15.1227 1.58552 15.0448C1.36959 14.9453 1.18689 14.7857 1.05929 14.5851C0.931684 14.3845 0.864575 14.1513 0.865989 13.9136V1.41357C0.865989 1.08205 0.997685 0.764111 1.23211 0.529691C1.46653 0.29527 1.78447 0.163574 2.11599 0.163574H15.866C16.1975 0.163574 16.5155 0.29527 16.7499 0.529691C16.9843 0.764111 17.116 1.08205 17.116 1.41357Z" fill="url(#paint0_linear_8044_14922)" />
      <path d="M17.116 1.41357V11.4136C17.116 11.7451 16.9843 12.063 16.7499 12.2975C16.5155 12.5319 16.1975 12.6636 15.866 12.6636H5.47536L2.92849 14.8636L2.92146 14.869C2.69648 15.0598 2.41094 15.1642 2.11599 15.1636C1.93261 15.1633 1.75153 15.1227 1.58552 15.0448C1.36959 14.9453 1.18689 14.7857 1.05929 14.5851C0.931684 14.3845 0.864575 14.1513 0.865989 13.9136V1.41357C0.865989 1.08205 0.997685 0.764111 1.23211 0.529691C1.46653 0.29527 1.78447 0.163574 2.11599 0.163574H15.866C16.1975 0.163574 16.5155 0.29527 16.7499 0.529691C16.9843 0.764111 17.116 1.08205 17.116 1.41357Z" fill="url(#paint0_linear_8044_14922)" /><defs>
        <linearGradient id="paint0_linear_8044_14922" x1="8.99098" y1="15.1636" x2="8.99098" y2="0.158593" gradientUnits="userSpaceOnUse">
          <stop stop-color="#BB7733" />
          <stop offset="0.07" stop-color="#C17F37" />
          <stop offset="0.2" stop-color="#D19844" />
          <stop offset="0.33" stop-color="#EABB58" />
          <stop offset="0.47" stop-color="#EECE6C" />
          <stop offset="0.61" stop-color="#F2DD7B" />
          <stop offset="0.82" stop-color="#ECC764" />
          <stop offset="0.99" stop-color="#EABB58" />
        </linearGradient>
      </defs>

    </svg>
  );
}

export default ChatIcon;
