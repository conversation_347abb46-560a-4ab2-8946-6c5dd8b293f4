// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function HamburgerIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <rect y="1.43" width="20" height="0.714"/>
        <path d="M6.891,7.962c0.141-0.14,0.365-0.14,0.506,0c0.14,0.141,0.14,0.365,0,0.505L6.221,9.643H20v0.715H6.221l1.176,1.177c0.14,0.14,0.14,0.364,0,0.505c-0.141,0.141-0.365,0.141-0.506,0l-1.786-1.785c-0.14-0.14-0.14-0.365,0-0.506L6.891,7.962z"/>
        <rect y="17.857" width="20" height="0.713"/>
      </g>
    </svg>
  );
}

export default HamburgerIcon;
