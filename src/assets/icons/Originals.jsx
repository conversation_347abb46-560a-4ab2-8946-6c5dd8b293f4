// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function OriginalsIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M10,20c-3.837,0-6.959-3.139-6.959-6.998c0-1.682,0.573-2.825,1.236-4.149c0.368-0.735,0.749-1.495,1.101-2.465l0.366-1.009C6.283,5.511,6.1,5.466,6.64,5.599L6.498,6.663C6.381,7.532,6.541,8.518,6.933,9.368c0.166,0.36,0.367,0.68,0.59,0.946C7.445,8.902,7.59,7.542,7.956,6.254c0.383-1.35,0.998-2.594,1.827-3.699C10.814,1.181,12,0.283,12.549,0.209L14.105,0l-1.047,1.171c-0.477,0.532-0.738,1.218-0.738,1.933c0,1.381,0.848,2.362,1.828,3.497c0.629,0.728,1.342,1.553,1.883,2.566c0.625,1.173,0.928,2.427,0.928,3.835C16.959,16.861,13.838,20,10,20z" />
    </svg>
  );
}

export default OriginalsIcon;
