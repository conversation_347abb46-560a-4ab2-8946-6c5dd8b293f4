// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FilterIcon(props) {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="#578495"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >

      <g clipPath="url(#clip0_5001_8114)">
        <path d="M6.8916 9.49194V17.4721C6.8916 17.8632 7.30414 18.1191 7.65474 17.9439L10.8188 16.3618C10.9975 16.2724 11.1103 16.0899 11.1103 15.8901V9.49194H6.8916Z" />
        <path d="M17.4382 0H0.5632C0.119903 0 -0.125642 0.515808 0.153823 0.859817L6.31069 8.43722H11.6906L17.8474 0.859817C18.1269 0.515945 17.8813 0 17.4382 0Z" />
      </g>
      <defs>
        <clipPath id="clip0_5001_8114">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>

  );
}

export default FilterIcon;
