// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function NotificationIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M10,20c1.199,0,2.23-0.725,2.686-1.758h-5.37C7.769,19.275,8.801,20,10,20z" />
        <path d="M16.055,9.682V8.398c0-2.728-1.813-5.039-4.297-5.794V1.758C11.758,0.789,10.969,0,10,0S8.242,0.789,8.242,1.758v0.847C5.758,3.359,3.945,5.671,3.945,8.398v1.284c0,2.396-0.913,4.667-2.571,6.396c-0.163,0.17-0.208,0.42-0.116,0.637C1.35,16.93,1.562,17.07,1.797,17.07h16.406c0.234,0,0.447-0.141,0.539-0.355c0.092-0.217,0.047-0.467-0.116-0.637C16.968,14.35,16.055,12.078,16.055,9.682z M10.586,2.372C10.393,2.354,10.198,2.344,10,2.344s-0.393,0.01-0.586,0.028V1.758c0-0.323,0.263-0.586,0.586-0.586s0.586,0.263,0.586,0.586V2.372z"/>
      </g>
    </svg>
  );
}

export default NotificationIcon;
