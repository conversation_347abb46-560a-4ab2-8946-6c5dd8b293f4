// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function CaretLeftIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path d="M4.301,10.002c0-0.358,0.138-0.718,0.41-0.991l8.598-8.6c0.547-0.547,1.435-0.547,1.98,0c0.546,0.547,0.547,1.435,0,1.981l-7.607,7.61l7.607,7.608c0.547,0.547,0.547,1.434,0,1.979s-1.434,0.547-1.98,0l-8.598-8.6C4.438,10.718,4.301,10.359,4.301,10.002z" />
    </svg>
  );
}

export default CaretLeftIcon;
