// DO NOT EDIT THIS FILE. This file auto-generated, use `npm run sync-icons` to update icons
import React from 'react';

function FaucetIcon(props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g>
        <path d="M0,5.125h3.722v7.464H0V5.125z" />
        <path d="M19.25,12.625v-0.754c0-3.03-2.466-5.496-5.497-5.496h-2.004V4.421l2.132,0.609c0.225,0.064,0.453,0.096,0.678,0.096c0.523,0,1.035-0.169,1.467-0.496C16.645,4.164,17,3.452,17,2.677c0-0.775-0.355-1.487-0.975-1.954c-0.618-0.466-1.399-0.612-2.145-0.399l-2.132,0.609v-1.058H10.25v1.058L8.12,0.324C7.375,0.111,6.593,0.256,5.974,0.723C5.354,1.189,5,1.902,5,2.677C5,3.452,5.354,4.164,5.974,4.63C6.405,4.957,6.917,5.126,7.44,5.126c0.226,0,0.454-0.032,0.68-0.096l2.13-0.609v1.954H5.222v4.75h7.859c0.645,0,1.169,0.523,1.169,1.168v0.332H13.5v1.5H20v-1.5H19.25z" />
      </g>
    </svg>
  );
}

export default FaucetIcon;
