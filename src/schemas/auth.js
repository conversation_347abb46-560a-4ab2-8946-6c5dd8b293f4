import { z } from 'zod';
import * as yup from 'yup';

export const signInSchema = z.object({
  userName: z
    .string()
    .min(1, 'Username is required')
    .refine((val) => val === '' || /^[a-zA-Z\d]{5,12}$/.test(val), {
      message: 'Username must be 5-12 characters long and contain only letters and digits',
    }),

  password: z
    .string()
    .min(1, 'Password is required')
    .refine(
      (val) =>
        val === '' ||
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,20}$/.test(val),
      {
        message:
          'Password must be 8-20 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
      }
    ),
});

export const signUpSchema = z
  .object({
    userName: z
      .string()
      .min(1, 'Username is required')
      .refine((val) => val === '' || /^[a-zA-Z\d]{5,12}$/.test(val), {
        message: 'Username must be 5-12 characters long and contain only letters and digits',
      })
      .refine((val) => val === '' || !/\s/.test(val), {
        message: 'Username cannot contain spaces',
      })
      .refine((val) => val === '' || !/^\d+$/.test(val), {
        message: 'Username cannot contain only numbers',
      }),

    email: z
      .string()
      .min(1, 'Email is required')
      .refine((val) => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
        message: 'Invalid email address',
      })
      .refine((val) => val === '' || !/^\d+@/.test(val), {
        message: 'Email username part cannot contain only numbers',
      }),

    firstName: z
      .string()
      .min(1, 'First Name is required')
      .refine((val) => val === '' || /^[A-Za-z]+$/.test(val), {
        message: 'First Name should contain only letters',
      })
      .refine((val) => val === '' || val.length >= 3, {
        message: 'First Name must be at least 3 characters',
      })
      .refine((val) => val === '' || val.length <= 50, {
        message: 'First Name must be less than 50 characters',
      }),

    lastName: z
      .string()
      .min(1, 'Last Name is required')
      .refine((val) => val === '' || /^[A-Za-z]+$/.test(val), {
        message: 'Last Name should contain only letters',
      })
      .refine((val) => val === '' || val.length >= 3, {
        message: 'Last Name must be at least 3 characters',
      })
      .refine((val) => val === '' || val.length <= 50, {
        message: 'Last Name must be less than 50 characters',
      }),


    password: z
      .string()
      .min(1, 'Password is required')
      .refine(
        (val) =>
          val === '' ||
          /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,20}$/.test(val),
        {
          message:
            'Password must be 8-20 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
        }
      ),

      confirmPassword: z
      .string()
      .min(1, 'Confirm Password is required'),

    over18: z
      .boolean()
      .refine((value) => value === true, {
        message: 'You must confirm that you are at least 18 years old',
      }),
  })
  .refine(
    (data) => {
      if (!data.password || !data.confirmPassword) return true;
      return data.confirmPassword === data.password;
    },
    {
      message: "Password doesn't match",
      path: ['confirmPassword'],
    }
  );


export const changePasswordSchema = yup.object().shape({
  currentPassword: yup.string().required('Current password is required'),
  newPassword: yup
    .string()
    .required('New password is required')
    .min(8, 'New password must be at least 8 characters long')
    .matches(
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&]).{8,10}$/,
      'Password must be 8-10 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character',
    ),
  confirmNewPassword: yup
    .string()
    .required('Confirm new password is required')
    .oneOf([yup.ref('newPassword')], 'Passwords do not match'),
});

// .refine((data) => data.newPassword === data.confirmNewPassword, {
//   message: 'Passwords do not match',
//   path: ['confirmNewPassword'],
// });

export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .refine((val) => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Invalid email address',
    })
});


export const kycDocSchema = yup.object().shape({
  file: yup
    .mixed()
    .required('File is required')
    .test(
      'fileSize',
      'File too large',
      (value) => value && value.size <= 10 * 1024 * 1024,
    )
    .test(
      'fileType',
      'Unsupported File Format',
      (value) =>
        value && ['image/jpeg', 'image/png', 'image/jpg'].includes(value.type),
    ),
});
