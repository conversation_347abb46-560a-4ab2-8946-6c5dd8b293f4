import * as yup from 'yup';

const deliverAddress = yup.object().shape({
  deliveryAddress1: yup.string().trim().required('Address 1 is required'),
  deliveryAddress2: yup.string().trim().required('Address 2 is required'),
  city: yup.number().required('City is required'),
  state: yup.number().required('State is required'),
  postalCode: yup
    .string()
    .required('Postal Code is required')
    .matches(/^[0-9]{5}$/, 'Postal Code must be exactly 5 digits'),
  makeDefaultAddress: yup.boolean(),
});

export default deliverAddress;
