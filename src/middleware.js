import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const protectedPatterns = [
  '/user/profile',
  '/game/',
  '/vip',
  '/favorites',
  '/notice',
  '/setting',
  '/store',
  '/task-list',
  '/transactions',
  '/user',
  '/two-factor',
  '/user/',
  // '/user/ignored-users',
  // '/user/kyc',
  // '/user/password',
  // '/user/prefrences',
  // '/user/profile',
  // '/user/responsible-gaming',
];

export default async function middleware(req) {
  const { pathname } = req.nextUrl;
  const cookie = cookies().get('accessToken');

  // Check if the current path matches any protected pattern
  const isProtected = protectedPatterns.some(
    (pattern) =>
      // For exact matches
      pathname === pattern ||
      // For dynamic routes (like /game/123)
      (pattern.endsWith('/') && pathname.startsWith(pattern)),
  );

  if (isProtected && !cookie) {
    return NextResponse.redirect(new URL('/', req.nextUrl));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|.*\\.png$).*)'],
};
