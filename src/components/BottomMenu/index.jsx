'use client';

import React, { useState, useEffect } from 'react';
import useGeneralStore from '@/store/useGeneralStore';
import MenuIcon from '@/assets/icons/Menu';
import FavoriteIcon from '@/assets/icons/Favorite';
import ChatIcon from '@/assets/icons/Chat';
import CartIcon from '@/assets/icons/Cart';
import ProfileIcon from '@/assets/icons/Profile';
import useAuthStore from '@/store/useAuthStore';
import { useRouter, usePathname, useParams } from 'next/navigation';
import HomeIcon from '@/assets/icons/HomeIcon';
import HeartIcon from '@/assets/icons/HeartIcon';
import FootballIcon from '@/assets/icons/Football';
import CasinoIcon from '@/assets/icons/Casino';
import { FileBarChart, Users, Receipt, Heart } from 'lucide-react';
import FriendChat from '@/assets/icons/FriendChat';
import SmallChatIcon from '@/assets/icons/SmallChatIcon';
import CasinoMenuIcon from '@/assets/icons/CasinoMenu';
import FriendChatOutlineIcon from '@/assets/icons/FriendChatOutlineIcon';
import NewFavIcon from '@/assets/icons/NewFavIcon';
import NewTransactionIcon from '@/assets/icons/NewTransactionIcon';
import TransactionIcon from '@/assets/icons/TransactionIcon';
import useChatStore from '@/store/useChatStore';

import chat from '../../assets/icons/chat.png'
function BottomMenu() {
  const { setOpenChat, setActiveMenu, activeMenu,openChat } = useGeneralStore(
    (state) => state,
  );
  const { gameId } = useParams();
  const { showChat, setShowChat } = useChatStore();
  const { isAuthenticated } = useAuthStore((state) => state);
  const router = useRouter();
  const pathname = usePathname();
  // const [activeMenu, setActiveMenu] = useState(pathname); // Track the active menu item

  const menuItems = [
    {
      title: '/',
      icon: <HomeIcon className="h-5 w-5 fill-steelTeal-1000" />,
      onClick: () => {
        setActiveMenu('/');
        // setOpenChat(false);
        setShowChat(false),
        router.push('/');
      },
    },
    {
      title: '/favorites',
      icon: <HeartIcon className="h-5 w-5 fill-steelTeal-1000" />,
      onClick: () => {
        setActiveMenu('/favorites');
        // setOpenChat(false);
        setShowChat(false),

        router.push('/favorites');
      },
    },
    {
      title: '/casino',
      icon: <CasinoMenuIcon className="h-5 w-5 fill-steelTeal-1000" />,
      onClick: () => {
        setActiveMenu('/casino');
        // setOpenChat(false);
        setShowChat(false),

        router.push('/casino');
      },
    },
    // {
    //   title: '/sports',
    //   icon: <FootballIcon className="h-5 w-5 fill-steelTeal-1000" />,
    //   onClick: () => {
    //     setActivchateMenu('/sports');
    //     setOpenChat(false);
    //     router.push('/sports');
    //   },
    // },
    {
      title: '/transactions',
      icon: <Receipt className="h-5 w-5" />,
      onClick: () => {
        setActiveMenu('/transactions');
        // setOpenChat(false);
        setShowChat(false),

        router.push('/transactions');
      },
    },

    // {
    //   title: 'friends',
    //   icon: <SmallChatIcon className="h-5 w-5 fill-steelTeal-1000" />,
    //   onClick: () => {
    //     setActiveMenu('friends');
    //     setOpenChat(true);
    //   },
    // },
    {
      title: '/public-friends',
      icon: <FriendChatOutlineIcon className="h-5 w-5 fill-steelTeal-1000" />,
      onClick: () => {
        setActiveMenu('/public-friends');
        // setOpenChat(false);
        setShowChat(false)
        router.push('/public-friends');
      },
    },
    // {
    //   title: 'msg',
    //   icon:<SmallChatIcon />,
    //   onClick: () => {
    //     setActiveMenu('msg');
    //     setShowChat(!showChat);
    //       // setOpenChat(!openChat);
    //   },
    // },
    // {
    //   title: '/inventory',
    //   icon: <CartIcon className="h-5 w-5 fill-steelTeal-1000" />,
    //   onClick: () => {
    //     setActiveMenu('/inventory');
    //     setOpenChat(false);
    //     router.push('/inventory');
    //   },
    // },
    // {
    //   title: '/user',
    //   icon: <ProfileIcon className="h-5 w-5 fill-steelTeal-1000" />,
    //   onClick: () => {
    //     setActiveMenu('/user');
    //     setOpenChat(false);
    //     router.push('/user');
    //   },
    // },
  ];
  useEffect(() => {
    setActiveMenu(pathname);
  }, [pathname]);
  if (gameId) return <></>;

  return isAuthenticated ? (
    <div className="bottom-menu-blur fixed bottom-0 left-0 right-0 z-50 block bg-maastrichtBlue-500 shadow-bottom-menu lg:hidden">
      <div className="mx-auto flex h-[3.75rem] max-w-[26.563rem] items-center justify-between gap-[0.75rem] px-[2.375rem]">
        {menuItems.map((item) => (
          <button
            key={item.title}
            type="button"
            className={`group flex h-10 w-10 items-center justify-center rounded-md ${
              activeMenu === item.title
                ? 'bg-primary-1000'
                : 'bg-richBlack-1000'
            }`}
            onClick={item.onClick}
          >
            {item.icon}
          </button>
        ))}
      </div>
    </div>
  ) : null;
}

export default BottomMenu;
