'use client';

import ProfileIcon from '@/assets/icons/Profile';
import profile_url from '@/assets/images/svg-images/profile-icon.svg';
import useModalStore from '@/store/useModalStore';
import { X } from 'lucide-react';
import OnlineUserImg from '@/assets/images/demo-image/user-img.jpg';
import Image from 'next/image';
import IconButton from '../Common/Button/IconButton';
import CustomImage from '../Common/CustomImage';
import UserAvatar from '../DefaultImage';

function ActivePlayerModel({ activePlayer }) {
  const { closeModal } = useModalStore((state) => state);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="bg-black/60 fixed inset-0 z-50 flex items-center justify-center  overflow-y-auto px-4"
    >
      <div className="relative w-full max-w-lg p-4">
        <div className="rounded-xl border border-richBlack-900 bg-maastrichtBlue-1000 shadow-2xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-richBlack-900 p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white text-lg font-semibold tracking-wide">
                Active Players
              </h3>
            </div>
            <IconButton onClick={closeModal} className="h-6 w-6 min-w-6">
              <X className="hover:text-white h-5 w-5 text-steelTeal-1000 transition duration-200" />
            </IconButton>
          </div>

          {/* Game Info */}
          <div className="flex items-center gap-4 p-4 max-sm:flex-col max-sm:text-center">
            <div className="h-16 w-16 overflow-hidden rounded-md">
              <CustomImage
                src={activePlayer?.thumbnail || ''}
                alt="Game Thumbnail"
                width={64}
                height={64}
                className="h-full w-full object-cover"
                skeletonWidth={64}
                skeletonHeight={64}
              />
            </div>
            <div className="flex flex-col">
              <h4 className="text-white text-xl font-medium">
                {activePlayer?.gameName}
              </h4>
              {/* You can also show total players or other game info here */}
            </div>
          </div>

          {/* Player List */}
          <div className="max-h-[300px] space-y-3 overflow-auto p-4 pt-0">
            {activePlayer?.players?.length > 0 ? (
              activePlayer.players.map((player, idx) => (
                <div
                  key={idx}
                  className="bg-maastrichtBlue-950 hover:bg-maastrichtBlue-900 flex items-center gap-3 rounded-lg border border-secondaryBorder p-2 transition"
                >
                  <div className="relative h-10 w-10 overflow-hidden rounded-full border border-richBlack-900">
                    {player?.profileImage ? (
                      <Image
                        src={player?.profileImage || OnlineUserImg}
                        alt={player?.username}
                        width={40}
                        height={40}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <UserAvatar
                        firstName={player?.firstName}
                        lastName={player?.lastName}
                        username={player?.username}
                      />
                    )}
                    <span className="border-maastrichtBlue-950 absolute bottom-0 right-0 h-2 w-2 rounded-full border-2 bg-green-400" />
                  </div>
                  <h5 className="text-white max-w-[160px] truncate text-sm font-medium">
                    {player?.username}
                  </h5>
                </div>
              ))
            ) : (
              <p className="text-center text-sm text-white-700">
                No active players at the moment.
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActivePlayerModel;
