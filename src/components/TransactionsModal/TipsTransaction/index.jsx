import React, { useEffect } from 'react';
import useModalStore from '@/store/useModalStore';
import { useTipsTransactions } from '@/hooks/useTipsTransactions';
import NoDataFound from '@/components/Common/NoDataFound';
import CommonLoader from '@/assets/json/button-loader.json';
import Lottie from 'lottie-react';

function TipsTransactions({ activeTab }) {
  const { tipsTransactions, isLoading, isError, error, refetch } =
    useTipsTransactions({ activeTab });

  return (
    <div>
      {isLoading && (
        <Lottie
          animationData={CommonLoader}
          loop
          className=" max-h-10 min-h-10 w-20"
        />
      )}
      {isError && <p>Error loading transactions: {error.message}</p>}
      {tipsTransactions && (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white-100">
                <th className="whitespace-nowrap px-2 py-4 text-center text-sm font-bold text-steelTeal-1000">
                  From
                </th>
                <th className="whitespace-nowrap px-2 py-4 text-center text-sm font-bold text-steelTeal-1000">
                  To
                </th>
                <th className="whitespace-nowrap px-2 py-4 text-center text-sm font-bold text-steelTeal-1000">
                  Amount
                </th>
                <th className="whitespace-nowrap px-2 py-4 text-center text-sm font-bold text-steelTeal-1000">
                  Date
                </th>
              </tr>
            </thead>
            <tbody>
              {tipsTransactions.length === 0 ? (
                <tr>
                  <td colSpan="4" className="py-4 text-center">
                    <NoDataFound />
                  </td>
                </tr>
              ) : (
                tipsTransactions.map((transaction, index) => {
                  return (
                    <tr key={index}>
                      {/* From (Sender Username) */}
                      <td className="whitespace-nowrap px-2 py-4 text-center text-sm font-normal text-white-1000">
                        {transaction?.User?.username || 'Loading...'}
                      </td>

                      {/* To (Receiver Username) */}
                      <td className="whitespace-nowrap px-2 py-4 text-center text-sm font-normal text-white-1000">
                        {transaction.receiverUsername}
                      </td>

                      {/* Amount */}
                      <td className="whitespace-nowrap px-2 py-4 text-center text-sm font-normal text-white-1000">
                        {transaction.amount}
                      </td>

                      {/* Created At */}
                      <td className="whitespace-nowrap px-2 py-4 text-center text-sm font-normal text-white-1000">
                        {new Date(transaction.createdAt).toLocaleString()}
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

export default TipsTransactions;
