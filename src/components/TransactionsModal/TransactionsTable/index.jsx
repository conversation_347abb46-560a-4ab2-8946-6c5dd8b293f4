import React from 'react';

function TransactionsTable({ data, columns, isLoading, hasTransactions }) {
  const isImage = (col) => {
    return typeof col === 'string' && col.startsWith('http');
  };
  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[37.5rem]">
        <thead>
          <tr className="border-b border-white-100">
            {columns.map((column) => (
              <th
                key={column.key}
                className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000"
              >
                {column.value}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {isLoading ? (
            <tr>
              <td colSpan={columns.length} className="py-4 text-center">
                <div className="h-10 w-24 text-center">Loading...</div>
              </td>
            </tr>
          ) : !hasTransactions ? (
            <tr>
              <td colSpan={columns.length} className="py-4 text-center">
                No transactions found
              </td>
            </tr>
          ) : (
            data.map((transaction, index) => (
              <tr key={index}>
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000"
                  >
                    {isImage(transaction[column.key.toLowerCase()]) ? (
                      <img
                        src={transaction[column.key.toLowerCase()]}
                        alt="Gift"
                        className="h-10 max-w-[100px] object-cover"
                      />
                    ) : (
                      transaction[column.key.toLowerCase()]
                    )}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}

export default TransactionsTable;
