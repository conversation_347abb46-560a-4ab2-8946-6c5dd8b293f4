import React from 'react';
import TransactionsTable from '../TransactionsTable';
import { useRainTransactions } from '@/hooks/useRainTransactions';

function RainTransactions() {
  const { rainTransactions, isLoading } = useRainTransactions({ rainId: null });
  const columns = [
    { key: 'id', value: 'ID' },
    { key: 'status', value: 'Status' },
    { key: 'amount', value: 'Amount' },
    { key: 'date', value: 'Date' },
  ];

  const hasTransactions = rainTransactions?.count > 0;
  const data = hasTransactions
    ? rainTransactions.rows.map((transaction) => ({
        id: transaction.casinoTransactionId,
        status: transaction.status === 1 ? 'Completed' : 'Pending',
        amount: transaction.amount,
        date: new Date(transaction.createdAt).toLocaleDateString(),
      }))
    : [];

  return (
    <TransactionsTable
      data={data}
      columns={columns}
      isLoading={isLoading}
      hasTransactions={hasTransactions}
    />
  );
}

export default RainTransactions;
