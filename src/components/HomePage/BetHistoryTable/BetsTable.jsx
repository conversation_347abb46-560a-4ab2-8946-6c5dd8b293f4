import React, { useEffect } from 'react';
import useBetStore from '@/store/useBetStore';
import useBets from '@/hooks/useBets';

import Lottie from 'lottie-react';
import CommonLoader from '@/assets/json/button-loader.json';
import NoDataFound from '@/components/Common/NoDataFound';
import useAuthStore from '@/store/useAuthStore';

function BetsTable({ betType, limit }) {
  const { allBets, myBets } = useBetStore((state) => state);
  const { refetchAllBets, isBetsLoading } = useBets({
    myBets: betType === 'myBets',
    limit: limit.value
  });
  const { isAuthenticated } = useAuthStore((state) => state);
  const data = betType === 'myBets' ? myBets : allBets;

  useEffect(() => {
    if (isAuthenticated)
      refetchAllBets();
  }, [betType]);

  return (
    <div className="overflow-x-auto">
      <table className="w-full min-w-[37.5rem]">
        <thead>
          <tr className="border-b border-white-100">
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              Game
            </th>
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              User
            </th>
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              Time
            </th>
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              Transaction Amount
            </th>
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              Multiplier
            </th>
            <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
              Payout
            </th>
          </tr>
        </thead>
        <tbody>
          {isBetsLoading ? (
            <tr>
              <td colSpan="6" className="py-4 text-center">
                <div className="flex h-10 w-full items-center justify-center p-10 text-center">
                  <Lottie
                    animationData={CommonLoader}
                    loop
                    className=" max-h-10 min-h-10 w-20"
                  />
                </div>
              </td>
            </tr>
          ) : data?.length === 0 ? (
            <tr>
              <td colSpan="6" className="py-10 text-center">
                <NoDataFound className="w-28" />
              </td>
            </tr>
          ) : (
            data?.slice(0, limit.value).map((bet, index) => (
              <tr key={index}>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
                  {bet?.name}
                </td>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
                  {bet?.username}
                </td>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
                  {bet?.createdAt}
                </td>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
                  {bet?.totalBet}
                </td>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
                  {bet?.multiplier || 0}
                </td>
                <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-green-1000">
                  {bet?.winAmount || 0}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}

export default BetsTable;
