'use client';

import Tabs from '@/components/Common/Tabs';
import React, { useState } from 'react';
import BetsTable from '@/components/HomePage/BetHistoryTable/BetsTable';
import Select from 'react-select';
import useAuthStore from '@/store/useAuthStore';

function BetHistoryTable() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const [selectedTab, setSelectedTab] = useState(0)
  const options = [
    { value: 11, label: 11 },
    { value: 25, label: 25 },
    { value: 50, label: 50 },
  ];

  const [selectedOption, setSelectedOption] = useState(options[0]);

  const tabs = [
    {
      label: 'Transactions',
      content: <BetsTable betType="myBets" limit={selectedOption} />,
    },
    {
      label: 'All Transactions',
      content: <BetsTable betType="allBets" limit={selectedOption} />,
    },
  ];

  return isAuthenticated ? (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container">
      <Select
        className="mobile-input-select ml-auto mt-0 w-16"
        classNamePrefix="mobile-input"
        defaultValue={selectedOption}
        onChange={setSelectedOption}
        options={options}
        placeholder="11"
      />
      <Tabs tabs={tabs} classes="-mt-12" selectedTab={selectedTab} setSelectedTab={setSelectedTab}/>
    </section>
  ) : null;
}

export default BetHistoryTable;
