'use client';

import { useEffect, useState } from 'react';
import useLobby from '@/hooks/useLobby';
import CategoryCarousel from '../CategoryCarousel';
import Lottie from 'lottie-react';
import CommonLoader from '@/assets/json/button-loader.json';
import useGameStore from '@/store/useGameStore';
import MainLoader from '@/components/Common/Loader/MainLoader';

export default function LobbyGames() {
  const { isLobbyGamesLoading } = useLobby();
  const { lobbyGames } = useGameStore();
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [gamePerRow, setGamesPerRow] = useState(5);
  const [viewAllCount, setViewAllCount] = useState(10);
  const lobbyGamesLength = lobbyGames.reduce(
    (sum, item) => sum + item.subCategoryGames.length,
    0,
  );
  // Detect screen size to determine how many games to show per row
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 640); // True if screen width is <1024px
      setGamesPerRow(
        window.innerWidth < 640 ? 2 : window.innerWidth < 768 ? 4 : 5,
      ); // True if screen width is <1024px
      setViewAllCount(
        window.innerWidth < 640 ? 4 : window.innerWidth < 768 ? 8 : 10,
      );
    };

    // Set the initial state on mount
    handleResize();

    // Listen for window resize events
    window.addEventListener('resize', handleResize);

    // Clean up the event listener on unmount
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (isLobbyGamesLoading) {
    return (
      <div className="flex h-[30dvh] w-full items-center justify-center">
        <MainLoader className="w-32" />
      </div>
    );
  }

  if (lobbyGamesLength === 0) {
    return <div className="text-white mt-8 text-center">No games found</div>;
  }
  return (
    <>
      <section className="mb-10 rounded-lg shadow-container">
        <CategoryCarousel lobbyGames={lobbyGames} />
      </section>
    </>
  );
}
