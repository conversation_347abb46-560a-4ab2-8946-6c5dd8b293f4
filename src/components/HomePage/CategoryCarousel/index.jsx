'use client';

import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import GameCard from '@/components/Common/GameCard';
import useAuthStore from '@/store/useAuthStore';
import toast from 'react-hot-toast';
import Auth from '@/components/Auth';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import defaultImage from '../../../assets/images/png/default-image.png';
import ZeroBalancePopUp from '@/components/Models/ZeroBalancePopUp';
import useCategoryTitleStore from '@/store/useCategoryTitleStore';
import { slugify } from '@/utils/helper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Mousewheel } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import ArrowCircleLeftIcon from '@/assets/icons/Arrow-Circle-Left';
import ArrowCircleDownIcon from '@/assets/icons/Arrow-Circle-Down';

export default function CategoryCarousel({ lobbyGames }) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const { userDetails } = useAuthStore();
  const { isAuthenticated, userWallet, coin } = useAuthStore((state) => state);
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const { setCategoryTitle } = useCategoryTitleStore((state) => state);
  const router = useRouter();

  const handleGameClick = (gameName, provider) => {
    if (userDetails?.isRestrict) {
      toast.error('You are restricted, Please contact administrator');
      return;
    }
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
    } else {
      if (coin === "GC" && userWallet.gcCoin === 0) {
        openModal(<ZeroBalancePopUp message="You have zero GC Coins! Please add funds." />);
        return;
      } else if (coin === "SC" && userWallet.scCoin === 0) {
        openModal(<ZeroBalancePopUp message="You have zero SC Coins! Please add funds." />);
        return;
      } else {
        router.push(`/game/${slugify(provider)}/${slugify(gameName)}`);
      }
    }
  };

  const handleViewAllClick = (categoryName, categoryId) => {
    const encodedCategoryName = encodeURIComponent(categoryName);
    setCategoryTitle(categoryName);
    router.push(`/category/${encodedCategoryName}?id=${categoryId}`);
  };

  return (
    <div className=" space-y-5 sm:space-y-10">
      {lobbyGames.map((group, index) => {
        const swiperRef = useRef(null);
        const [isBeginning, setIsBeginning] = useState(true);
        const [isEnd, setIsEnd] = useState(false);
        
        const totalGames = group.subCategoryGames.length;
        const noNavigationNeeded = isMobile ? totalGames <= 3 : totalGames <= 5;

        const handleSlidePrev = () => {
          if (swiperRef.current) {
            swiperRef.current.slideTo(Math.max(swiperRef.current.activeIndex - (isMobile ? 3 : 5), 0));
          }
        };

        const handleSlideNext = () => {
          if (swiperRef.current) {
            swiperRef.current.slideTo(swiperRef.current.activeIndex + (isMobile ? 3 : 5));
          }
        };

        return (
          <div key={index} className="sm:mb-10">
            <div className="flex items-center justify-between mb-4">
              {group.subCategoryGames?.length>0 && <h3 className="text-2xl font-bold text-white-1000">{group?.name.EN}</h3>}
              <div className="flex gap-3 items-center">
                {(isMobile
                  ? group.subCategoryGames.length > 3
                  : group.subCategoryGames.length > 5) && (
                  <button
                    className="text-base font-semibold text-white-1000 underline transition-all duration-300 ease-in-out hover:scale-90"
                    onClick={() =>
                      handleViewAllClick(group?.name.EN, group?.masterGameSubCategoryId)
                    }
                  >
                    View All
                  </button>
                )}
                {group.subCategoryGames?.length>0 && <div className="flex gap-2">
                  <button onClick={handleSlidePrev} disabled={isBeginning || noNavigationNeeded}>
                    <ArrowCircleLeftIcon
                      className={`size-7 fill-steelTeal-1000 transition-opacity ${isBeginning || noNavigationNeeded
                          ? 'opacity-30 cursor-not-allowed'
                          : 'cursor-pointer'
                        }`}
                    />
                  </button>

                  <button onClick={handleSlideNext} disabled={isEnd || noNavigationNeeded}>
                    <ArrowCircleDownIcon
                      className={`size-7 -rotate-90 fill-steelTeal-1000 transition-opacity ${isEnd || noNavigationNeeded
                          ? 'opacity-30 cursor-not-allowed'
                          : 'cursor-pointer'
                        }`}
                    />
                  </button>
                </div>}
              </div>
            </div>

            <Swiper
              breakpoints={{
                0: {
                  slidesPerView: 3,
                  slidesPerGroup: 3,
                  spaceBetween: 7,
                },
                768: {
                  slidesPerView: 4,
                  slidesPerGroup: 4,
                  spaceBetween: 14,
                },
                   1024: {
                  slidesPerView: 5,
                    slidesPerGroup: 5,
                  spaceBetween: 14,
                },
              }}
              modules={[Mousewheel]}
              mousewheel={{ forceToAxis: true }}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              className="!px-1"
            >
              {group.subCategoryGames.map((game, index) => (
                <SwiperSlide key={`${game?.masterCasinoGameId || game.id}-${index}`} style={{ overflow: 'visible', padding: '10px 0' }}>
                  <GameCard
                    src={game?.imageUrl || game?.url_thumb || defaultImage}
                    alt={game?.name}
                    width="200"
                    height="200"
                    onClick={() =>
                      handleGameClick(game?.name, game?.MasterCasinoProvider?.name)
                    }
                    isFavorite={game?.FavoriteGames}
                    gameId={game?.masterCasinoGameId}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        );
      })}
    </div>
  );
}
