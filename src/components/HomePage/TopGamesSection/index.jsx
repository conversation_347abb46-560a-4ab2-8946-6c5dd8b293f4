'use client';

import useEmblaCarousel from 'embla-carousel-react';
import GameCard from '@/components/Common/GameCard';
import { topGamesImages } from '@/config/gameData';
import {
  PrevButton,
  NextButton,
  usePrevNextButtons,
} from '../../Common/Button/EmblaArrowButtons/index';

export default function TopGamesSection(props) {
  const { slides, options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container">
      <div className="flex items-center justify-between gap-4 px-6 pt-6">
        <h6 className="text-xl font-bold text-white-1000">Top 20 Games</h6>

        <div className="embla__buttons grid grid-cols-2">
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
          <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
        </div>
      </div>

      <div className="embla">
        <div
          className="embla__viewport overflow-hidden px-4 pb-4 pt-7"
          ref={emblaRef}
        >
          <div className="embla__container -mx-2 flex">
            {topGamesImages.map((game, index) => (
              <div
                className="embla__slide min-w-0 shrink-0	grow-0 basis-2/4 px-2 xs:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/4 xxl:basis-1/5"
                key={index}
              >
                <div className="column flex flex-col gap-4">
                  <GameCard
                    src={game?.[0].src}
                    alt={game?.[0].alt}
                    width="10000"
                    height="10000"
                    onClick={() => console.log(`Game ${index + 1} clicked`)}
                  />
                  <GameCard
                    src={game?.[1].src}
                    alt={game?.[1].alt}
                    width="10000"
                    height="10000"
                    onClick={() => console.log(`Game ${index + 1} clicked`)}
                  />
                </div>
              </div>
            ))}
            {topGamesImages.map((game, index) => (
              <div
                className="embla__slide min-w-0 shrink-0	grow-0 basis-2/4 px-2 xs:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/4 xxl:basis-1/5"
                key={index}
              >
                <div className="column flex flex-col gap-4">
                  <GameCard
                    src={game?.[0].src}
                    alt={game?.[0].alt}
                    width="10000"
                    height="10000"
                    onClick={() => console.log(`Game ${index + 1} clicked`)}
                  />
                  <GameCard
                    src={game?.[1].src}
                    alt={game?.[1].alt}
                    width="10000"
                    height="10000"
                    onClick={() => console.log(`Game ${index + 1} clicked`)}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
