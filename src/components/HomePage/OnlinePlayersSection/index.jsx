'use client';

import CustomImage from '@/components/Common/CustomImage';
import PlayerCard from '@/components/Common/PlayerCard';
import useLivePlayers from '@/hooks/useLivePlayers';
import useAuthStore from '@/store/useAuthStore';
import usePlayerStore from '@/store/usePlayerStore';

export default function OnlinePlayersSection() {
  const { onlinePlayers } = useLivePlayers();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { livePlayers, isPlayerLoading } = usePlayerStore((state) => state);

  return isAuthenticated ? (
    <section className="mb-10 rounded-lg p-3">
      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Online Players</h6>
      </div>
      {isPlayerLoading && (
        <div className=" scrollbar-none flex flex-wrap gap-3 overflow-x-auto sm:grid sm:grid-cols-8 sm:gap-4 md:grid-cols-10 lg:grid-cols-12">
          {[...Array(5)].map((_, index) => (
            <CustomImage
              key={index}
              src="/"
              alt="Profile"
              width={75}
              height={75}
              className="h-full w-full max-w-full rounded-lg object-cover object-center"
              skeletonWidth={75}
              skeletonHeight={75}
            />
          ))}
        </div>
      )}

      {livePlayers && livePlayers.length > 0 ? (
        <div className="scrollbar-none flex gap-3 overflow-x-auto sm:grid sm:grid-cols-8 sm:gap-4 md:grid-cols-10 lg:grid-cols-12">
          {livePlayers.map((player, index) => (
            <PlayerCard
              key={index}
              width="1000"
              height="1000"
              player={player}
            />
          ))}
        </div>
      ) : (
        !isPlayerLoading && (
          <div className="py-4 text-center text-white-1000">
            No online players at the moment.
          </div>
        )
      )}
    </section>
  ) : null;
}
