'use client';

import { useRouter } from 'next/navigation';
import useEmblaCarousel from 'embla-carousel-react';
import GameCard from '@/components/Common/GameCard';
import { useGetCustomGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from '@/components/Common/Button/EmblaArrowButtons';
import toast from 'react-hot-toast';
import useModalStore from '@/store/useModalStore';
import ZeroBalancePopUp from '@/components/Models/ZeroBalancePopUp';
export default function CustomGame() {
  const router = useRouter();
  const { userDetails,isAuthenticated,userWallet,coin } = useAuthStore((state) => state);
  const { data: customGames, isLoading } = useGetCustomGamesQuery({
    enabled: isAuthenticated,
  });
  const { openModal } = useModalStore((state) => state);
  const options = { slidesToScroll: 'auto' };
  const [emblaRef, emblaApi] = useEmblaCarousel(options);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 shadow-container">
      <div className="flex items-center justify-between gap-4 px-6 pt-6">
        <h6 className="text-xl font-bold text-white-1000">Hot Games</h6>

        {customGames?.rows && customGames?.rows?.length > 5 && (
          <div className="embla__buttons grid grid-cols-2">
            <PrevButton
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
            />
            <NextButton
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
            />
          </div>
        )}
      </div>

      <div className="embla">
        <div
          className="embla__viewport overflow-hidden px-4 pb-4 pt-7"
          ref={emblaRef}
        >
          <div className="embla__container -mx-3 flex">
            {customGames?.rows?.map((game, index) => (
              <div
                className="embla__slide min-w-0 shrink-0	grow-0 basis-2/4 px-3 xs:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/4 xxl:basis-1/5"
                key={index}
              >
                <div className="column flex flex-col gap-4">
                  <GameCard
                    key={game?.name}
                    src={game?.MasterCasinoGamesThumbnails?.[0]?.thumbnail}
                    alt={game?.name}
                    gameId={game?.masterCasinoGameId}
                    isFavorite={game?.FavoriteGames}
                    width="10000"
                    height="10000"
                    onClick={() => {
                      if(userDetails?.isRestrict){
                        toast.error("You are restricted, Please contact administrator")
                        return
                      }
                      if (coin === "GC") {
                        if (userWallet.gcCoin === 0) {
                        
                          openModal(<ZeroBalancePopUp message="You have zero GC Coins! Please add funds." />);
                          return; 
                        }
                      } else if (coin === "SC") {
                        if (userWallet.scCoin === 0) {
                  
                          openModal(<ZeroBalancePopUp message="You have zero SC Coins! Please add funds." />);
                          return; 
                        }
                      }
                      router.push(`/game/${game?.masterCasinoGameId}`);
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
