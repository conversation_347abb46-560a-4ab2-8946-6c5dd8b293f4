'use client';

import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import BonusPlayIcon from '@/assets/icons/Bonus-Play';
import BonusBg1 from '@/assets/images/stock-images/bonus-bg-01.png';
import BonusFg1 from '@/assets/images/stock-images/bonus-fg-01.png';
import BonusBg2 from '@/assets/images/stock-images/bonus-bg-02.png';
import BonusFg2 from '@/assets/images/stock-images/bonus-fg-02.png';
import BonusBg3 from '@/assets/images/stock-images/bonus-bg-03.png';
import BonusFg3 from '@/assets/images/stock-images/bonus-fg-03.png';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from '@/components/Common/Button/EmblaArrowButtons';

export default function BonusSection(props) {
  const { slides, options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <section className="mb-10 rounded-lg p-6">
      {/* <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Bonus</h6>
        <IconButton>
          <ArrowCircleRightIcon className="fill-steelTeal-1000" />
        </IconButton>
      </div> */}

      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Bonus</h6>

        <div className="embla__buttons grid grid-cols-2">
          <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
          <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
        </div>
      </div>

      <div className="embla">
        <div className="embla__viewport overflow-hidden pt-7" ref={emblaRef}>
          <div className="embla__container -mx-1.5 flex md:-mx-[0.625rem]">
            <div className="embla__slide p-1.5 md:p-[0.625rem]">
              <div className="w-[8.375rem] md:w-[12.5rem]">
                <div className="relative h-full rounded-lg">
                  <Image
                    src={BonusBg1}
                    width={10000}
                    height={10000}
                    className="h-full w-full max-w-full rounded-lg object-cover object-center"
                    alt="Bonus BG"
                  />
                  <Image
                    src={BonusFg1}
                    width={10000}
                    height={10000}
                    className="absolute -top-[7.5%] left-2/4 w-full max-w-[80%] -translate-x-2/4"
                    alt="Bonus FG"
                  />
                  <div className="absolute bottom-2 left-2 flex items-center justify-center gap-2 md:bottom-3 md:left-3">
                    <BonusPlayIcon className="h-3.5 w-3.5	fill-white-1000 md:h-5 md:w-5" />
                    <span className="mt-1 inline-block text-xs font-bold leading-none text-white-1000 md:text-sm">
                      Bonus 1
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="embla__slide p-1.5 md:p-[0.625rem]">
              <div className="w-[17.063rem] md:w-[25.438rem]">
                <div className="relative h-full rounded-lg">
                  <Image
                    src={BonusBg2}
                    width={10000}
                    height={10000}
                    className="h-full w-full max-w-full rounded-lg object-cover object-center"
                    alt="Bonus BG"
                  />
                  <Image
                    src={BonusFg2}
                    width={10000}
                    height={10000}
                    className="absolute -top-[7.5%] left-2/4 w-full max-w-[80%] -translate-x-2/4"
                    alt="Bonus FG"
                  />
                  <div className="absolute bottom-2 left-2 flex items-center justify-center gap-2 md:bottom-3 md:left-3">
                    <BonusPlayIcon className="h-3.5 w-3.5	fill-white-1000 md:h-5 md:w-5" />
                    <span className="mt-1 inline-block text-xs font-bold leading-none text-white-1000 md:text-sm">
                      Bonus 2
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="embla__slide p-1.5 md:p-[0.625rem]">
              <div className="w-[17.063rem] md:w-[25.438rem]">
                <div className="relative h-full rounded-lg">
                  <Image
                    src={BonusBg3}
                    width={10000}
                    height={10000}
                    className="h-full w-full max-w-full rounded-lg object-cover object-center"
                    alt="Bonus BG"
                  />
                  <Image
                    src={BonusFg3}
                    width={10000}
                    height={10000}
                    className="absolute -top-[7.5%] right-[0] w-full max-w-[60%]"
                    alt="Bonus FG"
                  />
                  <div className="absolute bottom-2 left-2 flex items-center justify-center gap-2 md:bottom-3 md:left-3">
                    <BonusPlayIcon className="h-3.5 w-3.5	fill-white-1000 md:h-5 md:w-5" />
                    <span className="mt-1 inline-block text-xs font-bold leading-none text-white-1000 md:text-sm">
                      Bonus 3
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <div className="-mx-2 flex flex-wrap justify-center">

        
      </div> */}
    </section>
  );
}
