'use client';

import FavoriteStrokeIcon from '@/assets/icons/Favorite-Stroke';
import SortIcon from '@/assets/icons/Sort';
import useLobby from '@/hooks/useLobby';
import useGameStore from '@/store/useGameStore';
import { useState } from 'react';
import { useDebounce } from 'use-debounce';
import { useRouter } from 'next/navigation';
import ReactTooltip from '@/components/Common/ReactTooltip';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
const options = [
  // { value: 'Casino', label: 'Casino' },
  // { value: 'Sport', label: 'Sport' },
];

function GamesFilter() {
  const { userDetails, isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const [selectedOption, setSelectedOption] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { clearSearch } = useGameStore();
  const router = useRouter();
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);

  useLobby(debouncedSearchTerm);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  return (
    <section className="mb-[1.125rem]">
      <div className="flex items-center justify-between gap-[0.375rem] sm:gap-3">
        <div className="flex h-11 w-full   items-center gap-4 rounded-[0.625rem] bg-maastrichtBlue-1000 px-2 py-1">
          {/* <Select
            className="provider-react-select"
            classNamePrefix="provider-react"
            defaultValue={selectedOption}
            onChange={setSelectedOption}
            options={options}
            placeholder="Provider"
          /> */}

          <div className="relative h-10 w-full rounded-[0.625rem] hover:border-2 hover:border-primary-1000">
            {/* Input Field */}
            <input
              type="text"
              className="h-full w-full rounded border border-none bg-transparent pl-3 pr-8 text-sm font-normal placeholder:text-gray-400 focus:outline-none"
              placeholder="Search by games"
              onChange={handleSearchChange}
              value={searchTerm}
            />

            {/* Clear Icon */}
            {searchTerm && (
              <span
                className="absolute right-2 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={() => setSearchTerm('')}
                style={{ fontSize: '16px' }}
              >
                &#x2715; {/* Unicode for a cross symbol (X) */}
              </span>
            )}
          </div>
        </div>

        <div className="flex  items-center justify-end gap-[0.375rem] sm:max-w-[15.75rem] sm:gap-3">
          {/* <button className="group flex h-11 w-full min-w-11 items-center justify-center rounded-[0.625rem] bg-oxfordBlue-1000 transition-all duration-300 hover:bg-primary-1000">
            <SortIcon className="m-auto h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          </button> */}
          {/* <div className="w-full max-xl:hidden uppertab:hidden">
            <Select
              className="sort-react-select"
              classNamePrefix="sort-react"
              defaultValue={selectedOption}
              onChange={setSelectedOption}
              options={options}
              placeholder="Relevant"
            />
          </div> */}
          {/* <button className="group flex h-11 w-11 items-center justify-center rounded-[0.625rem] bg-oxfordBlue-1000 transition-all duration-300 hover:bg-primary-1000 lgx:hidden uppertab:flex">
            <SortIcon className="m-auto h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          </button> */}
          <ReactTooltip message={'Favorites'} id="FavoritesIconButton" />
          <button
            id={'FavoritesIconButton'}
            onClick={() => {
              if (!isAuthenticated) {
                localStorage.setItem('activeTab', 0);
                setSelectedTab(0);
                openModal(<Auth />);
                return;
              }
              router.push('/favorites')
            }}
            className="group flex h-11 w-11 min-w-11 items-center justify-center rounded-[0.625rem] bg-maastrichtBlue-1000 transition-all duration-300 hover:bg-primary-1000"
          >
            <FavoriteStrokeIcon className="m-auto h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
          </button>
        </div>
      </div>
    </section>
  );
}

export default GamesFilter;
