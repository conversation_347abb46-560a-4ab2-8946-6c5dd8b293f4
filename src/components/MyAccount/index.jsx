'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Select from 'react-select';
import { toast } from 'react-hot-toast';
import { usePathname, useRouter } from 'next/navigation';
import { useUploadProfileImageMutation } from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import { useQueryClient } from '@tanstack/react-query';
import EditIcon from '../../assets/images/demo-image/edit-icon.svg';
import UserAvatar from '../DefaultImage';
import Skeleton from 'react-loading-skeleton';

const sideLinkOptions = [
  { value: 'Profile', label: 'Profile', href: '/user/profile' },
  { value: 'Email', label: 'Email', href: '/user/email' },
  { value: 'Password', label: 'Password', href: '/user/password' },
  { value: 'Ignored User', label: 'Ignored Users', href: '/user/ignored-users' },
  { value: 'KYC', label: 'KYC', href: '/user/kyc' },
];

function MyAccount() {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();

  const { userDetails, setUserDetails } = useAuthStore((state) => ({
    userDetails: state.userDetails,
    setUserDetails: state.setUserDetails,
  }));

  const [selectedOption, setSelectedOption] = useState(
    sideLinkOptions.find((option) => option.href === pathname) || sideLinkOptions[0]
  );
  const [profileImage, setProfileImage] = useState('');
  const [imageLoaded, setImageLoaded] = useState(false);
  const [cacheBust, setCacheBust] = useState(Date.now().toString());
  const [imgError, setImgError] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  const { mutate: uploadProfileImage } = useUploadProfileImageMutation({
    onSuccess: (data) => {
      const uploadedImage = data?.data?.data?.profileImage;
      if (uploadedImage) {
        setUserDetails({ ...userDetails, profileImage: uploadedImage });
        setProfileImage(`${uploadedImage}?t=${Date.now()}`);
        setCacheBust(Date.now().toString());
        setImgError(false);
        setImageLoaded(false); 
        toast.success('Profile image uploaded successfully!');
        queryClient.invalidateQueries(['GET_PublicChatsQuery']);
      }
      setIsUploading(false);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Something went wrong';
      toast.error(message);
      setIsUploading(false);
    },
  });

  useEffect(() => {
    if (userDetails?.profileImage) {
      setProfileImage(`${userDetails.profileImage}?t=${cacheBust}`);
    }
  }, [userDetails?.profileImage, cacheBust]);

  const handleRouteChange = (item) => {
    setSelectedOption(item);
    router.push(item.href);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setIsUploading(true);
      setImageLoaded(false);
      setImgError(false);
      const formData = new FormData();
      formData.append('profile', file);
      uploadProfileImage(formData);
    }
  };

  return (
    <div className="w-full max-w-profileSidebar rounded-xl bg-richBlack-200 p-[56px_21px] max-xxl:max-w-[200px] max-xxl:p-[56px_8px] max-xl:items-center max-lg:flex max-lg:max-w-[768px] max-lg:justify-evenly max-lg:rounded-[0_40px_0_0] max-lg:py-8 max-xs:justify-between max-xs:gap-6">
      <div className="text-center justify-items-center">
        <div className="relative flex justify-center w-[120px] h-[120px] max-sm:w-[75px] max-sm:h-[75px]">
          {profileImage && !imgError ? (
            <>
              {!imageLoaded && (
                <div className="absolute inset-0">
                  <Skeleton
                    height="100%"
                    width="100%"
                    borderRadius="9999px"
                    containerClassName="w-full h-full"
                  />
                </div>
              )}
              <Image
                src={profileImage}
                alt="Profile"
                className={`w-full h-full rounded-full object-cover transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                width={120}
                height={120}
                onLoad={() => {
                  setImageLoaded(true);
                  setImgError(false);
                }}
                onError={() => {
                  setImgError(true);
                  setImageLoaded(true);
                }}
                priority
              />
            </>
          ) : (
            <div className="w-full h-full rounded-full object-cover overflow-hidden bg-gray-500 text-white flex items-center justify-center text-xl font-semibold">
              <UserAvatar
                firstName={userDetails?.firstName}
                lastName={userDetails?.lastName}
                userName={userDetails?.username}
                className="w-full h-full rounded-full object-cover"
                size={120}
              />
            </div>
          )}

          <label
            htmlFor="userImage"
            className={`absolute bottom-0 right-0 ${isUploading ? 'cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <Image
              src={EditIcon}
              width={40}
              height={40}
              className="h-10 w-10 max-sm:h-7 max-sm:w-7"
              alt="Edit"
            />
            <input
              id="userImage"
              type="file"
              className="hidden"
              onChange={handleFileChange}
              disabled={isUploading}
            />
          </label>
        </div>

        <p className="pt-2 text-[25px] font-normal max-xxl:text-xl">
          {userDetails?.username}
        </p>
      </div>

      <div className="w-1/2 lg:hidden max-xs:w-full">
        <Select
          className="profile-menu-select"
          classNamePrefix="profile-menu"
          placeholder="Select Country"
          defaultValue={selectedOption}
          onChange={handleRouteChange}
          options={sideLinkOptions}
          isSearchable={false}
        />
      </div>

      <div className="px-2.5 pt-[72px] max-xxl:px-2 max-lg:hidden">
        {sideLinkOptions?.map((sideLink) => {
          return (
            <Link
              key={sideLink?.href}
              className="text-primaryColor-500 hover:text-white group relative z-[1] mb-4 inline-block w-full cursor-pointer overflow-hidden rounded-[10px] bg-transparent p-[11px_10px] text-xl capitalize leading-none transition-all duration-500 ease-in-out max-xxl:mb-1 max-xxl:text-base"
              href={sideLink?.href}
            >
              <span className="absolute inset-0 z-[-1] bg-nav-gradient opacity-0 transition-all duration-500 ease-in-out group-hover:opacity-100" />
              {sideLink?.label}
            </Link>
          );
        })}
      </div>
    </div>
  );
}

export default MyAccount;
