'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import StoreBanner from '../../../assets/images/stock-images/store-banner.png';
import StoreBannerMobile from '../../../assets/images/stock-images/store-banner-mobile.png';
import CoinAC from '../../../assets/images/stock-images/coin-ac-large.png';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';

function Buy() {
  const coinVariants = {
    initial: { rotateY: 0 },
    hover: {
      rotateY: 360,
      transition: {
        duration: 1, // Duration of the rotation
        ease: 'linear', // Linear easing
        repeat: Infinity,
      },
    },
  };

  return (
    <div>
      <div className="mb-[0.625rem]">
        <Image
          src={StoreBanner}
          width={1000}
          height={1000}
          className="mx-w-full hidden w-full sm:block"
          alt="Store Banner"
        />
        <Image
          src={StoreBannerMobile}
          width={1000}
          height={1000}
          className="mx-w-full block w-full sm:hidden"
          alt="Store Banner"
        />
      </div>

      <div className="px-3 py-7 md:px-9">
        <div className="-m-5 flex flex-nowrap gap-5 overflow-x-auto p-5 md:grid md:grid-cols-3 md:gap-11 lg:grid-cols-3 xl:grid-cols-3 xxl:grid-cols-3">
          {Array(8)
            .fill('')
            .map((item) => {
              return (
                <div className="relative shrink-0 grow-0 basis-[11.875rem] cursor-pointer rounded-md bg-oxfordBlue-1000 pb-[0.625rem] pt-3.5 shadow-[0px_5px_7px_var(--richBlack-1000)] transition-all duration-300 before:absolute before:inset-0 before:z-[-1] before:block before:h-full before:w-full before:rounded-md before:bg-[radial-gradient(var(--primary-1000),_var(--oxfordBlue-1000))] before:transition-all before:duration-300 before:hover:rotate-[-5deg] before:hover:scale-105">
                  <h6 className="bg-cetaceanBlue-1000 px-4 pb-1 pt-2 text-center text-[0.813rem] font-normal leading-none text-white-1000">
                    10 Free AC Cash
                  </h6>
                  <div className="mx-auto mb-2 mt-3 flex aspect-square w-[56%] max-w-[6.625rem] items-center justify-center">
                    <motion.div
                      variants={coinVariants}
                      initial="initial"
                      whileHover="hover"
                      className="mx-w-full w-full"
                    >
                      <Image
                        src={CoinAC}
                        width={1000}
                        height={1000}
                        alt="Coin"
                      />
                    </motion.div>
                  </div>
                  <h6 className="px-4 text-center font-normal leading-none text-white-1000">
                    <span className="block text-xl font-bold leading-tight">
                      100,000
                    </span>
                    <span className="block text-base uppercase leading-tight">
                      Alibaba COINS
                    </span>
                  </h6>
                  <div className="mt-2 px-3">
                    <PrimaryButton className="w-full">$10</PrimaryButton>
                  </div>
                </div>
              );
            })}
        </div>
      </div>

      {/* <div className="rounded-[0.625rem] border border-dashed border-steelTeal-1000">
        <h6 className="px-3 py-5 text-center text-base font-normal leading-none text-steelTeal-1000">
          Maximum purchase of $9000 USD per day
        </h6>
      </div> */}
    </div>
  );
}

export default Buy;
