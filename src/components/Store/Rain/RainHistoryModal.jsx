'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { X } from 'lucide-react';
import Image from 'next/image';
import RainIcon from '@/assets/icons/Rain';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import { useRainTransactions } from '@/hooks/useRainTransactions';

const UserInfo = React.memo(({ username, profileImage }) => (
  <div className="flex flex-col items-center">
    <div className="relative mb-2">
      <Image
        src={profileImage}
        alt={username}
        className="rounded-full"
        width={60}
        height={60}
      />
    </div>
    <p className="text-lg font-bold text-white-1000">{username}</p>
  </div>
));

const TransactionsTable = React.memo(({ transactions }) => (
  <div className="mt-4 max-h-[40vh] overflow-y-auto text-center">
    <table className="w-full bg-maastrichtBlue-1000 text-left text-white-1000">
      <thead>
        <tr className="border-b border-white-100">
          <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
            User
          </th>
          <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
            Time
          </th>
          <th className="whitespace-nowrap px-2 py-4 text-left text-sm font-bold text-steelTeal-1000">
            Amount
          </th>
        </tr>
      </thead>
      <tbody>
        {transactions.map((entry, index) => (
          <tr key={index} className={`text-sm`}>
            <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
              {entry.username}
            </td>
            <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
              {entry.time}
            </td>
            <td className="whitespace-nowrap px-2 py-4 text-left text-sm font-normal text-white-1000">
              {entry.amount.toFixed(4)} AC
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
));

function RainHistoryModal({ rainDrop, chat }) {
  const { closeModal } = useModalStore((state) => state);
  const { rainTransactions, isLoading, isError } = useRainTransactions(
    chat?.rainDrop?.rainId,
  );
  const [formattedData, setFormattedData] = useState([]);

  useEffect(() => {
    if (rainTransactions) {
      const filteredTransactions = rainTransactions.rows.filter(
        (transaction) => transaction?.User?.userId !== chat?.rainDrop?.userId,
      );

      const formatted = filteredTransactions.map((transaction) => ({
        username: transaction?.User?.username,
        time: new Date(transaction?.createdAt).toLocaleString(),
        amount: transaction?.amount,
      }));

      setFormattedData(formatted);
    }
  }, [rainTransactions, chat?.rainDrop?.userId]);

  const handleCloseModal = () => {
    closeModal();
  };

  const memoizedTransactionsTable = useMemo(() => {
    return <TransactionsTable transactions={formattedData} />;
  }, [formattedData]);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-xl">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <div className="flex items-center gap-3">
              <RainIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                Rain
              </h3>
            </div>
            <div className="flex items-center gap-4">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>

          <div className="p-4">
            {isLoading ? (
              <p className="text-center text-white-1000">Loading...</p>
            ) : isError ? (
              <p className="text-center text-red-500">Error loading data.</p>
            ) : (
              <>
                <UserInfo
                  username={chat?.user?.username}
                  profileImage={chat?.user?.profileImage}
                />
                {memoizedTransactionsTable}
              </>
            )}

            <div className="mt-6 text-center">
              <button
                onClick={handleCloseModal}
                className="w-full rounded-lg bg-primary-1000 py-2 font-semibold tracking-wide text-white-1000"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RainHistoryModal;
