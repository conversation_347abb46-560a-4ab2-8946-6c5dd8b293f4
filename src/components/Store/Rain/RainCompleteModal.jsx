'use client';

import React, { useCallback, useMemo } from 'react';
import { X } from 'lucide-react';
import Image from 'next/image';
import useModalStore from '@/store/useModalStore';
import IconButton from '@/components/Common/Button/IconButton';
import RainHistoryModal from './RainHistoryModal';
import RainIcon from '@/assets/icons/Rain';

const UserInfo = React.memo(({ profileImage, username }) => (
  <div className="flex flex-col items-center">
    <Image
      src={profileImage}
      alt={username}
      className="rounded-full"
      width={80}
      height={80}
    />
    <p className="text-white mt-4 text-lg font-bold">{username}</p>
    <div className="mt-2 w-full rounded-lg bg-red-800 px-1 py-2 text-center">
      <p className="text-md text-white font-semibold tracking-normal">
        Rain Drop is complete
      </p>
    </div>
  </div>
));

const RainCompleteModal = ({ rainDrop, chat }) => {
  const { closeModal, openModal } = useModalStore((state) => state);

  const handleCloseModal = useCallback(() => {
    closeModal();
  }, [closeModal]);

  const handleViewHistory = useCallback(() => {
    openModal(<RainHistoryModal rainDrop={rainDrop} chat={chat} />);
  }, [openModal, rainDrop, chat]);

  const memoizedUserInfo = useMemo(() => {
    return (
      <UserInfo
        profileImage={chat?.user?.profileImage}
        username={chat?.user?.username}
      />
    );
  }, [chat]);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-md">
        <div className="relative overflow-hidden rounded-lg bg-maastrichtBlue-1000 p-6 shadow-lg">
          <div className="relative z-10">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <RainIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                  Rain
                </h3>
              </div>
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="fill-white h-5 w-5 transition-all duration-300 group-hover:fill-gray-300" />
              </IconButton>
            </div>

            {memoizedUserInfo}

            <div className="mt-6 text-center">
              <button
                onClick={handleViewHistory}
                className="text-md text-white w-full rounded-lg py-2 font-semibold tracking-wide underline"
              >
                View History
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RainCompleteModal;
