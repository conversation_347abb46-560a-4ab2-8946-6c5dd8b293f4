'use client';
import React from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import TaskList from '../TaskList';

function TaskListModal({}) {
  const { closeModal } = useModalStore((state) => state);

  const handleCloseModal = () => {
    closeModal();
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto "
    >
      <div className="relative max-h-[90vh] w-full max-w-3xl">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-2 shadow-lg">
          <div className="flex items-center justify-between p-2">
            <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
              Task List
            </h3>
            <div className="flex items-center gap-4">
              <IconButton
                onClick={handleCloseModal}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="max-h-[70vh] overflow-y-auto p-4">
            <TaskList />
          </div>
        </div>
      </div>
    </div>
  );
}

export default TaskListModal;
