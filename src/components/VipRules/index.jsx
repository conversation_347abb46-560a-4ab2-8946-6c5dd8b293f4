import React from 'react';
import Image from 'next/image';
import VipClubImg from '../../assets/images/stock-images/vip-club.png';
import VipTearSlider from './VipTearSlider';
function VipRules() {
  return (
    <div className="mt-10 flex items-end gap-2">
      <div className="w-full max-w-[160px]">
        <div className="flex items-center justify-center">
          <Image
            src={VipClubImg}
            width={10000}
            height={10000}
            className="h-[62px] w-[112px] rounded-lg object-contain max-md:h-12 max-md:w-12"
            alt="product image"
          />
        </div>
        <div className="mt-3.5">
          <ul className="flex flex-col gap-2">
            {[
              'wager amount',
              'Login Bonus',
              'Daily Bonus',
              'Weekly Bonus',
              'Monthly Bonus',
              'Rakeback',
              'Cash Back',
              'Level Up Bonus',
            ].map((item) => (
              <li
                key={item}
                className="textb-base flex h-12 items-center justify-center rounded-lg bg-vipClub px-2 py-2 capitalize max-sm:text-sm"
              >
                {item}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="w-full">
        <VipTearSlider />
      </div>
    </div>
  );
}

export default VipRules;
