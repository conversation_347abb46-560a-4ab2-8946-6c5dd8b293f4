'use client';

import React from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from '../Common/Button/EmblaArrowButtons';
import VipTierCard from './VipTierCard';
import CrystalAndGemImage from '../../assets/images/CrystalsAndGemsCard.png';
import Image from 'next/image';
import ArrowVip from '@/assets/icons/Arrow-Vip';
import SliderArrowRectangle from '@/assets/icons/Slider-Arrow-Rectangle';
import Skeleton from 'react-loading-skeleton';

function VipSlider({ vipTierRules, isLoading }) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    axis: 'y',
    dragFree: true,
    loop: false,
  });

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <div className="relative h-full max-h-[838px] pb-14">
      <div
        className="embla relative h-full max-h-[757px] overflow-hidden"
        ref={emblaRef}
      >
        <div className="embla__container flex h-full flex-col gap-3.5 max-md:flex-row [&>.embla-slide]:flex-[0_0_calc(100%_/4)] [&>.emblaSlide]:mx-[1%] [&>.emblaSlide]:min-w-0">
          {/* Static Slide Content */}
          {isLoading &&
            [...Array(4)]?.map((item) => (
              <div className="embla__slide emblaSlide !mx-0 ">
                <div className="w-full min-w-[298px]">
                  <div className="flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 shadow-vipCardShasow ">
                    <Skeleton width={250} height={120} className={`block`} />
                  </div>
                </div>
              </div>
            ))}
          {vipTierRules?.map((vip) => {
            return(
            <div className="embla__slide emblaSlide !mx-0 ">
              <div className="w-full min-w-[298px]">
                <div className={`flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 ${vip?.isUserActiveTier ? "shadow-vipCardShasow": "shadow-vipCardShasowdisable" }`}>
                  <div>
                    <Image
                      src={CrystalAndGemImage}
                      className={`${vip?.isUserActiveTier ? "": "grayscale" }`}
                      width="80px"
                      height="80px"
                      alt=""
                    />
                  </div>
                  <div className="flex min-w-[133px] max-w-[297px] flex-col gap-4">
                    <span className={`text-[28px] font-black leading-8 ${vip?.isUserActiveTier ? "text-primary-900": "text-richBlack-950" }` }>
                    VIP
                      <br/>
                      {vip?.name?.split("VIP")[1]}
                    </span>
                    <span className="text-xl font-bold leading-[18px]">
                      {(+vip?.wagerAmount)?.toLocaleString()}{' '}
                      <span className="text-richBlack-700 text-base font-normal">
                        Pts
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
          )}
          {/* <div className="embla__slide emblaSlide !mx-0 ">
            <div className="w-full min-w-[298px]">
              <div className="shadow-vipCardShasow flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] min-w-[133px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-primary-900">
                    VIP <br/> Gold
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/> Platinum I
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px]  px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/> Platinum II
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px]  px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/>  Platinum III
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px] px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/> Platinum I
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px]  px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/> Platinum II
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="embla__slide emblaSlide !mx-0 ">
            <div className="  w-full min-w-[298px]">
              <div className="shadow-vipCardShasowdisable flex h-full w-full items-center justify-center gap-4 rounded-[45px]  px-4 py-9 ">
                <div>
                  <Image
                    src={CrystalAndGemImage}
                    className="grayscale"
                    width="80px"
                    height="80px"
                    alt=""
                  />
                </div>
                <div className="flex max-w-[297px] flex-col gap-4">
                  <span className="text-[28px] font-black leading-8 text-richBlack-500">
                    VIP <br/>  Platinum III
                  </span>
                  <span className="text-xl font-bold leading-[18px]">
                    4500{' '}
                    <span className="text-base font-normal text-richBlack-700">
                      Pts
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div> */}
        </div>

        {/* Position the controls */}
      </div>
      <PrevButton
        onClick={onPrevButtonClick}
        disabled={prevBtnDisabled}
        className="text-white  bg-black absolute -left-[30px] top-[76px] rounded-full p-2 [&>.ArrowCircleLeftIcon]:hidden"
      >
        <SliderArrowRectangle />
      </PrevButton>
      <div className="embla__controls absolute bottom-0 left-0 right-0 z-10 flex -translate-y-1/2 transform justify-between px-4">
        <div className="absolute bottom-0 right-14 flex w-full max-w-[195px] items-center gap-5">
          <span className="text-steelTeal-500 text-end text-[13px] font-normal leading-3">
            SCROLL DOWN FOR MORE RANK
          </span>
          <NextButton
            onClick={onNextButtonClick}
            disabled={nextBtnDisabled}
            // Icon = {}
            className="text-white bg-black rounded-full pr-[5px] [&>.ArrowCircleRightIcon]:hidden"
          >
            <ArrowVip className="h-[50px] w-[50px]" />
          </NextButton>
        </div>
      </div>
    </div>
  );
}

export default VipSlider;
