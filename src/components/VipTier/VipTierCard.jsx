import React from 'react';
import CrystalAndGemImage from '../../assets/images/CrystalsAndGemsCard.png';
import Image from 'next/image';

const VipTierCard = () => {
  return (
    <>
    <div className='min-w-[298px] w-full'>
      <div className="flex shadow-vipCardShasow rounded-[45px] h-full w-full items-center justify-center gap-4 px-[50px] py-9 ">
        <div>
          <Image src={CrystalAndGemImage} width="80px" height="80px" alt="" />
        </div>
        <div className="flex max-w-[297px] flex-col gap-4">
          <span className="text-[28px] font-black leading-8 text-primary-900">
            VIP Gold
          </span>
          <span className="text-xl font-bold leading-[18px]">
            4500 <span className="text-richBlack-700 font-normal text-base">Pts</span>
          </span>
        </div>
      </div>
    </div>
    
    <div className='min-w-[298px] w-full mt-3.5'>
      <div className="flex shadow-vipCardShasowdisable rounded-[45px] h-full w-full items-center justify-center gap-4 px-[50px] py-9 ">
        <div>
          <Image src={CrystalAndGemImage} className='grayscale' width="80px" height="80px" alt="" />
        </div>
        <div className="flex max-w-[297px] flex-col gap-4">
          <span className="text-[28px] font-black leading-8 text-richBlack-500">
            VIP Gold
          </span>
          <span className="text-xl font-bold leading-[18px]">
            4500 <span className="text-richBlack-700 font-normal text-base">Pts</span>
          </span>
        </div>
      </div>
    </div>
    </>
  );
};

export default VipTierCard;
