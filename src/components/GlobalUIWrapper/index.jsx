'use client';

import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import { usePathname } from 'next/navigation';
import Home from '../Home';
import { useEffect, useRef } from 'react';
import { playerActivitySocket } from '@/utils/socket';

const GlobalUIWrapper = ({ children }) => {
  const { openChat, toggleSideMenu, showHomePopup, setShowHomePopup } =
    useGeneralStore((state) => state);
  const { isAuthenticated, userDetails } = useAuthStore((state) => state);
  const pathname = usePathname();
  const prevPath = useRef(pathname);

  useEffect(() => {
    if (prevPath.current !== pathname) {
      setShowHomePopup(false);
      if (prevPath?.current?.startsWith('/game')) {
        playerActivitySocket.emit('USER_LEFT_GAMEPLAY', {
          userId: userDetails?.userId,
        });
      }
      prevPath.current = pathname;
    }
  }, [pathname, setShowHomePopup]);

  return (
    <>
      <div className={`${showHomePopup ? 'hidden' : 'block'}`}>{children}</div>
      {showHomePopup && (
        <div
          className={`${pathname == '/vip' && 'bg-vip-tier-bg bg-cover bg-no-repeat'}  ${openChat ? 'lg:mr-[20.5rem] lg:w-[calc(100%-14.75rem-20.5rem)]' : 'lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)]'} ${toggleSideMenu ? 'lg:ml-[6rem]' : ' lg:ml-[14.75rem]'}  blurColor relative z-[2] min-h-dvh overflow-x-hidden px-3 pt-[4.75rem]  transition-all duration-300 ease-in-out md:px-8 md:pt-8 lg:z-[41] lg:ml-[14.75rem] `}
        >
          <div
            className={`mt-40-md-lg mx-auto w-full lg:max-w-[67.5rem] ${isAuthenticated ? '' : 'lg:pt-14'}`}
          >
            <Home />
          </div>
        </div>
      )}
    </>
  );
};

export default GlobalUIWrapper;
