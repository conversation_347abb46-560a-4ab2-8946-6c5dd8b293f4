'use client';

import { Suspense, useEffect, useState, memo } from 'react';
import MainLoader from '../Common/Loader/MainLoader';

const FallbackUI = memo(() => (
  <div className="text-white bg-black relative flex min-h-screen items-center justify-center overflow-hidden">
    <div className="z-10 w-32">
      <MainLoader />
    </div>
    <div className="absolute left-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]" />
    <div className="absolute right-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]" />
  </div>
));

export default function ClientSuspenseWrapper({ children }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything (including Suspense) until on client
  if (!isClient) return <FallbackUI />;

  return <Suspense fallback={<FallbackUI />}>{children}</Suspense>;
}
