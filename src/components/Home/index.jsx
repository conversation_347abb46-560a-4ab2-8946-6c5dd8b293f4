// 'use client';

// import Banner from '@/components/Banner';
import BetHistoryTable from '@/components/HomePage/BetHistoryTable';
// import BonusSection from '@/components/HomePage/BonusSection';
// import TopGamesSection from '@/components/HomePage/TopGamesSection';
// import HotGamesSection from '@/components/HomePage/HotGamesSection';
import OnlinePlayersSection from '@/components/HomePage/OnlinePlayersSection';
// import CustomGame from '@/components/HomePage/CustomGame';
import LobbyGames from '@/components/HomePage/LobbyGames';
import GamesFilter from '@/components/HomePage/GamesFilter';
import PromotionSlider from '@/components/PromotionBanner';
import ActivePlayer from '@/components/ActivePlayer';
import ActiveGroup from '../ActiveGroup';
import ConnectedPlay from '../ConnectedPlay';

export default function Home() {

  return (
    <>
      {/* <Banner /> */}

      <ConnectedPlay />
      <PromotionSlider />

      <ActivePlayer />
      <ActiveGroup />
      <GamesFilter />

      {/* <CustomGame /> */}

      {/* TOP 20 GAMES SECTION START */}
      <LobbyGames />
      {/* <TopGamesSection /> */}

      {/* BONUS SECTION START */}
      {/* <BonusSection /> */}

      {/* HOT GAMES SECTION START */}
      {/* <HotGamesSection /> */}

      {/* ONLINE PLAYERS SECTION START */}
      {/* <OnlinePlayersSection /> */}

      {/* BET HISTORY TABLE SECTION START */}
     <BetHistoryTable />
    </>
  );
}
