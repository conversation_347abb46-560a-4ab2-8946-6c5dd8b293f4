import React, { useEffect, useRef, useState } from 'react';
import ProfileIcon from '@/assets/icons/Profile';
import CloseIcon from '@/assets/icons/CloseIcon';
import useModalStore from '@/store/useModalStore';
import {
  pixiApplicationDestroy,
  pixiApplicationInit,
} from '@/pixi-js-scripts/bridge';
import { calculateRemainingTime } from '@/utils/helper';
import useAuthStore from '@/store/useAuthStore';
import useSpinWheelStore from '@/store/useSpinWheelStore';
import { useUpdateSpinWheelWallet } from '@/reactQuery/generalQueries';
import IconButton from '../Common/Button/IconButton';
import Timer from './Timer';

function SpinWheel() {
  const { closeModal } = useModalStore((state) => state);

  const [countdown, setCountdown] = useState();
  const musicRef = useRef(null);
  const jackpotSoundRef = useRef(null);
  const spinWheelSoundRef = useRef(null);
  const { userDetails } = useAuthStore((state) => state);
  const {
    spinWheelResult,
    setSpinWheelResult,
    jackpotSound,
    spinWheelSound,
    showClose,
  } = useSpinWheelStore((state) => state);
  const [remainingTime, setRemainingTime] = useState();
  const [isUpdated, setIsUpdated] = useState(false);

  useEffect(() => {
    if (userDetails?.spinWheelClaimedAt) {
      setCountdown(calculateRemainingTime(userDetails?.spinWheelClaimedAt));
    }
  }, [userDetails]);

  // useEffect(() => {
  //   musicRef.current = new Audio();
  //   musicRef.current.src = GameMusic;
  //   musicRef.current.loop = true;
  //   return () => {
  //     musicRef.current?.pause();
  //   };
  // }, []);

  // useEffect(() => {
  //   jackpotSoundRef.current = new Audio()
  //   jackpotSoundRef.current.src = JackpotHit
  //   jackpotSoundRef.current.loop = false
  //   spinWheelSoundRef.current = new Audio()
  //   spinWheelSoundRef.current.src = SpinWheelSound
  //   spinWheelSoundRef.current.loop = true
  //   return () => {
  //     jackpotSoundRef.current?.pause()
  //     spinWheelSoundRef.current?.pause()
  //   }
  // }, [])

  //  useEffect(() => {
  //   if(!userDetails?.isSpinWheelBonusClaimed && jackpotSound) {
  //     jackpotSoundRef.current?.play()
  //   }
  //   else {
  //     jackpotSoundRef.current?.pause()
  //   }
  // }, [jackpotSound])

  // useEffect(() => {
  //   if(!userDetails?.isSpinWheelBonusClaimed && spinWheelSound) {
  //     spinWheelSoundRef.current?.play()
  //   }
  //   else {
  //     spinWheelSoundRef.current?.pause()

  //   }
  // }, [spinWheelSound])

  const updatedWallet = useUpdateSpinWheelWallet({
    onSuccess: (res) => {
      setIsUpdated(true);
    },
    onError: (error) => {
      console.log('**************error', error);
    },
  });

  useEffect(() => {
    if (spinWheelResult?.showResult) {
      updatedWallet.mutate();
    }
  }, [spinWheelResult?.showResult]);

  const handleClose = () => {
    pixiApplicationDestroy();
    // getProfileMutation?.mutate()
    setSpinWheelResult({ showResult: false, gc: '', sc: '', index: '' });
  };

  useEffect(() => {
    pixiApplicationInit();
  }, []);

  useEffect(() => {
    if (userDetails?.spinWheelClaimedAt) {
      const intervalId = setInterval(() => {
        setRemainingTime(
          calculateRemainingTime(userDetails?.spinWheelClaimedAt),
        );
      }, 1000);

      return () => clearInterval(intervalId);
    }
  }, [userDetails]);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex h-full w-full items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <ProfileIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                Spin Wheel
              </h3>
            </div>

            <IconButton className="h-6 w-6 min-w-6" onClick={closeModal}>
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          {userDetails?.isSpinWheelBonusClaimed ? (
            <Timer
              hours={remainingTime?.hours}
              minutes={remainingTime?.minutes}
              seconds={remainingTime?.seconds}
            />
          ) : (
            <div className="">
              {!spinWheelResult?.showResult && (
                <>
                  <p className="my-8 text-center text-xl font-bold leading-5">
                    Give the wheel a whirl every day to snag <br />
                    your complimentary coins!{' '}
                  </p>
                  {/* Add Loader Here */}
                  {/* <div className='spin-loader'> <CircularProgress className='loader' style={{color: 'white' }}/></div> */}
                  <div
                    className="flex w-full items-center justify-center pb-4"
                    id="pixi-spin-wheel"
                  />
                </>
              )}
              {spinWheelResult?.showResult && (
                <div className="w-full p-4">
                  <div className="flex flex-col items-center justify-center gap-8">
                    <span className="text-white px-2 text-center text-2xl font-semibold tracking-widest">
                      You have won
                    </span>
                    <span className="text-white px-2 text-center text-xl font-semibold tracking-widest">
                      {spinWheelResult?.sc} SC
                    </span>
                    <span className="text-white px-2 text-center text-2xl font-semibold tracking-widest">
                      Spin again in 24 hours
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default SpinWheel;
