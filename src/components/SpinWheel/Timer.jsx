function Timer({ hours, minutes, seconds }) {
  return (
    <div className="w-full p-4">
      <div className="flex flex-col items-center justify-center gap-8">
        <span className="text-white px-2 text-center text-2xl font-semibold tracking-widest">
          You have already claimed your Spin wheel Prize
        </span>
        <span className="text-white px-2 text-center text-xl font-semibold tracking-widest">
          Spin again in
        </span>
        <div className="grid w-full grid-cols-3 gap-4">
          <div className="relative flex grow flex-col gap-5">
            <div className="flex aspect-square h-full w-full items-center justify-between rounded-lg bg-[#343650]">
              <div className="relative !-left-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
              <span className="text-3xl font-semibold text-[#a5b4fc] sm:text-6xl lg:text-7xl">
                {hours}
              </span>
              <div className="relative -right-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
            </div>
            <span className="text-center text-xs font-medium text-[#8486A9] sm:text-2xl">
              {hours === 1 ? 'Hour' : 'Hours'}
            </span>
          </div>
          <div className="relative flex grow flex-col gap-5">
            <div className="flex aspect-square h-full w-full items-center justify-between rounded-lg bg-[#343650]">
              <div className="relative !-left-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
              <span className="text-3xl font-semibold text-[#a5b4fc] sm:text-6xl lg:text-7xl">
                {minutes}
              </span>
              <div className="relative -right-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
            </div>
            <span className="text-center text-xs capitalize text-[#8486A9] sm:text-2xl">
              {minutes === 1 ? 'Minute' : 'Minutes'}
            </span>
          </div>
          <div className="relative flex grow flex-col gap-5">
            <div className="flex aspect-square h-full w-full items-center justify-between rounded-lg bg-[#343650]">
              <div className="relative !-left-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
              <span className="text-3xl font-semibold text-[#a5b4fc] sm:text-6xl lg:text-7xl">
                {seconds}
              </span>
              <div className="relative -right-[6px] h-2.5 w-2.5 rounded-full bg-[#191A24] sm:h-3 sm:w-3" />
            </div>
            <span className="text-center text-xs capitalize text-[#8486A9] sm:text-2xl">
              {seconds === 1 ? 'Second' : 'Seconds'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
export default Timer;
