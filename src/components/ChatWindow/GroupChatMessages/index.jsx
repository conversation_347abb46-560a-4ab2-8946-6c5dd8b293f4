/* eslint-disable no-nested-ternary */

'use client';

import CallIcon from '@/assets/icons/CallIcon';
import CloseIcon from '@/assets/icons/CloseIcon';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import ShowGroupModal from '@/components/Models/ShowGroupModal';
import useGroupCall, { rtc } from '@/hooks/useGroupCall';
import { useGetPublicGroupDetails } from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useCallStore from '@/store/useCallStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { formatDateTime, getAccessToken, isImageUrl } from '@/utils/helper';
import { ChevronLeft, Mic, MicOff } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import IconButton from '../../Common/Button/IconButton';
function GroupChatConversion(props) {
  const { isBackNav } = props;
  const {
    isGroupChatOpen,
    setIsGroupChatOpen,
    groupId,
    groupChat,
    isGroupPageOpen,
    groupPageChat,
    setActiveGroupPageTab,
    setGroupId,
    setGroupName,
  } = useGroupChatStore((state) => state);
  const { isCallActive: isPrivateCallActive } = usePrivateChatStore();
  const { openModal, clearModals } = useModalStore((state) => state);

  const openProfileDetails = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
  };

  const handleOpenModal = (e, id, group) => {
    setActiveGroupPageTab('about');
    e.stopPropagation();
    openProfileDetails(id, group?.groupName);
    openModal(<ShowGroupModal groupId={id} group={group} />);
  };

  // below for private
  const { privateChat } = props;

  const { userDetails, isAuthenticated } = useAuthStore((state) => state);
  const { setVoiceCall, voiceCall } = useVoiceCallStore((state) => state);
  const chatContainerRef = useRef(null);
  const {
    initiateCall,
    handleDeclineCall,
    isCallActive,
    isMuted,
    setIsMuted,
    callInitialized,toggleMicrophone
  } = useGroupCall();

  const { toggleMuted, setToggleMuted } = useCallStore();

  // const { data: groupDetails } = useGetGroupDetails({
  //   params: { groupId },
  //   enabled: !!isAuthenticated,
  // });

  const { data: groupDetails } = useGetPublicGroupDetails({
    params: { groupId },
    enabled: !!isAuthenticated,
  });

  const accessToken = getAccessToken();
  // const [isCallActive, setIsCallActive] = useState(false);
  // const [isMuted, setIsMuted] = useState(false);
  const tokenGenRetryLimit = 3; // Set retry limit

  const [retryCount, setRetryCount] = useState(0); // Counter for retry attempts
  const retryCounterRef = useRef(0);

  // const toggleMicrophone = async () => {
  //   setIsMuted(!isMuted);
  //   setToggleMuted(!isMuted);
  //   if (rtc.localAudioTrack) {
  //     if (isMuted) {
  //       await rtc.localAudioTrack.setEnabled(true);
  //     } else {
  //       await rtc.localAudioTrack.setEnabled(false);
  //     }
  //   }
  // };

  const [channelName, setChannelName] = useState();
  useEffect(() => {
    setChannelName(uuidv4());
  }, []);

  const closePrivateChatModal = () => {
    setIsGroupChatOpen(false);
  };

  useEffect(() => {
    if (!rtc.client) return;

    rtc.client.on('user-published', async (user, mediaType) => {
      await rtc.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc.remoteAudioTrack.play();
      }
    });

    rtc.client.on('user-unpublished', (user, mediaType) => {
      if (mediaType === 'audio') {
        if (rtc.remoteAudioTrack) {
          rtc.remoteAudioTrack.stop();
          rtc.remoteAudioTrack = null;
        }
      }
    });

    return () => {
      rtc.client.removeAllListeners();
      setIsMuted(false);
    };
  }, []);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [privateChat, groupChat, groupPageChat]);

  return isGroupChatOpen || isGroupPageOpen ? (
    <div className="group-chat flex h-full w-full flex-col">
      <div className="flex items-center justify-between gap-2 bg-oxfordBlue-1000 px-4 py-2 shadow-chat-header">
        <div className="flex w-full items-center justify-between">
          {!isBackNav && (
            <ChevronLeft
              className="cursor-pointer text-steelTeal-1000 transition-all duration-300 hover:text-white-1000"
              onClick={closePrivateChatModal}
            />
          )}
          <div className="flex items-center gap-4">
            <button
              type="button"
              className="relative h-10 w-10 shrink-0"
              onClick={(e) =>
                handleOpenModal(e, groupDetails?.group?.id, groupDetails?.group)
              }
            >
              <div className="relative h-10 w-10">
                <div className="pointer-events-none absolute inset-0 z-10 h-10 w-10 rounded-full"></div>
                <div className="h-10 w-10 overflow-hidden rounded-full">
                  <ChatAvatar
                    profileImage={groupDetails?.group?.profile}
                    firstName={groupDetails?.group?.groupName}
                    userName={groupDetails?.group?.username}
                    lastName=""
                    imageClassName="h-full w-full rounded-full object-cover"
                    imageWidth={40}
                    imageHeight={40}
                    avatarSize={40}
                  />
                </div>
              </div>
            </button>
            <p className="text-xl">{groupDetails?.group?.groupName}</p>
          </div>

          <div className="flex items-center gap-2">
            {groupDetails?.group?.members?.find(
              (d) => d.userId == userDetails?.userId,
            ) && callInitialized?.groupId === groupDetails?.group?.id ? (
              <IconButton
                disabled
                className="bg-steelTeal-1000/20 h-6 w-6 animate-pulse rounded-full"
              >
                <CallIcon className="fill-steelTeal-500 h-5 w-5 opacity-70" />
              </IconButton>
            ) : (
              !isPrivateCallActive && (
                <>
                  {!isCallActive && (
                    <IconButton
                      onClick={() =>
                        initiateCall({
                          groupId,
                          groupName: groupDetails?.group?.groupName,
                        })
                      }
                      className="h-6 w-6"
                    >
                      <CallIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                    </IconButton>
                  )}

                  {isCallActive &&
                    voiceCall?.channelName ===
                      groupDetails?.group?.groupName && (
                      <>
                        <IconButton
                          onClick={handleDeclineCall}
                          className="h-6 w-6"
                        >
                          <DisconnectIcon
                            className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                            fill="red"
                          />
                        </IconButton>

                        <IconButton
                          onClick={toggleMicrophone}
                          className="h-6 w-6"
                        >
                          {toggleMuted ? (
                            <MicOff className="h-5 w-5 text-red-500 transition-all duration-300 hover:text-red-600" />
                          ) : (
                            <Mic className="h-5 w-5 text-steelTeal-1000 transition-all duration-300 hover:text-white-1000" />
                          )}
                        </IconButton>
                      </>
                    )}
                </>
              )
            )}
            {!isGroupPageOpen && (
              <IconButton onClick={closePrivateChatModal} className="h-6 w-6">
                <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            )}
          </div>
        </div>
      </div>

      <div className="flex min-h-0 shrink grow basis-[0%] flex-col">
        <div
          ref={chatContainerRef}
          className="scrollbar-none page-chat flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden px-[0.625rem]"
        >
          <div className="flex flex-col gap-[0.625rem]">
            {groupPageChat?.map((chat) => {
              const received =
                Number(chat?.userId) !== Number(userDetails?.userId);
              return (
                <div
                  key={chat?.createdAt}
                  className={`flex ${received ? 'chat-received' : 'justify-end'} mr-4 gap-[0.625rem] py-[0.625rem] pl-[0.625rem]`}
                >
                  <div className="chat-box flex w-full flex-col gap-3">
                    <div className="gap- chat-box-inner flex w-full flex-col">
                      <h6
                        className={`flex ${received ? '' : 'justify-end'} gap-5 text-[0.813rem] font-normal leading-none text-steelTeal-1000`}
                      >
                        {received && (
                          <span className="inline-block truncate">
                            @{chat?.user?.username}
                          </span>
                        )}
                        <span className={`inline-block `}>
                          {formatDateTime(chat?.createdAt)}
                        </span>
                      </h6>
                      {chat?.message ? (
                        isImageUrl(chat?.message) ? (
                          <Image
                            src={chat?.message}
                            width={10000}
                            height={10000}
                            className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                            alt="GIF"
                          />
                        ) : (
                          <p className="break-all rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                            {chat?.message}
                          </p>
                        )
                      ) : null}
                      {chat?.image && (
                        <Image
                          src={chat?.image}
                          width={10000}
                          height={10000}
                          className="h-auto w-full max-w-full"
                          alt="Chat Image"
                        />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            {isGroupPageOpen &&
              groupChat.length === 0 &&
              groupChat?.map((chat) => {
                const received =
                  Number(chat?.userId) !== Number(userDetails?.userId);
                return (
                  <div
                    key={chat?.createdAt}
                    className={`flex ${received ? '' : 'justify-end'} mr-4 gap-[0.625rem] py-[0.625rem] pl-[0.625rem]`}
                  >
                    <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
                      <div className="flex w-full flex-col gap-1">
                        <h6
                          className={`flex ${received ? '' : 'justify-end'} gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000`}
                        >
                          {received && (
                            <span className="inline-block truncate">
                              @{chat?.user?.username}
                            </span>
                          )}
                          <span className={`inline-block `}>
                            {formatDateTime(chat?.createdAt)}
                          </span>
                        </h6>
                        {chat?.message ? (
                          isImageUrl(chat?.message) ? (
                            <Image
                              src={chat?.message}
                              width={10000}
                              height={10000}
                              className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                              alt="GIF"
                            />
                          ) : (
                            <p className="break-all rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                              {chat?.message}
                            </p>
                          )
                        ) : null}
                        {chat?.image && (
                          <Image
                            src={chat?.image}
                            width={10000}
                            height={10000}
                            className="h-auto w-full max-w-full"
                            alt="Chat Image"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  ) : (
    <p>null</p>
  );
}
export default GroupChatConversion;
