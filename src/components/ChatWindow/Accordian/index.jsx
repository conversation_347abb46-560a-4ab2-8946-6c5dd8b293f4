import { useState } from "react";

 function CustomAccordion() {
  const [openIndex, setOpenIndex] = useState(0); // Default open first accordion

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index); // Toggle accordion
  };

  const accordionData = [
    { title: "Accordion 1", content: "This is the content of Accordion 1." },
    { title: "Accordion 2", content: "This is the content of Accordion 2." },
    { title: "Accordion 3", content: "This is the content of Accordion 3." },
  ];

  return (
    <div className="max-w-xl mx-auto mt-10">
      {accordionData.map((item, index) => (
        <div key={index} className="mb-4 border border-gray-300 rounded-lg">
          {/* Accordion Header */}
          <button
            onClick={() => toggleAccordion(index)}
            className="w-full text-left px-4 py-2 bg-gray-100 hover:bg-gray-200 focus:outline-none"
          >
            <h2 className="font-semibold text-lg">{item.title}</h2>
          </button>

          {/* Accordion Content */}
          <div
            className={`overflow-hidden transition-[max-height] duration-300 ${
              openIndex === index ? "max-h-screen" : "max-h-0"
            }`}
          >
            <div className="px-4 py-2 bg-white">
              <p>{item.content}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default CustomAccordion;