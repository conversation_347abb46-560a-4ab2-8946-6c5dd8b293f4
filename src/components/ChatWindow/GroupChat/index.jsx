'use client';

import Image from 'next/image';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import MessageIcon from '@/assets/icons/MessageIcon';
import AddGroupIcon from '@/assets/icons/AddGroupIcon';
import useModalStore from '@/store/useModalStore';
import CreateGroupModal from '@/components/Models/CreateGroupModal';
import {
  useAcceptDeclineGroupJonRequest,
  useGetGroupListQuery,
  useGetGroupRequestListQuery,
  useGetPublicGroupListQuery,
  useJoinGroup,
} from '@/reactQuery/chatWindowQuery';
import ShowGroupModal from '@/components/Models/ShowGroupModal';
import IconButton from '@/components/Common/Button/IconButton';
import useGroupChatStore from '@/store/useGroupChatStore';
import useAuthStore from '@/store/useAuthStore';
import { useQueryClient } from '@tanstack/react-query';
import GroupImg1 from '../../../assets/images/demo-image/user-profile.jpg';
import ArrowDownIcon from '@/assets/icons/ArrowDownIcon';
import CheckCircleIcon from '@/assets/icons/Check-Circle';
import CrossCircleIcon from '@/assets/icons/Cross-Circle';
import GroupToggleSwitch from '@/components/Common/ToggleSwitch/GroupToggleSwitch';
import GroupsListing from './GroupsListing';
import toast from 'react-hot-toast';

const images = [GroupImg1, GroupImg1, GroupImg1, GroupImg1];

const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

function GroupChat() {
  const { openModal } = useModalStore((state) => state);
  const [openIndex, setOpenIndex] = useState(1);
  const [searchInput, setSearchInput] = useState('');
  // const [toggleGroup, setToggleGroup] = useState('MY_GROUPS');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const queryClient = useQueryClient();
  const observerRef = useRef(null);
  const groupObserverRef = useRef(null);
  const groupsContainerRef = useRef(null);
  const { isAuthenticated } = useAuthStore((state) => state);
  const {
    setIsGroupChatOpen,
    setGroupId,
    setGroupChat,
    setGroupName,
    setActiveGroupPageTab,
    toggleGroupTab:toggleGroup,setToggleGroupTab:setToggleGroup
  } = useGroupChatStore((state) => state);
  // Create a stable debounced search function
  const debouncedSearchHandler = useMemo(
    () => debounce((value) => setDebouncedSearch(value), 1000),
    [],
  );

  const toggleAccordion = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  useEffect(() => {
    setGroupChat([]);
  }, []);

  // Handle search input changes
  const handleSearchChange = (e) => {
    const { value } = e.target;
    setSearchInput(value);
    debouncedSearchHandler(value);
  };

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, status, refetch:refetchMyGroup } =
    useGetGroupListQuery({
      search: debouncedSearch,
      enabled: !!isAuthenticated,
    });
  const {
    data: dataGroupInvitation,
    fetchNextPage: fetchNextPageGroupInvitation,
    hasNextPage: hasNextPageGroupInvitation,
    isFetchingNextPage: isFetchingNextPageGroupInvitation,
    status: statusGroupInvitation,
  } = useGetGroupRequestListQuery({
    search: debouncedSearch,
    enabled: !!isAuthenticated,
    invitationType: 'invite',
  });
  const {
    data: publicGroups,
    fetchNextPage: fetchNextPagePublicGroups,
    hasNextPage: hasNextPagePublicGroups,
    isFetchingNextPage: isFetchingNextPagePublicGroups,
    status: statusPublicGroups,
  } = useGetPublicGroupListQuery({
    search: debouncedSearch,
    enabled: !!isAuthenticated,
    groupType: 'public',
  });

  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
      refetchMyGroup()
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });

  const responseRequest = useAcceptDeclineGroupJonRequest({
    onSuccess: async (response, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_REQUEST_LIST_QUERY'],
      });

      toast.success(
        variables?.requestStatus == 'ACCEPTED'
          ? 'Request accepted successfully!'
          : 'Request declined successfully',
      );
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });


  const lastGroupElementRef = useCallback(
    (node) => {
      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (
            entries[0].isIntersecting &&
            hasNextPagePublicGroups &&
            !isFetchingNextPagePublicGroups
          ) {
            fetchNextPagePublicGroups();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) observerRef.current.observe(node);
    },
    [
      hasNextPagePublicGroups,
      isFetchingNextPagePublicGroups,
      fetchNextPagePublicGroups,
    ],
  );
  const lastGroupElementGroupRef = useCallback(
    (node) => {
      if (groupObserverRef.current) groupObserverRef.current.disconnect();

      groupObserverRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) groupObserverRef.current.observe(node);
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage],
  );

  //   const lastGroupElementRef = useCallback(
  //   (node) => {
  //     if (observerRef.current) observerRef.current.disconnect();

  //     observerRef.current = new IntersectionObserver(
  //       (entries) => {
  //         if (toggleGroup == 'MY_GROUPS') {
  //           if (
  //             entries[0].isIntersecting &&
  //             hasNextPage &&
  //             !isFetchingNextPage
  //           ) {
  //             fetchNextPage();
  //           }
  //         } else {
  //           if (
  //             entries[0].isIntersecting &&
  //             hasNextPagePublicGroups &&
  //             !isFetchingNextPagePublicGroups
  //           ) {
  //             fetchNextPagePublicGroups();
  //           }
  //         }
  //       },
  //       { rootMargin: '200px' },
  //     );

  //     if (node) observerRef.current.observe(node);
  //   },
  //   [
  //     hasNextPagePublicGroups,
  //     isFetchingNextPagePublicGroups,
  //     fetchNextPagePublicGroups,
  //     fetchNextPage,
  //     hasNextPage,
  //     isFetchingNextPage,
  //   ],
  // );

  const openCreateModal = () => {
    openModal(<CreateGroupModal />);
  };

  // if (status === 'pending') {
  //   return (
  //     <div className="flex h-full items-center justify-center">
  //       <div className="text-white">Loading...</div>
  //     </div>
  //   );
  // }

  if (status === 'error') {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-red-500">Error loading groups</div>
      </div>
    );
  }
  const openProfileDetails = (groupId, groupName) => {
    setGroupId(groupId);
    setGroupName(groupName);
  };

  const handleOpenModal = (e, id, group) => {
    setActiveGroupPageTab('about');
    e.stopPropagation();
    openProfileDetails(id, group?.groupName);
    openModal(<ShowGroupModal groupId={id} group={group} />);
  };

  const openChat = (groupId, groupName) => {
    queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
  };

  return (
    <div className="px-2.5 pb-2 pt-3">
      <div className="flex items-center justify-between gap-2">
        <div className="relative w-full max-w-[200px]">
          <input
            className="h-9 w-full resize-none rounded-3xl bg-maastrichtBlue-1000 px-4 pr-8 py-2 placeholder:text-steelTeal-1000"
            placeholder="Search group"
            value={searchInput}
            onChange={handleSearchChange}
          />
          {searchInput && (
            <span
              className="absolute right-2 top-1/2 -translate-y-1/2 transform cursor-pointer text-gray-500 hover:text-gray-700"
              onClick={() => {
                setSearchInput('');
                setDebouncedSearch('');
              }}
              style={{ fontSize: '16px' }}
            >
              &#x2715;
            </span>
          )}
        </div>
        <button
          type="button"
          className="text-white flex h-9 w-auto items-center justify-center gap-1 rounded-3xl bg-primary-1000 px-2 text-[10px] font-bold capitalize leading-none"
          onClick={openCreateModal}
        >
          <AddGroupIcon className="h-5 shrink-0" />
          <span className="whitespace-nowrap text-[13px]">create group</span>
        </button>
      </div>

      <GroupToggleSwitch
        toggleGroup={toggleGroup}
        setToggleGroup={setToggleGroup}
      />

      <div>
        <button
          onClick={() => toggleAccordion(0)}
          className={`flex w-full items-center justify-between border-b border-solid px-5 py-2.5 focus:outline-none ${
            openIndex === 0 ? 'border-borderColor-100' : 'border-gray-700'
          }`}
        >
          <div className="flex flex-row gap-2">
            <div className="text-white font-medium">
              Invitations ({dataGroupInvitation?.groups?.length ?? 0})
            </div>
          </div>
          <ArrowDownIcon
            className={`transition-all duration-300 ease-in-out ${
              openIndex === 0 ? 'rotate-180' : ''
            }`}
          />
        </button>

        <div
          className={`overflow-hidden transition-[max-height] duration-300 ${
            openIndex === 0 ? 'max-h-screen' : 'max-h-0'
          }`}
        >
          <div className="py-1">
            <section className="overflow-y-auto pt-1">
              <div className="flex flex-col gap-3">
                {dataGroupInvitation?.groups?.length === 0 ? (
                  <div className="text-white py-3 text-center text-sm">
                    No invitations found
                  </div>
                ) : (
                  dataGroupInvitation?.groups.map((group, index) => (
                    <div
                      key={group.id}
                      ref={
                        index === dataGroupInvitation?.groups.length - 1
                          ? lastGroupElementRef
                          : null
                      }
                      className="group-card flex items-center justify-between gap-4 rounded-lg border-l-4 border-yellow-500 bg-maastrichtBlue-1000 px-4 py-3 transition hover:shadow-md"
                    >
                      <div className="relative shrink-0">
                        <div className="bg-white h-12 w-12 overflow-hidden rounded-full">
                          <Image
                            src={
                              group?.UserChatGroup?.profile ||
                              '/default-avatar.png'
                            }
                            alt={`${group?.groupName} avatar`}
                            height={100}
                            width={100}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      </div>

                      <div className="flex-1">
                        <span className="text-white break-all text-sm font-semibold">
                          {group?.UserChatGroup?.groupName}
                        </span>
                        <p
                          className="max-h-12 max-w-[120px] overflow-hidden break-words text-xs text-gray-400"
                          title={group?.UserChatGroup?.description}
                        >
                          {group?.UserChatGroup?.description}
                        </p>
                      </div>

                      <div
                        className="flex items-center "
                        disabled={responseRequest?.isPending}
                      >
                        <IconButton
                          // onClick={() => handleJoinRequest(group.id, "accept")}
                          className="h-7 w-7 min-w-7 rounded-full transition hover:bg-green-800/20"
                          aria-label="Accept Invitation"
                          title="Accept"
                          onClick={() => {
                            responseRequest.mutate({
                              groupId: group?.groupId,
                              requestId: group?.id,
                              requestStatus: 'ACCEPTED',
                            });
                          }}
                        >
                          <CheckCircleIcon className="fill-green-900" />
                        </IconButton>
                        <IconButton
                          // onClick={() => handleJoinRequest(group.id, "decline")}
                          className="h-7 w-7 min-w-7 rounded-full transition hover:bg-red-800/20"
                          aria-label="Decline Invitation"
                          title="Decline"
                          onClick={() => {
                            responseRequest.mutate({
                              groupId: group?.groupId,
                              requestId: group?.id,
                              requestStatus: 'DECLINE',
                            });
                          }}
                        >
                          <CrossCircleIcon className="fill-red-500" />
                        </IconButton>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </section>
          </div>
        </div>
      </div>

      {toggleGroup == 'MY_GROUPS' ? (
        <GroupsListing
          name={'My Groups'}
          toggleAccordion={toggleAccordion}
          data={data}
          openChat={openChat}
          handleOpenModal={handleOpenModal}
          openIndex={openIndex}
          ref={lastGroupElementGroupRef}
          toggleGroup={toggleGroup}
          showLoading={isFetchingNextPage}
          searchLoading={status=="pending"}
        />
      ) : (
        <GroupsListing
          name="Public Groups"
          toggleAccordion={toggleAccordion}
          data={publicGroups}
          openChat={openChat}
          handleOpenModal={handleOpenModal}
          openIndex={openIndex}
          ref={lastGroupElementRef}
          toggleGroup={toggleGroup}
          joinGroup={joinGroup}
          showLoading={isFetchingNextPagePublicGroups}
          searchLoading={status=="pending"}
        />
      )}
    </div>
  );
}

export default GroupChat;
