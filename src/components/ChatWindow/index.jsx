'use client';

import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { Grid } from '@giphy/react-components';
import {
  CircleArrowDown,
  MessageCircleMore,
  SendHorizontal,
} from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

// import NotificationIcon from '@/assets/icons/Notification';
import ArrowRightIcon from '@/assets/icons/Arrow-Right';
import ChatIcon from '@/assets/icons/Chat';
import CloseIcon from '@/assets/icons/CloseIcon';
import EmojiIcon from '@/assets/icons/Emoji';
import FriendGoldenIcon from '@/assets/icons/FriendGoldenIcon';
import FriendsChatIcon from '@/assets/icons/FriendsChatIcon';
import GIFIcon from '@/assets/icons/GIF';
import GroupChatIcon from '@/assets/icons/GroupChatIcon';
import PublicChatIcon from '@/assets/icons/PublicChatIcon';
import SettingIcon from '@/assets/icons/Setting';
import coinAc from '@/assets/images/stock-images/coin-ac.png';
import { useGrabRainDrop } from '@/hooks/useGrabRainDrop';
import {
  useGetFriendsRequestListQuery,
  useGetGroupDetailQuery,
  useGetGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useGeneralStore from '@/store/useGeneralStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { formatDateTime, isImageUrl } from '@/utils/helper';
import { usePathname, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import coinsGift from '../../assets/images/coins-gift.png';
import countryFlag from '../../assets/images/stock-images/flag-uk.png';
import useChatWindow from '../../hooks/useChatWindow';
import Auth from '../Auth';
import IconButton from '../Common/Button/IconButton';
import MainLoader from '../Common/Loader/MainLoader';
import ReactTooltip from '../Common/ReactTooltip';
import NotificationPopup from '../NotificationPopup';
import SearchUsers from '../SearchUsers';
import RainCompleteModal from '../Store/Rain/RainCompleteModal';
import UserInfo from '../UserInfoModal';
import ChatAvatar from './ChatAvatar';
import Friends from './Friends';
import GroupChat from './GroupChat';
import GroupChatConversion from './GroupChatMessages';
import PrivateChat from './PrivateChat';
import RecentChat from './RecentChat';

export default function ChatWindow({ mobile }) {
  const router = useRouter();
  const keyboardVisible = useRef(false); // Track keyboard visibility
  const gifPickerRef = useRef();
  const emojiPickerRef = useRef();
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const {
    message,
    setMessage,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    showGifPicker,
    section,
    setSection,
    showSuggestions,
    selectedSuggestion,
    publicChats,
    isPublicChatsLoading,
    chatContainerRef,
    groupChatContainerRef,
    inputRef,
    suggestionsRef,
    tagSuggestion,
    handleKeyDown,
    selectSuggestion,
    handleSendMessage,
    privateChat,
    recipientUser,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    isPrivateChatOpen,
    livePlayersCount,
    newMessagesCount,
    newGroupMessagesCount,
    scrollToBottom,
    handleGifSelect,
    searchQuery,
    setSearchQuery,
    fetchGifs,
    setGrabbedChat,
    updateGrabbedRainChat,
    error,
  } = useChatWindow();
  const pathname = usePathname();
  const { userDetails, isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { setIsPrivateChatOpen } = usePrivateChatStore((state) => state);

  const { isGroupChatOpen, setIsGroupChatOpen, groupName } = useGroupChatStore(
    (state) => state,
  );

  useEffect(() => {
    if (gifMessage && inputRef.current) {
      inputRef.current.focus();
    }
  }, [gifMessage]);

  const { data: groupListDdata } = useGetGroupListQuery({
    search: '',
    enabled: !!isAuthenticated,
  });

  const { data: currentGroupData } = useGetGroupDetailQuery({
    groupName: groupName,
    enabled: !!groupName && section === 'GroupChat',
  });
  const messageRef = useRef(null);
  const { openChat, setOpenChat } = useGeneralStore((state) => state);
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  // useNotice();
  const [privateChatUserDetails, setPrivateChatUserDetails] = useState(null);
  const { openModal } = useModalStore((state) => state);
  const { grabRainDrop } = useGrabRainDrop();
  const { data: friendsRequestList } = useGetFriendsRequestListQuery({
    enabled: !!isAuthenticated,
  });

  const bottomRef = useRef(null);

  const handleOpenUserInfoModal = (userId) => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    openUserInfoModal(userId);
    openModal(
      <UserInfo setPrivateChatUserDetails={setPrivateChatUserDetails} />,
    );
  };

  const handleGrab = (chat) => {

    if (
      (chat?.rainDrop?.playerType == 'vip only' ||
        chat?.rainDrop?.playerType === 'vip') &&
      !userDetails?.vipTierDetail?.vipUser
    ) {
      toast.error('Only for VIP users');
      return;
    }
    grabRainDrop({
      userId: userDetails?.userId,
      rainDrop: {
        rainId: chat?.rainDrop?.rainId,
        amountType: chat?.rainDrop?.amountType,
        amount: chat?.rainDrop?.amount,
        playerNo: chat?.rainDrop?.playerNo,
        playerType: chat?.rainDrop?.playerType,
      },
    });

    // Update the chat to show grabbed status
    updateGrabbedRainChat(chat, userDetails?.userId);
    setGrabbedChat({ userId: userDetails?.userId, chat });
  };

  const handleSetting = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }

    router.push('/user/prefrences');
  };
  const handleUserClick = (user) => {
    // Perform additional actions with the clicked user
  };

  useEffect(() => {
    bottomRef?.current?.scrollIntoView({ behavior: 'auto' });
  }, [pathname]);

  useEffect(() => {
    const handleResize = () => {
      if (window.visualViewport) {
        const isKeyboardVisible =
          window.visualViewport.height < window.innerHeight;
        keyboardVisible.current = isKeyboardVisible; // Track keyboard visibility
        // Keep chat open if keyboard is visible and input is focused
        if (openChat) {
          if (isKeyboardOpen) {
            setOpenChat(true);
          }
          if (isKeyboardVisible && isKeyboardOpen) {
            setOpenChat(true);
          } else {
            // Explicitly remove focus fisKeyboardVisiblerom the input field
            if (messageRef.current) {
              messageRef.current.blur();
            }
          }
        }
      }
    };

    // Listen for resize events to detect keyboard visibility changes
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [messageRef, isKeyboardOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        gifPickerRef.current &&
        !gifPickerRef.current.contains(event.target) &&
        showGifPicker
      ) {
        handleGifPickerToggle();
      }
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target) &&
        showEmojiPicker
      ) {
        handleEmojiPickerToggle();
      }
    };

    if (showGifPicker || showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showGifPicker, showEmojiPicker]);

  return (
    <>
      <button
        type="button"
        className={`
        fixed bottom-16
        hidden lg:flex  
        ${openChat ? 'right-[20.5rem] top-[1rem]' : 'right-2 top-[1rem]'}
        z-[50] 
        h-8 w-8 min-w-8
        items-center justify-center
        rounded-l
        ${!openChat ? 'border border-primary-1000' : ''}
        bg-oxfordBlue-1000
        transition-all duration-300
        hover:bg-primary-1000
        active:scale-90
      `}
        onClick={() => {
          setOpenChat(!openChat);
        }}
      >
        {openChat ? (
          <ArrowRightIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        ) : (
          <ChatIcon className="h-[1rem] w-[1rem] fill-white-1000 transition-all duration-300" />
        )}
      </button>

      <section
        className={`
        customBlurColor
        fixed top-0 z-[41] transition-all duration-300 ease-in-out
        ${isAuthenticated ? 'h-[calc(100dvh_-_60px)]' : ' h-[100dvh] pb-[20px] lg:pb-0'}
 
        w-[100%] bg-cetaceanBlue-1000
        lg:h-dvh lg:w-[20.5rem]
        ${mobile ? 'right-0' : openChat ? 'right-0' : 'right-[-100%]'}
      `}
      >
        {isPublicChatsLoading ? (
          <div className="flex h-full w-full items-center justify-center bg-cetaceanBlue-1000">
            <MainLoader className="w-32" />
          </div>
        ) : (
          <div className="mt-[44px] flex h-full w-full flex-col bg-cetaceanBlue-1000 lg:mt-auto">
            <div className="hidden h-[3.75rem] items-center justify-between gap-2 bg-cetaceanBlue-1000 px-2.5 py-5 shadow-chat-header lg:flex">
              <button
                type="button"
                className="font-nunito flex cursor-default items-center justify-center gap-2 rounded-md border-2 border-solid border-transparent bg-transparent px-2.5 py-1.5 text-[0.813rem] text-white-1000"
              >
                <span className="d-block h-5 w-5">
                  <Image
                    src={countryFlag}
                    width={10000}
                    height={10000}
                    className="w-[1.875rem] max-w-full"
                    alt="Coin"
                  />
                </span>
                <span>EN</span>
              </button>
              <div className="flex justify-end gap-2">
                <div className="flex gap-4">
                  <NotificationPopup />

                  <ReactTooltip message="Friends" id="friends-tooltip" />
                  <div
                    className="relative"
                    onClick={() => {
                      if (!isAuthenticated) {
                        localStorage.setItem('activeTab', 0);
                        setSelectedTab(0);
                        openModal(<Auth />);
                        return;
                      }
                      if (section === 'Friends') setSection('PublicChat');
                      else setSection('Friends');
                    }}
                  >
                    <IconButton id="friends-tooltip">
                      <FriendGoldenIcon
                        className={`${
                          section === 'Friends'
                            ? 'fill-white-1000'
                            : 'fill-steelTeal-1000'
                        } transition-all duration-300 group-hover:fill-white-1000`}
                      />
                    </IconButton>
                    {friendsRequestList?.count > 0 && (
                      <span className="text-white absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-[10px] font-bold">
                        {friendsRequestList?.count > 9
                          ? '9+'
                          : friendsRequestList?.count}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {section !== 'Friends' && (
              <SearchUsers
                onUserClick={handleUserClick}
                setPrivateChatUserDetails={setPrivateChatUserDetails}
              />
            )}
            <div className="mt-[1rem] flex items-center justify-between bg-cetaceanBlue-1000 shadow-chat-header lg:mt-[0rem]">
              <button
                type="button"
                className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'PrivateChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
                onClick={() => {
                  if (!isAuthenticated) {
                    localStorage.setItem('activeTab', 0);
                    setSelectedTab(0);
                    openModal(<Auth />);
                    return;
                  }
                  setSection('PrivateChat');
                  setIsPrivateChatOpen(false);
                  setIsGroupChatOpen(false);
                }}
              >
                {/* <IconButton> */}
                <FriendsChatIcon
                  className={`${section === 'PrivateChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
                />
                {/* </IconButton> */}
                <span>Friends</span>
              </button>
              <button
                type="button"
                className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'GroupChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
                onClick={() => {
                  if (!isAuthenticated) {
                    localStorage.setItem('activeTab', 0);
                    setSelectedTab(0);
                    openModal(<Auth />);
                    return;
                  }
                  setSection('GroupChat');
                  setIsPrivateChatOpen(false);
                  setIsGroupChatOpen(false);
                }}
              >
                {/* <IconButton> */}
                <GroupChatIcon
                  className={`${section === 'GroupChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
                />
                {/* </IconButton> */}
                <span>Groups</span>

                {isAuthenticated && newGroupMessagesCount > 0 && (
                  <div className="h-2 w-2 rounded-full bg-blue-1000" />
                )}
              </button>
              <button
                type="button"
                className={`font-nunito flex w-full items-center justify-center gap-1 border-2 border-solid border-transparent ${section === 'PublicChat' ? 'border-b-red-1000 bg-transparent' : 'bg-maastrichtBlue-1000'} h-10 px-2.5 py-2.5 text-[0.813rem] text-white-1000`}
                onClick={() => {
                  setSection('PublicChat');
                  setIsPrivateChatOpen(false);
                  setIsGroupChatOpen(false);
                }}
              >
                {/* <IconButton> */}
                <PublicChatIcon
                  className={`${section === 'PublicChat' ? 'fill-red-1000' : 'fill-steelTeal-1000 group-hover:fill-white-1000'} transition-all duration-300`}
                />
                {/* </IconButton> */}
                <span>Public</span>
                {/* <span className={`w-6 h-6 rounded-full text-xs text-white flex items-center justify-center ${section === 'PublicChat' ? 'bg-maastrichtBlue-1000' : 'bg-black-1000'}`}>2</span> */}
                {newMessagesCount > 0 && (
                  <div className="h-2 w-2 rounded-full bg-green-1000" />
                )}
              </button>
            </div>
            <div className="relative flex min-h-0 shrink grow basis-[0%] flex-col">
              {section === 'PublicChat' && (
                <div
                  ref={chatContainerRef}
                  className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden  sm:pb-0"
                >
                  <div className="flex flex-col gap-[0.625rem] px-[0.625rem]">
                    {publicChats?.map((chat,index) => {
                      return (
                        <div
                          key={index}
                          className="flex gap-[0.625rem] py-[0.625rem] pl-[0.625rem]"
                        >
                          <div
                            className="relative h-[45px] w-[45px] cursor-pointer rounded-full"
                            onClick={() =>
                              handleOpenUserInfoModal(chat?.userId)
                            }
                          >
                            <ChatAvatar
                              key={chat.id}
                              profileImage={chat.user?.profileImage}
                              firstName={chat.user?.firstName}
                              lastName={chat.user?.lastName}
                              userName={chat.user?.username}
                              imageClassName="h-full w-full max-w-full rounded-full object-cover object-center"
                              imageWidth={45}
                              imageHeight={45}
                              avatarSize={45}
                            />
                            <span className="absolute -bottom-1 -left-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-primary-1000 pt-1 text-center text-xs leading-none">
                              {chat?.user?.UserVipTiers
                                ? Number(
                                    chat?.user?.UserVipTiers[0]?.level || 0,
                                  )
                                : 'NA'}
                            </span>
                          </div>
                          <div className="flex w-[calc(100%_-_3.625rem)] flex-col gap-3">
                            <div className="flex w-full flex-col gap-1">
                              <h6 className="flex gap-[0.625rem] text-[0.813rem] font-normal leading-none text-steelTeal-1000">
                                <span
                                  onClick={() =>
                                    handleOpenUserInfoModal(chat?.userId)
                                  }
                                  className=" inline-block  cursor-pointer truncate"
                                >
                                  @{chat?.user?.username}
                                </span>
                                <span className="inline-block">
                                  {formatDateTime(chat?.createdAt)}
                                </span>
                              </h6>
                              {!chat?.rainDrop && !chat?.moreDetails ? (
                                isImageUrl(chat?.message) ? (
                                  <Image
                                    src={chat?.message}
                                    width={10000}
                                    height={10000}
                                    className="w-32 rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000"
                                    alt="GIF"
                                  />
                                ) : (
                                  <p className="break-all rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-3 py-2 text-sm font-normal text-white-1000">
                                  {chat?.message}
                                  </p>
                                )
                              ) : null}
                              {chat?.moreDetails && chat.message === 'tip' && (
                                <div className="relative rounded-lg bg-blue-500 p-2">
                                  <p className="mb-1 text-lg">
                                    <span
                                      className="cursor-pointer font-semibold tracking-wide text-teal-300"
                                      onClick={() =>
                                        handleOpenUserInfoModal(
                                          chat?.moreDetails?.receiverId,
                                        )
                                      }
                                    >
                                      @{chat?.moreDetails?.receiverName}
                                    </span>{' '}
                                    just recieved a TIP
                                  </p>
                                  <div className="rounded-lg bg-blue-600 p-1 shadow-lg">
                                    <p className="flex items-center gap-2 font-bold">
                                      <Image
                                        src={coinAc}
                                        width={10000}
                                        height={10000}
                                        className="w-5 max-w-full lg:w-5"
                                        alt="Coin"
                                      />{' '}
                                      <span className="mt-1 flex items-center">
                                        {chat?.moreDetails?.amount} AC
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              )}
                              {chat?.rainDrop && (
                                <div className="rounded-lg rounded-tl-none bg-maastrichtBlue-1000 px-2 py-2">
                                  <p className="my-1">
                                    {chat?.rainDrop?.message}
                                  </p>
                                  <div className="relative overflow-hidden rounded-lg">
                                    <Image
                                      src={coinsGift}
                                      width={10000}
                                      height={10000}
                                      className="h-auto w-full max-w-full"
                                      alt="Chat Image"
                                    />
                                    {(chat?.rainDrop?.playerType ===
                                      'vip only' ||
                                      chat?.rainDrop?.playerType === 'vip') && (
                                      <div className="absolute left-1 top-1 my-1 flex max-w-min items-center justify-center rounded-full bg-yellow-400 px-3 ">
                                        <span className="m-0 text-sm font-semibold text-black-1000 ">
                                          VIP
                                        </span>
                                      </div>
                                    )}
                                    <div className="absolute right-3 top-3">
                                      <p className="text-white text-lg font-bold">
                                        Coin Drops
                                      </p>

                                      {chat?.rainDrop?.status === 'complete' ? (
                                        <p
                                          onClick={() =>
                                            openModal(
                                              <RainCompleteModal
                                                rainDrop={chat?.rainDrop}
                                                chat={chat}
                                              />,
                                            )
                                          }
                                          className="text-white cursor-pointer rounded-lg bg-red-500 px-2 py-1 font-bold"
                                        >
                                          Completed
                                        </p>
                                      ) : chat?.rainDrop?.userId !=
                                          userDetails?.userId &&
                                        chat?.rainDrop?.grabbedStatus ? (
                                        <p className="text-white rounded-lg bg-green-500 px-2 py-1 font-bold">
                                          Grabbed
                                        </p>
                                      ) : chat?.rainDrop?.userId !=
                                        userDetails?.userId ? (
                                        <button
                                          onClick={() => handleGrab(chat)}
                                          className="text-white rounded-lg bg-red-500 px-2 py-1 font-bold"
                                        >
                                          Grab
                                        </button>
                                      ) : null}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div ref={bottomRef} />
                </div>
              )}

              {section === 'GroupChat' && (
                <div
                  ref={groupChatContainerRef}
                  className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden  sm:pb-0"
                >
                  {isGroupChatOpen ? <GroupChatConversion /> : <GroupChat />}
                </div>
              )}

              {section === 'Friends' && (
                <div className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden  sm:pb-0">
                  <Friends
                    setPrivateChatUserDetails={setPrivateChatUserDetails}
                  />
                </div>
              )}

              {section === 'PrivateChat' && (
                <div className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden  sm:pb-0">
                  {isPrivateChatOpen ? (
                    <PrivateChat
                      privateChat={privateChat}
                      recipientUser={recipientUser}
                      privateChatUserDetails={privateChatUserDetails}
                    />
                  ) : (
                    <RecentChat />
                  )}
                </div>
              )}

              {showEmojiPicker && (
                <div
                  ref={emojiPickerRef}
                  className="absolute bottom-28  right-0 xxs:right-auto"
                >
                  <Picker
                    autoFocus={false}
                    data={data}
                    onEmojiSelect={(emoji) => {
                      setShowEmojiPicker(false);
                      setMessage(message + emoji.native);
                      if (gifMessage) setGifMessage(null);
                    }}
                  />
                </div>
              )}

              {showGifPicker && (
                <div
                  className="absolute bottom-28 left-0 z-50 w-full"
                  ref={gifPickerRef}
                >
                  <div style={{ maxHeight: 300, overflowY: 'scroll' }}>
                    <Grid
                      key={searchQuery}
                      fetchGifs={fetchGifs}
                      width={300}
                      columns={2}
                      gutter={6}
                      onGifClick={handleGifSelect}
                    />
                  </div>
                </div>
              )}

              {(section === 'PublicChat' ||
                isPrivateChatOpen ||
                isGroupChatOpen) && (
                <div className="relative mb-[40px] bg-oxfordBlue-1000 px-[0.625rem] py-5 pb-0 shadow-[0px_-4px_10px_var(--richBlack-1000)] lg:mb-0 lg:pb-5">
                  {showSuggestions && (
                    <ul className="absolute bottom-28 mt-1 max-h-60 w-11/12 overflow-y-auto rounded-md bg-maastrichtBlue-1000 shadow-lg">
                      {section === 'PublicChat' &&
                        tagSuggestion?.map((suggestion, index) => (
                          <button
                            type="button"
                            key={suggestion?.username}
                            ref={(el) => (suggestionsRef.current[index] = el)} // Add ref to each suggestion
                            className={`block w-full cursor-pointer px-4 py-2 ${
                              index === selectedSuggestion
                                ? 'text-white bg-blue-500'
                                : ''
                            }`}
                            onClick={() => selectSuggestion(index)}
                          >
                            {suggestion?.username}
                          </button>
                        ))}
                    </ul>
                  )}
                  <div className="flex items-center justify-between rounded-[0.625rem]  border-2 border-cetaceanBlue-1000 bg-cetaceanBlue-1000 px-1 transition-all duration-100 hover:border-2 hover:border-primary-1000">
                    {showGifPicker ? (
                      <input
                        ref={inputRef}
                        className="hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                        placeholder="Search GIFs..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        style={{ opacity: gifMessage ? '0' : '1' }}
                      />
                    ) : gifMessage ? (
                      <div className="mb-2 flex  items-center">
                        <Image
                          src={gifMessage}
                          width={10000}
                          height={10000}
                          className="w-[5.875rem] max-w-full rounded-lg"
                          alt="Selected GIF"
                        />
                        <IconButton
                          onClick={() => setGifMessage(null)}
                          className="z-0 ml-2 h-6 w-6 min-w-6 cursor-pointer"
                        >
                          <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                        </IconButton>
                      </div>
                    ) : (
                      <div className="ml-1 w-full">
                        <input
                          ref={messageRef}
                          className={`hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 py-2 pr-[0.625rem] placeholder:text-steelTeal-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                          placeholder={
                            isAuthenticated
                              ? showEmojiPicker
                                ? 'Search Emojis'
                                : 'Type your message'
                              : 'Please Login to send a message'
                          }
                          value={message}
                          readOnly={!isAuthenticated}
                          onChange={handleInputChange}
                          onKeyDown={handleKeyDown}
                          style={{ opacity: gifMessage ? '0' : '1' }}
                          onFocus={(e) => {
                            setOpenChat(true);
                            setIsKeyboardOpen(true); // Keyboard likely opened (mobile)
                          }}
                        />
                      </div>
                    )}
                    {isAuthenticated && (
                      <SendHorizontal
                        onClick={handleSendMessage}
                        className="h-8 w-8 rounded-[0.313rem] bg-primary-1000 p-1"
                      />
                    )}
                  </div>
                  <p className="h-[24px] text-primary-1000">{error}</p>
                  <div className="flex items-center justify-between gap-4 ">
                    <div className="flex items-center gap-[0.313rem] text-[0.813rem] font-normal leading-none text-white-1000">
                      <span className="mb-1 inline-block h-[0.313rem] w-[0.313rem] rounded-full bg-green-1000" />
                      <span>
                        Online:
                        {isAuthenticated
                          ? section === 'GroupChat'
                            ? currentGroupData?.totalOnlineMembers
                            : livePlayersCount
                          : '-' || '-'}
                      </span>
                    </div>

                    <div className="flex items-center justify-end gap-[0.313rem]">
                      <button
                        onClick={handleSetting}
                        type="button"
                        disabled={!isAuthenticated}
                        className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                      >
                        <SettingIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                        Setting
                      </button>
                      <button
                        type="button"
                        onClick={handleGifPickerToggle}
                        disabled={!isAuthenticated}
                        className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000  ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                      >
                        <GIFIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                        GIF
                      </button>
                      <button
                        onClick={handleEmojiPickerToggle}
                        type="button"
                        disabled={!isAuthenticated}
                        className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                      >
                        <EmojiIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                        Emoji
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {newMessagesCount > 0 && section === 'PublicChat' && (
                <button
                  type="button"
                  onClick={() => scrollToBottom(true)}
                  className="absolute bottom-32 right-2 flex items-center gap-2 rounded-xl bg-tiber-1000 p-2"
                >
                  <MessageCircleMore className="text-white-1000 transition-all duration-300" />
                  New Messages
                  <CircleArrowDown className="text-white-1000 transition-all duration-300" />
                </button>
              )}
            </div>
          </div>
        )}
      </section>
    </>
  );
}
