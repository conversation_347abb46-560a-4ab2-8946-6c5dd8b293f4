import React, { useEffect, useRef, useState } from 'react';
import countryFlag from '../../../assets/images/stock-images/flag-uk.png';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import NotificationPopup from '@/components/NotificationPopup';
import ReactTooltip from '@/components/Common/ReactTooltip';
import IconButton from '@/components/Common/Button/IconButton';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useModalStore from '@/store/useModalStore';
import useChatWindow from '@/hooks/useChatWindow';
import { X } from 'lucide-react';
import useGeneralStore from '@/store/useGeneralStore';
import SearchIcon from '@/assets/icons/Search';
import FriendGoldenIcon from '@/assets/icons/FriendGoldenIcon';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import Auth from '@/components/Auth';
import useChatStore from '@/store/useChatStore';

const ChatHeader = ({ setShowChatHeader }) => {
  const {
    toggleSearchInput,
    showSearchInput,
    setShowSearchInput,
    setIsPrivateChatOpen,
  } = usePrivateChatStore((state) => state);
  // const [activeTab, setActiveTab] = useState('lobby');
  const { isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { openModal } = useModalStore((state) => state);
  const { section, setSection } = useChatWindow();
  const { setShowHomePopup, setOpenChat } = useGeneralStore((state) => state);
  const pathname = usePathname();
  const prevPath = useRef(pathname);
  const {
    setShowChat,
    showChat,
    chatHeaderActiveTab: activeTab,
    setChatHeaderActiveTab: setActiveTab,
  } = useChatStore();
  useEffect(() => {
    if (prevPath.current !== pathname) {
      setShowChatHeader(false);
      prevPath.current = pathname;
    }
  }, [pathname, setShowChatHeader]);

  return (
    <div className="flex w-full flex-wrap items-center justify-between gap-2 px-1 sm:px-4">
      <div className="flex flex-shrink-0 items-center gap-2">
        <button
          type="button"
          className="font-nunito flex items-center justify-center gap-2 rounded-md border-2 border-transparent bg-transparent py-1.5 text-[0.813rem] text-white-1000"
        >
          <span className="h-5 w-5">
            <Image
              src={countryFlag}
              width={30}
              height={30}
              className="w-[1.875rem] max-w-full"
              alt="Flag"
            />
          </span>
          <span>EN</span>
        </button>
      </div>

      <div className="flex min-w-0 flex-1 flex-wrap items-center justify-center gap-2">
        <div
          onClick={() => {
            // setOpenChat(true);
            setShowHomePopup(false);
            setShowChat(true);
            setActiveTab('chat');
            toggleSearchInput();
          }}
          className="relative flex cursor-pointer items-center gap-2 rounded-md bg-maastrichtBlue-1000 px-2 py-1 lg:hidden"
        >
          <SearchIcon
            className={`h-[1rem] w-[1rem] ${
              showSearchInput ? 'fill-primary-1000' : 'fill-white-1000'
            } transition-all duration-300`}
          />
        </div>

        <button
          onClick={() => {
            setActiveTab('lobby');
            // setOpenChat(false);
            setShowChat(false);
            setShowSearchInput(false);
          }}
          className={`gradient-border-before relative flex flex-shrink-0 items-center justify-center overflow-visible rounded-md px-2 py-1 text-sm font-medium transition-all sm:text-base ${
            activeTab === 'lobby'
              ? 'bg-primary-700 text-white-1000'
              : 'hover:bg-cetaceanBlue-800 text-white-750'
          }`}
        >
          Lobby
        </button>

        <button
          onClick={() => {
            setShowHomePopup(false);
            setActiveTab('chat');
            // setOpenChat(true);
            setShowChat(true);
            setSection('PublicChat');
            setShowSearchInput(false);
            setIsPrivateChatOpen(false);
          }}
          className={`gradient-border-before border-white-110 relative flex flex-shrink-0 items-center justify-center overflow-visible rounded-md px-2 py-1 text-sm font-medium transition-all sm:text-base ${
            activeTab === 'chat'
              ? 'bg-primary-700 text-white-1000'
              : 'hover:bg-cetaceanBlue-800 text-white-750'
          }`}
        >
          Chat
        </button>
      </div>

      <div className="flex flex-shrink-0 items-center gap-2">
        <NotificationPopup />
        <ReactTooltip message="Friends" id="friends-tooltip" />
        <IconButton
          id="friends-tooltip"
          onClick={() => {
            if (!isAuthenticated) {
              localStorage.setItem('activeTab', 0);
              setSelectedTab(0);
              openModal(<Auth />);
              return;
            }
            // setOpenChat(true);
            setShowHomePopup(false);

            setShowChat(true);
            setActiveTab('chat');

            setShowSearchInput(false);
            if (section === 'Friends') setSection('PublicChat');
            else setSection('Friends');
          }}
        >
          <FriendGoldenIcon
            className={`${
              section === 'Friends' ? 'fill-white-1000' : 'fill-steelTeal-1000'
            } transition-all duration-300 group-hover:fill-white-1000`}
          />
        </IconButton>
        <IconButton
          className="h-6 w-6 min-w-6"
          onClick={() => {
            setShowChatHeader(false);
            setShowHomePopup(false);
            // setOpenChat(false);
            setShowChat(false);
            setShowSearchInput(false);
          }}
        >
          <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
        </IconButton>
      </div>
    </div>
  );
};
export default ChatHeader;
