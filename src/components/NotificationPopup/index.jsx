"use client"
import NotificationIcon from '@/assets/icons/Notification';
import useChatWindow from '@/hooks/useChatWindow';
import useNotifications from '@/hooks/useNotification';
import { useUpdateNotificationMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import useNotificationStore from '@/store/useNoticeStore';
import { formatDateTime } from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';
import Auth from '../Auth';
import IconButton from '../Common/Button/IconButton';
import ReactTooltip from '../Common/ReactTooltip';
import NotificationGoldenIcon from '@/assets/icons/NotificationGoldenIcon';
import Lottie from 'lottie-react';
import CommonLoader from '@/assets/json/button-loader.json';
import { useOpenChatWindow } from '@/utils/chat';

const NotificationPopup = () => {
  // const [showNotificationPopup, setShowNotificationPopup] = useState(!false);
  const { openModal } = useModalStore((state) => state);
  const queryClient = useQueryClient()
  const { userDetails, isAuthenticated } = useAuthStore();
  const { setOpenChat } = useGeneralStore(
    (state) => state,
  );
  const router = useRouter()
  // const {setSection}=useChatWindow()
  const { setSection } = useChatWindow();
  const popupRef = useRef(null);
  const { refetch , notificationLoading} = useNotifications();
  const {
    setNotifications,
    addNotice,
    notifications,
    setShowNotificationPopup,
    showNotificationPopup,
    setPage,
    page,
  } = useNotificationStore();
  const openChatWindow=useOpenChatWindow()
  const updateNotification = useUpdateNotificationMutation({
    onSuccess: (response) => {
      if (response?.data?.success) {
        setPage(1);
        refetch();

        queryClient.invalidateQueries({
          queryKey: ['GET_NOTIFICATIONS_LIST_QUERY'],
          exact: true,
        });

        queryClient.invalidateQueries({
          queryKey: ['notificationsDetails'],
          exact: true,
        });
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
    },
  });
  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setShowNotificationPopup(false);
      }
    };

    if (showNotificationPopup) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotificationPopup]);
  const handleNotificationClick = (data) => {
    const { referenceType } = data;
    if (referenceType == 'FRIEND_REQUEST') {
      setSection('Friends');
    } else if (referenceType == 'GROUP_CHAT' || referenceType == 'GROUP_JOIN') {
      setSection('GroupChat');
    } else if (referenceType == 'PRIVATE_CHAT') {
      setSection('PrivateChat');
    }
    openChatWindow()
    setShowNotificationPopup(false);
    updateNotification.mutate({ notificationIds: [data?.notificationId] });
  };
  return (
    <div ref={popupRef}>
      <ReactTooltip message="Notifications" id="notification-tooltip" position='left' />
      <IconButton
        id="notification-tooltip"
        onClick={() => {
          if (!isAuthenticated) {
            localStorage.setItem('activeTab', 0);
            openModal(<Auth />);
            return;
          }
          setShowNotificationPopup(!showNotificationPopup);
        }}
      >
        <div className="relative">
          {/* Notification Icon */}
          <NotificationGoldenIcon />


          {/* Badge (Notification Count) */}
          {
            notifications?.count > 0 &&
            <span className="text-white absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-[10px] font-bold">
              {notifications?.count > 9 ? "9+" : notifications?.count}
            </span>
          }
        </div>
      </IconButton>

      {showNotificationPopup && (
        <div className="absolute left-0 top-[60px] z-10 grid max-h-[55dvh] w-full gap-2 overflow-y-auto rounded-md border border-[grey] bg-[#000000fa] p-[20px] xl:top-[46px]">
          {notificationLoading ? (
            <div className="flex h-10 w-full items-center justify-center p-10 text-center">
              <Lottie
                animationData={CommonLoader}
                loop
                className="h-10 w-20"
              />
            </div>
          ) : (
            <>
              {notifications?.notifications?.map((data, index) => (
                <div
                  key={index}
                  className="cursor-pointer rounded bg-[#80808024] p-2"
                  onClick={() => handleNotificationClick(data)}
                >
                  <p>{data?.message}</p>
                  <p>{formatDateTime(data?.createdAt)}</p>
                </div>
              ))}

              {notifications?.count == 0 && (
                <div
                  key="notfound"
                  className="cursor-pointer rounded bg-[#80808024] p-2"
                >
                  <p>No notifications found</p>
                </div>
              )}

              {notifications?.count !== 0 && (
                <button
                  className="border-gray-500 p-[6px] rounded-xl border"
                  onClick={() => {
                    router.push("/notifications");
                    setShowNotificationPopup(!showNotificationPopup);
                    if (window && window?.innerWidth < 1280) {
                      setOpenChat(false)
                    }
                  }}
                >
                  View More
                </button>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationPopup;
