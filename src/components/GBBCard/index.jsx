'use client'

import useModalStore from "@/store/useModalStore";
import IconButton from "../Common/Button/IconButton";
import { X } from "lucide-react";
import Image from 'next/image';
import MysteryBox from "../../assets/gif/mystery-box.gif";
import SCicon from "@/assets/icons/SCicon";
import PrimaryButtonOutline from "../Common/Button/PrimaryButtonOutline";
import MysteryBoxImg from "../../assets/webp/box.webp";
import { formatDateWithSuffix } from "@/utils/helper";
import { useEffect, useState } from "react";
import { useGreenBonusClaim } from "@/reactQuery/gamesQuery";
import ClaimCard from "../ClaimCard";
import GCicon from "@/assets/icons/GCicon";

const GBBCard = ({ bonusData }) => {
    const { openModal } = useModalStore((state) => state);
    const [claimText, setClaimText] = useState('')
    const { clearModals } = useModalStore((state) => state);
    let { isClaimable, bonusValidityWindow } = bonusData
    const { refetch } = useGreenBonusClaim({enabled:false});
    const bonusAmount = bonusData?.greenBonusDetails?.scBonusDetails?.bonusAmount?.toFixed(2)
    const bonusAmountGC = bonusData?.greenBonusDetails?.gcBonusDetails?.bonusAmount?.toFixed(2)



    const createConfetti = () => {
        const colors = ['#ff0', '#0f0', '#00f', '#f00', '#ff69b4']; // Customize colors
        const confettiCount = 100; // Number of confetti pieces

        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.position = 'fixed';
            confetti.style.top = '-10px'; // Start above the screen
            confetti.style.left = `${Math.random() * 100}vw`; // Random x-position
            confetti.style.width = `${Math.random() * 10 + 5}px`; // Random width
            confetti.style.height = `${Math.random() * 10 + 5}px`; // Random height
            confetti.style.backgroundColor =
                colors[Math.floor(Math.random() * colors.length)]; // Random color
            confetti.style.opacity = '0.8';
            confetti.style.transform = `rotate(${Math.random() * 360}deg)`; // Random rotation
            confetti.style.zIndex = '1000'; // Ensure it’s on top

            // Animation
            confetti.animate(
                [
                    { transform: `translateY(0) rotate(${Math.random() * 360}deg)` },
                    {
                        transform: `translateY(${window.innerHeight + 10}px) rotate(${Math.random() * 360}deg)`,
                    },
                ],
                {
                    duration: Math.random() * 2000 + 2000, // Random duration between 2-4 seconds
                    easing: 'linear',
                    fill: 'forwards',
                },
            );

            document.body.appendChild(confetti);

            // Cleanup: Remove confetti after animation
            setTimeout(() => {
                confetti.remove();
            }, 4000);
        }
    };

    const isClaimGreenBonusCalled = async () => {
        const result = await refetch();
        if (result?.data?.success) {
            openModal(<ClaimCard bonusAmount={bonusAmount} bonusAmountGC={bonusAmountGC}/>)
        }
        // Trigger confetti
        createConfetti();
    };

    return (
        <div className="w-full fixed left-0 right-0 top-0 z-50 bg-black-900 flex items-center justify-center h-full">
            <div className="max-w-[701px] w-full bg-bonusBg bg-center bg-cover rounded-xl relative">

                <IconButton
                    onClick={clearModals}
                    className="h-6 w-6 min-w-6 absolute z-[3] right-4 top-4"
                >
                    <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                </IconButton>

                <div className="flex">
                    <div className="py-12 max-sm:py-8 pl-10 max-sm:px-4 pr-2 max-w-[422px] w-full shrink-0 relative z-[1] max-sm:max-w-full max-sm:flex max-sm:flex-col max-sm:justify-center max-sm:items-center max-sm:text-center">
                        <h4 className="text-2xl max-sm:text-base font-normal text-white max-sm:!leading-none">You've earned a </h4>
                        <h2 className="text-5xl max-sm:text-2xl font-bold text-green-400">Green Bar Bonus!</h2>
                        <p className="text-base max-sm:text-xs font-normal text-white max-w-[300px] max-sm:max-w-60 w-full leading-5 max-sm:leading-4 mt-1">Some GBBs come with a Mystery Box a match bonus in SC or GC only claimable when you claim an active GBB.</p>

                        {bonusAmount && <div className="flex items-center gap-2 mt-8 max-sm:mt-4">
                            <div className="h-[42px] max-w-[134px] w-full flex items-center gap-2 relative rounded-md overflow-hidden">
                                <span style={{ opacity: 0.92 }} className="absolute w-full h-full bg-scarlet-500 backdrop-blur-[10px] before:content-[''] before:absolute before:w-full before:h-full before:bg-scratchBg before:bg-cover before:bg-no-repeat before:bg-center"></span>
                                <SCicon className='size-8' />
                                <span className="text-5xl font-bold text-primary-1000">{bonusAmount}</span>
                            </div>
                            {(bonusAmount && bonusData?.issuedGBB !== null) && <Image src={MysteryBoxImg} alt='' />}
                        </div>}

                        {bonusAmountGC && <div className="flex items-center gap-2 mt-8 max-sm:mt-4">
                            <div className="h-[42px] max-w-[134px] w-full flex items-center gap-2 relative rounded-md overflow-hidden">
                                <span style={{ opacity: 0.92 }} className="absolute w-full h-full bg-scarlet-1000 backdrop-blur-[10px] before:content-[''] before:absolute before:w-full before:h-full before:bg-scratchGCBG before:bg-cover before:bg-no-repeat before:bg-center"></span>
                                <GCicon className='size-8' />
                                {<span className="text-5xl font-bold text-primary-1000">{bonusAmountGC}</span>}
                            </div>
                            {(!bonusAmount && bonusAmountGC && bonusData?.issuedGBB !== null)&& <Image src={MysteryBoxImg} alt='' />}
                        </div>}
                        <div>
                            <PrimaryButtonOutline animated className={`mt-6 ${isClaimable ? 'mb-10' : 'mb-2'} max-sm:mb-5 !max-w-[134px] max-sm:!max-w-[179px] w-full bg-cetaceanBlue-500`} onClick={() => { isClaimable ? isClaimGreenBonusCalled() : setClaimText('You cannot claim the bonus') }}>Claim now</PrimaryButtonOutline>
                            <p className="h-30px text-red-500">{claimText}</p>
                        </div>
                        <p className="text-base font-normal text-white">{`Claim Window: ${formatDateWithSuffix(bonusValidityWindow?.startDate)} – ${formatDateWithSuffix(bonusValidityWindow?.endDate)}`}</p>
                    </div>

                    <div className="max-sm:hidden">
                        <Image src={MysteryBox} alt='' className="max-w-[400px] max-sm:max-w-80 max-xxs:max-w-56 absolute -right-[6%] max-sm:-right-7 top-0" />
                    </div>

                </div>

            </div>
        </div>
    )
}
export default GBBCard
