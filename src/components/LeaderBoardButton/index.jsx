import LeaderBoardIcon from '@/assets/icons/LeaderBoard';
import LeaderBoardToggle from '../Common/ToggleSwitch/LeaderBoardToggleSwitch';

function LeaderBoardButton({ isActive }) {
  return (
    <div className="flex w-full items-center justify-between gap-2.5 p-2.5 hover:bg-nav-gradient">
      <div className="flex items-center gap-2">
        <LeaderBoardIcon />

        <span className="text-base font-normal text-white-1000">
          LeaderBoard
        </span>
      </div>
      <div className="flex items-center gap-2">
        <LeaderBoardToggle />
      </div>
    </div>
  );
}

export default LeaderBoardButton;
