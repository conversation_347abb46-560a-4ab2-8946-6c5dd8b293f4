'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import useGeneralStore from '@/store/useGeneralStore';
import { useGetAllCmsPagesQuery } from '@/reactQuery/generalQueries';
import plus18 from '../../assets/images/svg-images/18+.svg';
import BrandLogo from '../../assets/images/logo/brand-logo.svg';
import { useParams } from 'next/navigation';

function Footer() {
  const { openChat,toggleSideMenu } = useGeneralStore((state) => state);
  const { gameId } = useParams();

  const { data: cmsLinks } = useGetAllCmsPagesQuery({
    enabled: true,
  });
  if (gameId) {
    return <></>
  }
  return (
    <footer
      className={`${openChat ? 'lg:mr-[20.5rem] lg:w-[calc(100%-14.75rem-20.5rem)]' : 'lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)]'}  ${toggleSideMenu?"lg:ml-[6rem]":" lg:ml-[14.75rem]"}    px-3 pb-[3.75rem] transition-all duration-300 ease-in-out xs:px-8  lg:pb-0 relative lg:z-[41]`}
    >
      <div className="mx-auto w-full lg:max-w-[67.5rem]">
        <div className="border-b border-white-100 pb-8 pt-12">
          <div className="grid grid-cols-2 gap-4 xs:grid-cols-3 md:grid-cols-5">
            <div className="col-span-1">
              <h6 className="mb-4 text-sm">Live Casino</h6>
              <div className="flex flex-col gap-1">
                <Link
                  href="/favorites"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Favorite
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Recent Play
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Evolution
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Pragmatic Play
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Table Games
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Hot
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  New
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Bonus Game
                </Link>
              </div>
            </div>
            <div className="col-span-1">
              <h6 className="mb-4 text-sm">Slot</h6>
              <div className="flex flex-col gap-1">
                <Link
                  href="/favorites"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Favorite
                </Link>
                <Link
                  href="/"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Recent Play
                </Link>
                <Link
                  href="/"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Evolution
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Pragmatic Play
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Table Games
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Hot
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  New
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Bonus Game
                </Link>
              </div>
            </div>
            <div className="col-span-1">
              <h6 className="mb-4 text-sm">Promotion</h6>
              <div className="flex flex-col gap-1">
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  VIP CLUB
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Promotion
                </Link>
                <Link
                  href="#"
                  className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                >
                  Gaming News
                </Link>
              </div>
            </div>
            <div className="col-span-1">
              <h6 className="mb-4 text-sm">Support / Legal</h6>
              <div className="flex flex-col gap-1">
                {cmsLinks &&
                  cmsLinks?.map((link) => (
                    <Link
                      href={`/${link.slug}`}
                      className="w-fit text-xs text-steelTeal-1000 duration-300 hover:text-primary-1000"
                      key={link.cmsPageId}
                    >
                      {link.title.EN}
                    </Link>
                  ))}
              </div>
            </div>
            <div className="col-span-1">
              <h6 className="mb-4 text-sm">Responsible Gaming</h6>
              <Image
                src={plus18}
                width={10000}
                height={10000}
                className="mb-4 w-[5.125rem] max-w-full"
                alt="Coin"
              />
            </div>
          </div>
        </div>
        <div className="flex items-center justify-start gap-8 py-4">
          <Image
            src={BrandLogo}
            width={1000}
            height={1000}
            className="w-full max-w-[3.75rem] grayscale"
            alt="Brand Logo"
          />
          <div>
            <p className="text-xs text-steelTeal-1000">
              <Link href="mailto:<EMAIL>">Support: <EMAIL> | Partners:</Link>
              <Link href="mailto:<EMAIL>"><EMAIL></Link>
            </p>
            <p className="text-xs text-steelTeal-1000">
              Copywrite@2024, Fansbets, All Right Reserved
            </p>
            {/* <p className="text-xs text-steelTeal-1000">
              Registration Number:XXXXX, Located At XXXXXXXXXXXXXXXXXXXXXXXXX
              Willemstad, Curacao
            </p> */}
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
