'use client';

import React from 'react';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import { useRouter } from 'next/navigation';

function OrderSuccess() {
  const router = useRouter();

  const backToInventory = () => {
    router.push('/inventory');
  };

  return (
    <div className="m-[0_auto] mt-7 w-full max-w-[864px]">
      <div className="flex flex-col items-center justify-center rounded-md  bg-tiber-1000 px-4 py-10">
        <h2 className="text-white mb-5 text-3xl max-sm:text-xl">
          Congratulations
        </h2>
        <p className="text-white text-center text-base leading-tight max-sm:text-sm">
          Your request has been successfully submitted.
        </p>
        <p className="text-white text-center text-base leading-tight max-sm:text-sm">
          We appreciate your effort and look forward to assisting you further.
        </p>
        <div className="mt-7 text-center">
          <PrimaryButton className="capitalize" onClick={backToInventory}>
            Back To Inventory
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
}

export default OrderSuccess;
