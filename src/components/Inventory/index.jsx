'use client';
import React, { useState } from 'react';

import React from 'react';
import ShoppigCartIcon from '@/assets/icons/ShoppigCartIcon';
import Select from 'react-select';

import PrimaryButton from '@/components/Common/Button/PrimaryButton';

const options = [
  { value: 'chocolate', label: 'Chocolate' },
  { value: 'strawberry', label: 'Strawberry' },
  { value: 'vanilla', label: 'Vanilla' },
];

function index() {
  const [selectedOption, setSelectedOption] = useState(null);
  return (
    <div>
      <section className="section-blur rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-maastrichtBlue-200 px-5 pb-10 pt-3.5">
        <div className="flex flex-wrap items-center gap-6 max-sm:flex-col">
          <h3 className="mr-auto flex grow items-center gap-4 text-xl font-normal leading-none text-white-1000 md:text-2xl max-sm:w-full">
            <ShoppigCartIcon className="h-5 w-5 fill-white-1000" />
            <span>Inventory</span>
          </h3>
        </div>
        <div className="relative m-[0_auto] mt-7 flex w-full max-w-[600px] items-center justify-between">
          <div className="flex flex-col items-center justify-between gap-3 text-white-1000">
            <span className="flex h-10 w-10 items-center justify-center rounded-full bg-scarlet-900 text-white-1000">
              1
            </span>
            Delivery address
          </div>
          <div className='flex flex-col items-center justify-between gap-3 text-steelTeal-1000 before:absolute before:left-[30%] before:top-[25%] before:h-[3px] before:w-[calc(50%_-_118px)] before:-translate-x-[54%] before:-translate-y-[46%] before:bg-steelTeal-1000 before:content-[""] after:absolute after:right-[12%] after:top-[25%] after:h-[3px] after:w-[calc(50%_-_116px)] after:-translate-x-[11%] after:-translate-y-[46%] after:bg-steelTeal-1000 after:content-[""] max-md:hidden max-md:before:hidden max-md:after:hidden'>
            <span className="flex h-10 w-10 items-center justify-center rounded-full bg-steelTeal-1000 text-white-1000">
              2
            </span>
            Order Summery
          </div>
          <div className="flex flex-col items-center justify-between gap-3 text-steelTeal-1000 max-md:hidden">
            <span
              className="flex h-10 w-10 items-center justify-center rounded-full bg-steelTeal-1000 text-white-1000"
              text-white-1000
            >
              3
            </span>
            Items and delivery
          </div>
        </div>

        <div className="m-[0_auto] mt-7 w-full max-w-[864px]">
          <div>
            <div className="mb-4">
              <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
                Address 2
              </label>
              <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
                <input
                  placeholder="Address"
                  className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
                  type="text"
                  name="Address"
                />
              </div>
            </div>
            <div className="">
              <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
                Address 2
              </label>
              <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
                <input
                  placeholder="Address"
                  className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
                  type="text"
                  name="Address"
                />
              </div>
            </div>

            <div className="mt-4 grid grid-cols-2 gap-5 max-xs:grid-cols-1">
              <div className="">
                <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
                  City
                </label>
                <div className="">
                  <Select
                    name="country"
                    className="form-input-select"
                    classNamePrefix="form-input"
                    placeholder="Select City"
                    defaultValue={selectedOption}
                    onChange={setSelectedOption}
                    options={options}
                  />
                </div>
              </div>

              <div className="">
                <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
                  State
                </label>
                <div className="">
                  <Select
                    name="country"
                    className="form-input-select"
                    classNamePrefix="form-input"
                    placeholder="Select State"
                    defaultValue={selectedOption}
                    onChange={setSelectedOption}
                    options={options}
                  />
                </div>
              </div>

              <div className="">
                <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
                  Postal Code
                </label>
                <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
                  <input
                    placeholder="Postal Code"
                    className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
                    type="text"
                    name="Address"
                  />
                </div>
              </div>
            </div>

            <div className="mt-7 text-center">
              <PrimaryButton>Save Address</PrimaryButton>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default index;
