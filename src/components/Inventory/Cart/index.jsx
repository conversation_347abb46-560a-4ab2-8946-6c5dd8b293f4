'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import MinusIcon from '@/assets/icons/MinusIcon';
import PlusIcon from '@/assets/icons/PlusIcon';
import Image from 'next/image';
import useCart from '@/hooks/useCartHook';
import CloseIcon from '@/assets/icons/CloseIcon';
import { useUpdateCartMutation } from '@/reactQuery/inventoryQuery';
import IconButton from '@/components/Common/Button/IconButton';
import useCartStore from '@/store/useCartStore';
import { useRouter } from 'next/navigation';

function Cart({ onNext, onPrevious }) {
  const router = useRouter();
  const { mutate: updateCart, isLoading: isUpdating } = useUpdateCartMutation();
  const { refetch, cartItems, cartLoading } = useCart();
  const { toggleShowCart, showCart } = useCartStore((state) => state);

  const [orderSessionId, setOrderSessionId] = useState(null);
  useEffect(() => {
    cartItems?.rows?.forEach((data) => {
      setOrderSessionId(data.orderSessionId);
    });
  }, [cartItems]);

  const handleQuantityChange = (inventoryId, action) => {
    updateCart({
      inventoryId,
      orderSessionId,
      action,
    });
  };

  if (!showCart) return null;

  const handleConfirmOrder = () => {
    const hasInactiveItems = cartItems?.rows?.some(
      (item) => item.inventory?.isActive === false,
    );

    if (hasInactiveItems) {
      toast.error(
        'One or more items in your cart are inactive. Please remove them before confirming the order.',
      );
      return;
    }

    router.push('/inventory/checkout');
    toggleShowCart();
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                Cart
              </h3>
            </div>

            <IconButton onClick={toggleShowCart} className="h-6 w-6 min-w-6">
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="text-white p-4">
            <div className="m-[0_auto] mt-7 w-full max-w-[864px]">
              <div className="mt-7 grid gap-8">
                {cartItems?.rows?.map(
                  ({ inventoryId, quantity, inventory, price }) => (
                    <div
                      className="flex items-start justify-between"
                      key={inventoryId}
                    >
                      <div className="flex items-start gap-4">
                        <Image
                          src={inventory?.inventoryImages[0]}
                          width={10000}
                          height={10000}
                          className="h-24 w-24 rounded-lg object-contain max-md:h-12 max-md:w-12"
                          alt="product image"
                        />

                        <div>
                          <h6 className="text-base font-bold text-white-1000 max-xs:text-sm">
                            {inventory?.name}
                          </h6>
                          <p className="text-base font-normal text-white-1000 max-xs:text-sm">
                            {price} Tickets
                          </p>
                          <p className="mt-2 text-red-1000">
                            {inventory?.isActive == false
                              ? 'This item is currently in-active'
                              : null}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end justify-between gap-4">
                        <p className="text-xl font-bold text-white-1000 max-xs:text-base">
                          {price * quantity} SC
                        </p>
                        <div className="flex items-center rounded-lg border border-solid border-steelTeal-1000">
                          <a
                            className="bg-gray h-full cursor-pointer px-2.5 py-2"
                            onClick={() =>
                              handleQuantityChange(inventoryId, 'deduct')
                            }
                          >
                            <MinusIcon />
                          </a>
                          <span className="h-full border-x border-solid border-steelTeal-1000 px-2.5 py-1 text-base leading-tight max-xs:text-sm">
                            {quantity}
                          </span>
                          <a
                            className="h-full cursor-pointer px-2.5 py-2"
                            onClick={() =>
                              handleQuantityChange(inventoryId, 'add')
                            }
                          >
                            <PlusIcon />
                          </a>
                        </div>
                      </div>
                    </div>
                  ),
                )}
              </div>
              {cartItems?.count == '0' && (
                <p className="p-5 text-center">Cart is empty</p>
              )}

              {cartItems?.count !== 0 && (
                <div className="mt-7 text-center">
                  <PrimaryButton
                    className="capitalize"
                    onClick={handleConfirmOrder}
                  >
                    confirm order
                  </PrimaryButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Cart;
