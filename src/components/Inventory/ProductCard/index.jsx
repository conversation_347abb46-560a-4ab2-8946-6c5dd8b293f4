'use client';

import React from 'react';
import Image from 'next/image';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import CartPlus from '@/assets/icons/CartPlus';
import useAddToCart from '@/hooks/useAddToCart';

function ProductCard({ product }) {
  const { addInventoryToCart, addToCartAndRedirect } = useAddToCart();

  const handleAddToCart = (inventoryId) => {
    addInventoryToCart({ inventoryId });
  };

  const handleGetNow = (inventoryId) => {
    addToCartAndRedirect({ inventoryId });
  };

  return (
    <div>
      <Image
        src={product?.inventoryImages[0]}
        width={10000}
        height={10000}
        className="rounded-lg"
        alt="product image"
      />
      <h6 className="mt-2.5 text-base font-normal leading-tight">
        {product.name}
      </h6>
      <p className="mt-.5 text-base font-normal">
        {product.receivedQuantity} Pcs
      </p>
      <p className="mt-.5 text-base font-normal">{product.tickets} AC</p>
      <div className="flex items-center justify-between gap-2">
        <PrimaryButton
          className="w-full !px-2 font-medium"
          onClick={() => handleGetNow(product?.inventoryId)}
        >
          Get now
        </PrimaryButton>
        <PrimaryButton
          className="!px-2"
          onClick={() => handleAddToCart(product?.inventoryId)}
        >
          <CartPlus />
        </PrimaryButton>
      </div>
    </div>
  );
}

export default ProductCard;
