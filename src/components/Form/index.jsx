import { Controller } from 'react-hook-form';
import Select from 'react-select';

export const FormInput = ({
  name,
  label,
  control,
  placeholder,
  error,
  readOnly = false,
}) => (
  <div className="mb-4">
    <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
      {label}
    </label>
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <input
          {...field}
          readOnly={readOnly}
          placeholder={placeholder}
          className={`text-white w-full rounded-md border p-[15.5px] text-base font-normal leading-none ${
            error ? 'border-red-500' : 'border-transparent'
          } bg-tiber-1000 placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000`}
        />
      )}
    />
    {error && <span className="text-red-500">{error.message}</span>}
  </div>
);

export const FormSelect = ({
  name,
  label,
  control,
  options,
  onChange,
  value,
  error,
}) => (
  <div>
    <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
      {label}
    </label>
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          {...field}
          options={options}
          onChange={onChange}
          className={`form-input-select ${error ? 'border-red-500' : ''}`}
          classNamePrefix="form-input"
          value={value}
          placeholder={`Select ${label}`}
        />
      )}
    />
    {error && <span className="text-red-500">{error.message}</span>}
  </div>
);
