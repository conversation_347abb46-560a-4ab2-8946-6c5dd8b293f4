import CallGoldenIcon from '@/assets/icons/CallGoldenIcon';
import CallRedIcon from '@/assets/icons/CallRedIcon';
import CheckCircleIcon from '@/assets/icons/Check-Circle';
import CrossCircleIcon from '@/assets/icons/Cross-Circle';
import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import MessageGoldenIcon from '@/assets/icons/MessageGoldenIcon';
import useChatWindow from '@/hooks/useChatWindow';
import useGroupCall from '@/hooks/useGroupCall';
import usePrivateCall from '@/hooks/usePrivateCall';
import { useTabState } from '@/hooks/useTabState';
import {
  useAcceptDeclineGroupJonRequest,
  useGetGroupRequestListQuery,
  useJoinGroup,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { useOpenChatWindow } from '@/utils/chat';
import { slugify } from '@/utils/helper';
import useAudioPlayer from '@/utils/useAudioPlayer';
import { useQueryClient } from '@tanstack/react-query';
import { ChevronDown, ChevronUp, Circle, Users } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import toast from 'react-hot-toast';
import ChatAvatar from '../ChatWindow/ChatAvatar';
import IconButton from '../Common/Button/IconButton';
import MainLoader from '../Common/Loader/MainLoader';
import CallIcon from '@/assets/icons/CallIcon';

function GroupTab({
  groups,
  publicGroups,
  lastGroupElementRef,
  groupsLoading,
  publicGroupLoading,
  isFetchingNextPageGroup,
  isFetchingNextPagePublicGroups,
  groupCount,
  myGroupsRef,
}) {
  const [openGroups, setOpenGroups] = useState({});
  const router = useRouter();
  const { userDetails } = useAuthStore((state) => state);
  const { audio } = useAudioPlayer();
  const { setVoiceCall, voiceCall } = useVoiceCallStore((state) => state);
  const { clearModals, openModal } = useModalStore((state) => state);
  const [channelName, setChannelName] = useState('');
  const { setIsGroupChatOpen, setGroupId, setGroupChat, setGroupName } =
    useGroupChatStore((state) => state);
  const queryClient = useQueryClient();
  const { setSection, section } = useChatWindow();
  const { activeGroupTab, setActiveGroupTab } = useTabState();
  const { initiateCall, disconnectCall, isCallActive } = usePrivateCall();
  const openChatWindow = useOpenChatWindow();
  const {
    initiateCall: initiateCallGroup,
    disconnectCall: disconnectCallGroup,
    isCallActive: isCallActiveGroup,
    handleDeclineCall: handleDeclineCallGroup,callInitialized
  } = useGroupCall();

  const toggleGroup = (groupId) => {
    setOpenGroups((prev) => ({
      // ...prev,
      [groupId]: !prev[groupId],
    }));
  };
  const {
    setIsPrivateChatOpen,
    setUserId,
    isCallActive: isPrivateCallActive,
    setIsCallActive,
    userId,
    searchUserName,
    setSearchUserName,
  } = usePrivateChatStore((state) => state);
  const {
    data: dataGroupInvitation,
    fetchNextPage: fetchNextPageGroupInvitation,
    hasNextPage: hasNextPageGroupInvitation,
    isFetchingNextPage: isFetchingNextPageGroupInvitation,
    status: statusGroupInvitation,
    isLoading: isLoadingInvitations,
  } = useGetGroupRequestListQuery({
    search: '',
    enabled: true,
    invitationType: 'invite',
  });
  const openChat = (userId) => {
    setUserId(userId);
    setIsPrivateChatOpen(true);
    if (searchUserName != '') {
      setSearchUserName('');
    }
    openChatWindow();
  };
  const openGroupChat = (groupId, groupName) => {
    // queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setSection('GroupChat');
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName != '') {
      setSearchUserName('');
    }
    openChatWindow();
  };

  const userStatusColor = (status) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-[#28a745] shadow-[0_0_5px_rgba(40,167,69,0.5)]'; // Green with subtle glow
      case 'RECENTLY_ACTIVE':
        return 'bg-[#ffc107] shadow-[0_0_5px_rgba(255,193,7,0.5)]'; // Yellow with subtle glow
      case 'BUSY':
        return 'bg-[#ffa500] shadow-[0_0_5px_rgba(255,193,7,0.5)]'; // Yellow with subtle glow
      case 'AWAY_MODE':
        return 'bg-[#f8f9fa] border border-[#ced4da] shadow-[0_0_5px_rgba(0,0,0,0.2)]'; // Light gray with a slight shadow
      case 'GHOST_MODE':
        return 'bg-[#ff0000] shadow-[0_0_5px_rgba(108,117,125,0.5)]'; // Dark gray with a subtle glow
      default:
        return 'bg-gray-500'; // Default color in case of unknown status
    }
  };

  const joinGroup = useJoinGroup({
    onSuccess: async (response) => {
      toast.success('Group Joined');
      queryClient.invalidateQueries({
        queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY'],
      });
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_LIST_QUERY'],
      });
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });
  const responseRequest = useAcceptDeclineGroupJonRequest({
    onSuccess: async (response, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['GET_GROUP_REQUEST_LIST_QUERY'],
      });

      toast.success(
        variables?.requestStatus == 'ACCEPTED'
          ? 'Request accepted successfully!'
          : 'Request declined successfully',
      );
    },
    onError: (error) => {
      console.error('Error sending join request:', error);
    },
  });

  return (
    <>
      <div className="flex items-center justify-center  gap-5 border-b border-gray-600 p-0 pt-2 md:p-5">
        <button
          onClick={() => {
            setActiveGroupTab('MyGroups');
            setOpenGroups({});
          }}
          className={`text-white cursor-pointer border-b-2  py-3 font-medium transition-colors duration-200 ${
            activeGroupTab === 'MyGroups'
              ? 'border-red-600'
              : 'border-transparent hover:text-red-300'
          }`}
        >
          My Groups {groupCount > 0 ? ` (${groupCount})` : '(0)'}
        </button>
        <button
          onClick={() => {
            setActiveGroupTab('PublicGroups');
            setOpenGroups({});
          }}
          className={`text-white cursor-pointer border-b-2  py-3 font-medium transition-colors duration-200 ${
            activeGroupTab === 'PublicGroups'
              ? 'border-red-600 '
              : 'border-transparent hover:text-red-300'
          }`}
        >
          Public Groups{' '}
          {publicGroups?.total > 0 ? `(${publicGroups?.total})` : '(0)'}
        </button>
        <button
          onClick={() => {
            setActiveGroupTab('Invitations');
            setOpenGroups({});
          }}
          className={`text-white cursor-pointer border-b-2  py-3 font-medium transition-colors duration-200 ${
            activeGroupTab === 'Invitations'
              ? 'border-red-600'
              : 'border-transparent hover:text-red-300'
          }`}
        >
          Invitations{' '}
          {dataGroupInvitation?.groups?.length > 0
            ? `(${dataGroupInvitation?.groups?.length})`
            : '(0)'}
        </button>
      </div>
      <div className="space-y-2  p-0 pt-5 md:space-y-5 md:p-5">
        {activeGroupTab === 'Invitations' && (
          <>
            {isLoadingInvitations ? (
              <div className="align-content-center flex h-[50dvh] items-center justify-center text-[18px]">
                <MainLoader className="w-32" />
              </div>
            ) : !dataGroupInvitation?.groups ||
              dataGroupInvitation?.groups?.length == 0 ? (
              <div className="text-md flex h-[50dvh] items-center justify-center py-6 text-center text-gray-400">
                No invitations found
              </div>
            ) : (
              <>
                {dataGroupInvitation?.groups?.map((group) => (
                  <div
                    className="flex  items-center justify-between rounded-b-lg rounded-t-lg border-l-4 border-yellow-500 bg-gray-700 px-5 py-3"
                    // onClick={() => toggleGroup(group.id)}
                  >
                    <div className="text-white  items-center justify-between rounded-lg p-3 text-lg  ">
                      {/* Group Name */}
                      <div className="flex items-center  font-bold text-yellow-400">
                        {/* Remove or keep the dot as per preference */}
                        {/* <div className="w-3 h-3 bg-yellow-400 rounded-full"></div> */}
                        {group?.UserChatGroup?.groupName}
                      </div>
                      <span className="text-[15px]">
                        {group?.UserChatGroup?.groupDescription}
                      </span>

                      {/* Online & Total Members Count */}
                    </div>

                    <div
                      className="text-white flex items-center "
                      disabled={responseRequest?.isPending}
                    >
                      {/* <Phone className="w-5 h-5 cursor-pointer" /> */}
                      <IconButton
                        onClick={() => {
                          responseRequest.mutate({
                            groupId: group?.groupId,
                            requestId: group?.id,
                            requestStatus: 'ACCEPTED',
                          });
                        }}
                        className="h-7 w-7 min-w-7 rounded-full transition hover:bg-green-800/20"
                        aria-label="Accept Invitation"
                        title="Accept"
                      >
                        <CheckCircleIcon className="fill-green-900" />
                      </IconButton>
                      <IconButton
                        onClick={() => {
                          responseRequest.mutate({
                            groupId: group?.groupId,
                            requestId: group?.id,
                            requestStatus: 'DECLINE',
                          });
                        }}
                        className="h-7 w-7 min-w-7 rounded-full transition hover:bg-red-800/20"
                        aria-label="Decline Invitation"
                        title="Decline"
                      >
                        <CrossCircleIcon className="fill-red-500" />
                      </IconButton>
                      {/* {openGroups[group.id] ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )} */}
                    </div>
                  </div>
                ))}
                {isFetchingNextPageGroupInvitation && (
                  <div className="align-content-center flex  items-center justify-center text-[18px]">
                    Loading more invitations...
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeGroupTab === 'MyGroups' && (
          <>
            {groupsLoading ? (
              <div className="align-content-center flex h-[50dvh] items-center justify-center text-[18px]">
                <MainLoader className="w-32" />
              </div>
            ) : !groups || groups?.length == 0 ? (
              <div className="text-md flex h-[50dvh] items-center justify-center py-6 text-center text-gray-400">
                No groups found
              </div>
            ) : (
              <>
                {groups?.map((group, index) => {
                  const isLast = index === groups.length - 1;

                  return (
                    <div
                      key={group.id}
                      className="rounded-lg bg-gray-800 shadow-md"
                    >
                      {/* Group Header */}
                      <div
                        className="flex cursor-pointer  items-start justify-between rounded-t-lg bg-gray-700 px-4 py-4 sm:flex-row sm:items-center sm:px-5"
                        onClick={() => toggleGroup(group.id)}
                        ref={isLast ? myGroupsRef : null}
                      >
                        {/* Left Side: Group Name and Member Count */}
                        <div className="text-white flex w-full flex-col flex-wrap  items-start xs:gap-0 sm:flex-row sm:items-center sm:gap-3">
                          {/* Group Name */}
                          <div className="flex items-center gap-2 text-yellow-400">
                            {/* Optional dot */}
                            {/* <div className="w-3 h-3 bg-yellow-400 rounded-full"></div> */}
                            <span className="break-all text-lg font-bold ">
                              {group.groupName}
                            </span>
                          </div>

                          {/* Online & Total Members Count */}
                          <div className="bg-white/10 mt-2 flex items-center gap-4 rounded-lg px-0  py-1 text-sm sm:mt-0 sm:px-4">
                            <div className="flex items-center gap-2">
                              <Users className="text-gray-400" />
                              <span className="font-semibold text-gray-300">
                                {group?.groupMembersCount}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Circle className="h-3 w-3 rounded-full bg-green-400 text-green-400" />
                              <span className="font-semibold text-green-300">
                                {group?.totalOnlineMembers}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="mt-3 flex items-center gap-4 sm:mt-0">
                          {callInitialized?.groupId === group.id ? (
                            <IconButton
                              disabled
                              className="bg-steelTeal-1000/20 h-6 w-6 animate-pulse rounded-full"
                            >
                              <CallIcon className="fill-steelTeal-500 h-5 w-5 opacity-70" />
                            </IconButton>
                          ) : (
                            !isPrivateCallActive &&
                            (voiceCall?.channelName ? (
                              voiceCall?.channelName == group.groupName ? (
                                <IconButton
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeclineCallGroup();
                                  }}
                                  className="h-6 w-6"
                                >
                                  <DisconnectIcon
                                    className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                    fill={'red'}
                                  />
                                </IconButton>
                              ) : (
                                ''
                              )
                            ) : !isCallActiveGroup ? (
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  initiateCallGroup({
                                    groupId: group.id,
                                    groupName: group.groupName,
                                  });
                                }}
                                className="h-6 w-6"
                              >
                                <CallGoldenIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                              </IconButton>
                            ) : (
                              // )
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeclineCallGroup();
                                }}
                                className="h-6 w-6"
                              >
                                <DisconnectIcon
                                  className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                  fill={'red'}
                                />
                              </IconButton>
                            ))
                          )}
                          <MessageGoldenIcon
                            className="h-5 w-5 cursor-pointer"
                            onClick={() =>
                              openGroupChat(group.id, group.groupName)
                            }
                          />
                          {openGroups[group.id] ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </div>
                      </div>
                      {/* Group Members */}
                      {openGroups[group.id] && (
                        <div className="grid grid-cols-1 gap-5 p-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 xxl:grid-cols-3">
                          {group?.members?.map((user, id) => (
                            <div
                              key={user.userId}
                              className="text-white relative flex flex-col rounded-lg bg-gray-700 p-4 shadow-md"
                            >
                              {/* User Info Section */}
                              <div className="flex items-center gap-3">
                                <div className="relative h-12 w-12 rounded-full">
                                  <ChatAvatar
                                    key={id}
                                    profileImage={user?.user?.profileImage}
                                    firstName={user?.user?.firstName}
                                    lastName={user?.user?.lastName}
                                    userName={user?.user?.username}
                                    imageClassName="h-full w-full rounded-full object-cover"
                                    imageWidth={48}
                                    imageHeight={48}
                                    avatarSize={48}
                                  />
                                  <div
                                    className={`absolute bottom-0 right-[-4px] z-30 h-3 w-3 rounded-full ${userStatusColor(
                                      user?.user?.currentStatus,
                                    )}`}
                                  />
                                </div>

                                {/* Username */}
                                <div className="flex-1">
                                  <div className="text-lg font-bold">
                                    {user?.user?.username}
                                  </div>
                                </div>

                                {/* Call & Message Buttons */}
                                {userDetails?.userId != user?.user?.userId && (
                                  <div className="flex gap-3">
                                    {user?.user?.areFriends &&
                                      //   <Phone
                                      //     className={`h-5 w-5 ${
                                      //       isCallActive ? 'text-red-600' : 'text-blue-400'
                                      //     } cursor-pointer`}
                                      //     onClick={() =>
                                      //       isCallActive
                                      //         ? disconnectCall(user?.user?.userId)
                                      //         : initiateCall(user?.user?.userId)
                                      //     }
                                      //   />
                                      // )}
                                      //      {
                                      (isCallActive ? (
                                        userId == user?.user?.userId && (
                                          <CallRedIcon
                                            className={`h-5 w-5  cursor-pointer`}
                                            onClick={() => {
                                              if (isCallActive) {
                                                disconnectCall(
                                                  user?.user?.userId,
                                                );
                                              } else {
                                                initiateCall(
                                                  user?.user?.userId,
                                                );
                                              }
                                            }}
                                          />
                                        )
                                      ) : (
                                        <CallGoldenIcon
                                          className={`h-5 w-5 ${isCallActive ? 'text-red-600' : 'text-blue-400'}  cursor-pointer`}
                                          onClick={() => {
                                            if (isCallActive) {
                                              disconnectCall(
                                                user?.user?.userId,
                                              );
                                            } else {
                                              initiateCall(user?.user?.userId);
                                            }
                                          }}
                                        />
                                      ))}
                                    <MessageGoldenIcon
                                      className="h-5 w-5 cursor-pointer text-blue-400"
                                      onClick={() => openChat(user?.userId)}
                                    />
                                  </div>
                                )}

                                {/* Game Details Section */}
                                {user?.user?.currentGamePlay && (
                                  <div className="mt-2 w-full rounded-lg bg-gray-600 px-3 py-2">
                                    <div className="flex items-center gap-3">
                                      {/* Game Thumbnail */}
                                      <div className="flex h-16 w-20 shrink-0 items-center justify-center rounded-md bg-gray-500">
                                        <Image
                                          src={
                                            user?.user?.currentGamePlay
                                              ?.gameThumbnail || ''
                                          }
                                          width={80}
                                          height={64}
                                          alt="Game image"
                                          className="h-full w-auto rounded-md object-cover"
                                        />
                                      </div>

                                      {/* Game Name & Button */}
                                      <div className="min-w-[120px] flex-1">
                                        <p className="truncate text-sm">
                                          {
                                            user?.user?.currentGamePlay
                                              ?.gameName
                                          }
                                        </p>
                                        <div className="mt-2 flex justify-end">
                                          <button
                                            className="text-white w-full cursor-pointer rounded bg-gray-700 px-3 py-1 text-sm sm:w-auto"
                                            onClick={() =>
                                              router.push(
                                                `/game/${slugify(user?.user?.currentGamePlay?.moreDetails?.product)}/${slugify(user?.user?.currentGamePlay?.gameName)}`,
                                              )
                                            }
                                          >
                                            Play Game
                                          </button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
                {isFetchingNextPageGroup && (
                  <div className="align-content-center flex  items-center justify-center text-[18px]">
                    Loading more groups...
                  </div>
                )}
              </>
            )}
          </>
        )}

        {activeGroupTab === 'PublicGroups' && (
          <>
            {publicGroupLoading ? (
              <div className="align-content-center flex h-[50dvh] items-center justify-center text-[18px]">
                <MainLoader className="w-32" />
              </div>
            ) : !publicGroups?.groups || publicGroups?.groups?.length == 0 ? (
              <div className="text-md flex h-[50dvh] items-center justify-center py-6 text-center text-gray-400">
                No groups found
              </div>
            ) : (
              <>
                {publicGroups?.groups?.map((group, index) => {
                  const isLast = index === publicGroups.groups.length - 1;

                  return (
                    <div
                      key={group.id}
                      className="rounded-lg bg-gray-800 shadow-md"
                      ref={isLast ? lastGroupElementRef : null}
                    >
                      {/* Group Header */}
                      <div
                        className="flex cursor-pointer  items-start justify-between rounded-t-lg bg-gray-700 px-4 py-4 sm:flex-row sm:items-center sm:px-5"
                        onClick={() => toggleGroup(group.id)}
                      >
                        {/* Left Side: Group Name and Member Count */}
                        <div className="text-white flex w-full flex-col flex-wrap  items-start xs:gap-0 sm:flex-row sm:items-center sm:gap-3">
                          {/* Group Name */}
                          <div className="flex items-center gap-2 text-yellow-400">
                            {/* Optional dot */}
                            {/* <div className="w-3 h-3 bg-yellow-400 rounded-full"></div> */}
                            <span className="break-words text-lg font-bold">
                              {group.groupName}
                            </span>
                          </div>

                          {/* Online & Total Members Count */}
                          <div className="bg-white/10 mt-2 flex items-center gap-4 rounded-lg px-0  py-1 text-sm sm:mt-0 sm:px-4">
                            <div className="flex items-center gap-2">
                              <Users className="text-gray-400" />
                              <span className="font-semibold text-gray-300">
                                {group?.groupMembersCount}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Circle className="h-3 w-3 rounded-full bg-green-400 text-green-400" />
                              <span className="font-semibold text-green-300">
                                {group?.totalOnlineMembers}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Right Side: Actions */}
                        <div className="mt-3 flex items-center gap-4 sm:mt-0">
                          {!isPrivateCallActive &&
                            group?.isUserExistInGroup &&
                            (voiceCall?.channelName ? (
                              voiceCall?.channelName == group.groupName ? (
                                <IconButton
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeclineCallGroup();
                                  }}
                                  className="h-6 w-6"
                                >
                                  <DisconnectIcon
                                    className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                    fill={'red'}
                                  />
                                </IconButton>
                              ) : (
                                ''
                              )
                            ) : !isCallActiveGroup ? (
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  initiateCallGroup({
                                    groupId: group.id,
                                    groupName: group.groupName,
                                  });
                                }}
                                className="h-6 w-6"
                              >
                                <CallGoldenIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 hover:fill-white-1000" />
                              </IconButton>
                            ) : (
                              // )
                              <IconButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeclineCallGroup();
                                }}
                                className="h-6 w-6"
                              >
                                <DisconnectIcon
                                  className="h-5 w-5 fill-red-600 transition-all duration-300 hover:fill-red-800"
                                  fill={'red'}
                                />
                              </IconButton>
                            ))}

                          <MessageGoldenIcon
                            className="h-5 w-5 cursor-pointer"
                            onClick={() =>
                              openGroupChat(group.id, group.groupName)
                            }
                          />
                          {!group?.isUserExistInGroup && (
                            <button
                              disabled={joinGroup?.isPending}
                              className="text-white flex items-center justify-center gap-2 rounded-2xl bg-primary-1000 px-4 py-2 text-sm font-semibold capitalize transition duration-200 hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-primary-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                joinGroup.mutate({
                                  groupId: group.id,
                                  action: 'join',
                                });
                              }}
                            >
                              Join
                            </button>
                          )}
                          {openGroups[group.id] ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </div>
                      </div>

                      {/* Group Members */}
                      {openGroups[group.id] && (
                        <div className="grid grid-cols-1 gap-5 p-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 xxl:grid-cols-3">
                          {group?.members?.map((user, id) => (
                            <div
                              key={user.userId}
                              className="text-white relative flex flex-col rounded-lg bg-gray-700 p-4 shadow-md"
                            >
                              {/* User Info Section */}
                              <div className="flex items-center gap-3">
                                <div className="relative h-12 w-12 rounded-full">
                                  <ChatAvatar
                                    key={id}
                                    profileImage={user?.user?.profileImage}
                                    firstName={user?.user?.firstName}
                                    lastName={user?.user?.lastName}
                                    userName={user?.user?.username}
                                    imageClassName="h-full w-full rounded-full object-cover"
                                    imageWidth={48}
                                    imageHeight={48}
                                    avatarSize={48}
                                  />
                                  <div
                                    className={`absolute bottom-0 right-[-4px] z-30 h-3 w-3 rounded-full ${userStatusColor(
                                      user?.user?.currentStatus,
                                    )}`}
                                  />
                                </div>

                                {/* Username */}
                                <div className="flex-1">
                                  <div className="text-lg font-bold">
                                    {user?.user?.username}
                                  </div>
                                </div>

                                {/* Call & Message Buttons */}
                                {userDetails?.userId != user?.user?.userId && (
                                  <div className="flex gap-3">
                                    {user?.user?.areFriends &&
                                      //   <Phone
                                      //     className={`h-5 w-5 ${
                                      //       isCallActive ? 'text-red-600' : 'text-blue-400'
                                      //     } cursor-pointer`}
                                      //     onClick={() =>
                                      //       isCallActive
                                      //         ? disconnectCall(user?.user?.userId)
                                      //         : initiateCall(user?.user?.userId)
                                      //     }
                                      //   />
                                      // )}
                                      //      {
                                      (isCallActive ? (
                                        userId == user?.user?.userId && (
                                          <CallRedIcon
                                            className={`h-5 w-5  cursor-pointer`}
                                            onClick={() => {
                                              if (isCallActive) {
                                                disconnectCall(
                                                  user?.user?.userId,
                                                );
                                              } else {
                                                initiateCall(
                                                  user?.user?.userId,
                                                );
                                              }
                                            }}
                                          />
                                        )
                                      ) : (
                                        <CallGoldenIcon
                                          className={`h-5 w-5 ${isCallActive ? 'text-red-600' : 'text-blue-400'}  cursor-pointer`}
                                          onClick={() => {
                                            if (isCallActive) {
                                              disconnectCall(
                                                user?.user?.userId,
                                              );
                                            } else {
                                              initiateCall(user?.user?.userId);
                                            }
                                          }}
                                        />
                                      ))}
                                    <MessageGoldenIcon
                                      className="h-5 w-5 cursor-pointer text-blue-400"
                                      onClick={() => openChat(user?.userId)}
                                    />
                                  </div>
                                )}
                              </div>

                              {/* Game Details Section */}
                              {/* {user?.user?.currentGamePlay && (
                        <div className="mt-2 w-full rounded-lg bg-gray-600 px-3 py-2">
                          <div className="flex items-center gap-3">
                            <div className="flex h-16 w-20 shrink-0 items-center justify-center rounded-md bg-gray-500">
                              <Image
                                src={
                                  user?.user?.currentGamePlay?.gameThumbnail ||
                                  ''
                                }
                                width={80}
                                height={64}
                                alt="Game image"
                                className="h-full w-auto rounded-md object-cover"
                              />
                            </div>

                            <div className="min-w-[120px] flex-1">
                              <p className="truncate text-sm">
                                {user?.user?.currentGamePlay?.gameName}
                              </p>
                              <div className="mt-2 flex justify-end">
                                <button
                                  className="text-white w-full cursor-pointer rounded bg-gray-700 px-3 py-1 text-sm sm:w-auto"
                                  onClick={() =>
                                    router.push(
                                      `/game/${slugify(user?.user?.currentGamePlay?.moreDetails?.product)}/${slugify(user?.user?.currentGamePlay?.gameName)}`,
                                    )
                                  }
                                >
                                  Play Game
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )} */}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
                {isFetchingNextPagePublicGroups && (
                  <div className="align-content-center flex  items-center justify-center text-[18px]">
                    Loading more groups...
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </>
  );
}

export default GroupTab;
