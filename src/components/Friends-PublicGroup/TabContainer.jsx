'use client';
import { useCallback, useEffect, useRef, useState } from 'react';

import useChatWindow from '@/hooks/useChatWindow';
import { useTabState } from '@/hooks/useTabState';
import {
  useGetFriendsListQuery,
  useGetGroupListQuery,
  useGetPublicGroupListQuery,
} from '@/reactQuery/chatWindowQuery';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import { getAccessToken } from '@/utils/helper';
import { walletSocket } from '@/utils/socket';
import FriendsTab from './FriendsTab';
import GroupTab from './GroupTab';

const TabContainer = () => {
  const { activeTab, toggleTab } = useTabState();
  const { setSection, section } = useChatWindow();
  const { setIsGroupChatOpen } = useGroupChatStore((state) => state);
  const { isAuthenticated } = useAuthStore((state) => state);
  const observerRef = useRef(null);
  const {
    data: friendsList,
    isLoading: friendsListLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({ params: { search: '' } });
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status,
    refetch,
    isLoading: isMyGroupsLoading,
  } = useGetGroupListQuery({ enabled: !!isAuthenticated });

  const {
    data: publicGroups,
    fetchNextPage: fetchNextPagePublicGroups,
    hasNextPage: hasNextPagePublicGroups,
    isFetchingNextPage: isFetchingNextPagePublicGroups,
    status: statusPublicGroups,
    isLoading: isPublicGroupLoading,
  } = useGetPublicGroupListQuery({
    search: '',
    enabled: !!isAuthenticated,
    groupType: 'public',
  });

  const [friendList, setFriendList] = useState(friendsList);
  const [groupList, setGroupListList] = useState(data?.groups);
  const [groupCount, setGroupCount] = useState(data?.total || 0);

  // Static data for demonstration
  const friends = Array.from({ length: 9 }, (_, i) => ({
    id: i + 1,
    name: `User ${i + 1}`,
    game: {
      name: 'Game Name',
      type: 'Game Type',
      availableSeats: 2,
    },
    inCall: i === 2, // Example: User 3 is in a call
  }));

  const groups = [
    {
      id: 1,
      name: 'Group Name',
      users: friends.slice(0, 3),
    },
    {
      id: 2,
      name: 'Group Name',
      users: friends.slice(3, 6),
    },
  ];

  const lastGroupElementRef = useCallback(
    (node) => {
      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (
            entries[0].isIntersecting &&
            hasNextPagePublicGroups &&
            !isFetchingNextPagePublicGroups
          ) {
            fetchNextPagePublicGroups();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) observerRef.current.observe(node);
    },
    [
      hasNextPagePublicGroups,
      isFetchingNextPagePublicGroups,
      fetchNextPagePublicGroups,
    ],
  );

  const myGroupsRef = useCallback(
    (node) => {
      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) observerRef.current.observe(node);
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage],
  );

  useEffect(() => {
    if (data?.total) setGroupCount(data?.total);
  }, [data]);

  useEffect(() => {
    walletSocket.auth = { token: getAccessToken() };
    walletSocket.connect();

    const handleUserStatus = (data) => {
      //   setSocketUserStatus(data?.data);

      // ✅ Updating Friends List
      setFriendList((prevFriends) => {
        if (!prevFriends || !prevFriends.rows) return prevFriends;

        const updatedFriends = {
          ...prevFriends,
          rows: prevFriends.rows.map((friend) =>
            friend?.relationUser?.userId === data?.data?.userId
              ? {
                  ...friend,
                  relationUser: {
                    ...friend.relationUser,
                    currentStatus: data?.data?.status,
                  },
                }
              : friend,
          ),
        };

        return updatedFriends;
      });

      // ✅ Updating Group List
      setGroupListList((prevGroups) => {
        const updatedGroups = prevGroups.map((group) => ({
          ...group,
          members: group.members.map((member) =>
            member.user.userId == data?.data?.userId
              ? {
                  ...member,
                  user: {
                    ...member.user,
                    currentStatus: data?.data?.status,
                  },
                }
              : member,
          ),
        }));

        return updatedGroups;
      });
    };

    walletSocket.on('USER_STATUS', handleUserStatus);

    return () => {
      walletSocket.off('USER_STATUS', handleUserStatus);
    };
  }, []); // ✅ No dependencies needed

  useEffect(() => {
    setFriendList(friendsList);
    setGroupListList(data?.groups);
  }, [friendsList, data?.groups]);

  return (
    <div className=" text-white min-h-screen">
      <div className="flex justify-center gap-2 ">
        <button
          onClick={() => {
            toggleTab('friends');
            setSection('PrivateChat');
            setIsGroupChatOpen(false);
          }}
          className={`rounded px-4 py-2 ${
            activeTab === 'friends'
              ? 'text-white bg-red-600'
              : 'text-white bg-gray-700'
          } cursor-pointer`}
        >
          Friends
        </button>
        <button
          onClick={() => {
            toggleTab('groups');
            setSection('GroupChat');
            setIsGroupChatOpen(false);
          }}
          className={`rounded px-4 py-2 ${
            activeTab === 'groups'
              ? 'text-white bg-red-600'
              : 'text-white bg-gray-700'
          } cursor-pointer`}
        >
          Groups
        </button>
      </div>
      {activeTab === 'friends' ? (
        <FriendsTab users={friendList} isLoading={friendsListLoading} />
      ) : (
        <GroupTab
          groups={groupList}
          publicGroups={publicGroups}
          lastGroupElementRef={lastGroupElementRef}
          groupsLoading={isMyGroupsLoading}
          publicGroupLoading={isPublicGroupLoading}
          isFetchingNextPageGroup={isFetchingNextPage}
          isFetchingNextPagePublicGroups={isFetchingNextPagePublicGroups}
          groupCount={groupCount}
          myGroupsRef={myGroupsRef}
        />
      )}
    </div>
  );
};

export default TabContainer;
