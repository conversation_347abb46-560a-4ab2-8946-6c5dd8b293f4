import React from 'react';
import Image from 'next/image';
import useEmblaCarousel from 'embla-carousel-react';
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from '@/components/Common/Button/EmblaArrowButtons';
import Card01 from '../../../assets/images/stock-images/card-01.png';
import Card02 from '../../../assets/images/stock-images/card-02.png';
import Card03 from '../../../assets/images/stock-images/card-03.png';
import Card04 from '../../../assets/images/stock-images/card-04.png';
import Card05 from '../../../assets/images/stock-images/card-05.png';
import coinSC from '../../../assets/images/stock-images/coin-sc.png';

function Cards(props) {
  const { slides, options } = props;
  const [emblaRef, emblaApi] = useEmblaCarousel(options);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <div>
      <div className="text-normal mb-3 flex justify-between gap-6 border-b border-primary-1000 py-2 text-sm leading-none text-white-1000 md:text-2xl">
        <h2 className="flex items-center justify-start gap-2">
          Completed Words: 0
        </h2>
        <h2 className="flex items-center justify-end gap-2">
          <span className="inline-block text-right">Bonus Pool: 2000</span>
          <span className="mb-1 flex h-4 w-4 items-center justify-center md:h-5 md:w-5">
            <Image
              src={coinSC}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full"
              alt="Store Banner"
            />
          </span>
        </h2>
      </div>

      <div className="flex flex-col items-center gap-6 md:flex-row md:items-start md:justify-between">
        <div className="relative z-0 flex min-h-[27.5rem] w-full max-w-[22.375rem] items-center justify-center overflow-hidden rounded-[0.625rem] bg-oxfordBlue-1000 px-3 py-4 before:pointer-events-none before:absolute before:left-[-25.25rem] before:top-[-23.375rem] before:z-[-1] before:block before:aspect-square before:w-[34.625rem] before:rounded-full before:bg-primary-1000 before:blur-[70px] after:pointer-events-none after:absolute after:bottom-[-30.25rem] after:right-[-24rem] after:z-[-1] after:block after:aspect-square after:w-[34.625rem] after:rounded-full after:bg-primary-1000 after:blur-[70px]">
          <div className="m-auto flex w-full items-center justify-center gap-2">
            <PrevButton
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
            />
            <div className="flex w-full max-w-[13.438rem] items-center justify-center">
              <div className="embla">
                <div className="embla__viewport overflow-hidden" ref={emblaRef}>
                  <div className="embla__container flex">
                    {Array(4)
                      .fill('')
                      .map((item, i) => {
                        return (
                          <div
                            key={i}
                            className="embla__slide min-w-0 shrink-0 grow-0 basis-full"
                          >
                            <Image
                              src={Card01}
                              width={10000}
                              height={10000}
                              className="mx-w-full h-auto w-full"
                              alt="Card"
                            />
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
            <NextButton
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
            />
          </div>
        </div>

        <div className="my-auto flex h-fit w-full gap-4 overflow-x-auto py-3 md:grid md:max-w-[34.5rem] md:grid-cols-4 md:gap-5 md:px-5 md:py-5">
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card01}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card05}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card04}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card03}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card02}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card05}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card01}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
          <div className="flex w-full items-center justify-center max-md:shrink-0 max-md:grow-0 max-md:basis-[7rem]">
            <Image
              src={Card04}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full grayscale"
              alt="Card"
            />
          </div>
        </div>
      </div>

      <div className="md:text-basel text-normal mt-3 flex justify-between gap-6 py-1 text-sm leading-none text-white-1000">
        <h3 className="flex items-center justify-start gap-2">
          Duration:2024.04.29-2024.05.06
        </h3>
        <h3 className="flex items-center justify-end gap-2">
          <span className="inline-block">Status:</span>
          <span className="inline-block text-green-1000">Active</span>
        </h3>
      </div>

      <div className="mt-10 w-full rounded-[0.625rem] border border-dashed border-steelTeal-1000 p-5">
        <h6 className="text-base font-normal text-steelTeal-1000">
          Game Rule:
        </h6>

        <ol className="list-inside list-decimal text-base font-normal text-steelTeal-1000">
          <li className="my-1.5">
            What's in the treasure chest
            <span className="block">
              Different amount of SC, GC, Chest, Letter Cards (Share Bonus Pool
              after Spell), Maybe NFT in future
            </span>
          </li>

          <li className="my-1.5">
            Requirements to open the treasure chest
            <ol className="list-inside list-[lower-alpha]">
              <li>Email already set</li>
              <li>
                Level 4 or higher (Buy gold coins and wager, easy to reach)
              </li>
            </ol>
          </li>

          <li className="my-1.5">
            How to get the treasure chest
            <ol className="list-inside list-[lower-alpha]">
              <li>Daily login, regular users can get 2, vip 3</li>
              <li>Spend $10 on the store, get 1 treasure chest</li>
              <li>Be active in the chat room</li>
              <li>Share to get 1 every day</li>
            </ol>
          </li>

          <li className="my-1.5">
            Expiration time of treasure chest
            <span className="block">
              The treasure chest is valid for 2 weeks, please open it in time to
              avoid expiration can not be used.
            </span>
          </li>

          <li className="my-1.5">
            Maximum number of valid treasure chests limit
            <span className="block">
              You can have up to 50 treasure chests at the same time, so please
              open them in time!
            </span>
          </li>
        </ol>
      </div>
    </div>
  );
}

export default Cards;
