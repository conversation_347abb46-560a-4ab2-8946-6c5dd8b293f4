'use client';

import React from 'react';
import Tabs from '@/components/Common/Tabs';
import Chest from './Chest';
import Cards from './Cards';
import ChestCardIcon from '@/assets/icons/Chest-Card';

function ChestAndCard() {
  const purchaseTabs = [
    {
      label: 'Chest',
      content: <Chest />,
    },
    {
      label: 'Cards',
      content: <Cards />,
    },
  ];

  return (
    <section className="section-blur rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-maastrichtBlue-200 px-5 pb-10 pt-3.5">
      <div className="flex flex-wrap items-center gap-6 max-sm:flex-col">
        <h3 className="mr-auto flex grow gap-4 md:text-2xl text-xl font-normal leading-none text-white-1000 max-sm:w-full">
          <ChestCardIcon className="h-5 w-5 fill-white-1000" />
          <span>Chest and Cards</span>
          <button className='text-steelTeal-1000 text-base font-bold leading-none ml-auto hover:text-primary-1000 duration-300 transition-all'>History</button>
        </h3>

        <Tabs tabs={purchaseTabs} />
      </div>
    </section>
  );
}

export default ChestAndCard;
