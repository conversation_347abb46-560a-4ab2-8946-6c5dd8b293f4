import React from 'react';
import Image from 'next/image';
import ChestBox from '../../../assets/images/stock-images/chest-box-img.png';
import coinGC from '../../../assets/images/stock-images/coin-gc.png';

function Chest() {
  return (
    <div>
      <div className="flex flex-col items-center gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div className="relative z-0 min-h-[27.5rem] w-full max-w-[19.688rem] overflow-hidden rounded-[0.625rem] bg-oxfordBlue-1000 px-3 py-4 before:absolute before:left-[-25.25rem] before:top-[-23.375rem] before:z-[-1] before:block before:aspect-square before:w-[34.625rem] before:rounded-full before:bg-primary-1000 before:blur-[70px] after:absolute after:bottom-[-30.25rem] after:right-[-24rem] after:z-[-1] after:block after:aspect-square after:w-[34.625rem] after:rounded-full after:bg-primary-1000 after:blur-[70px]">
          <div className="mx-auto mb-[1.875rem] mt-[2.375rem] flex w-full max-w-[13.375rem] items-center justify-center">
            <Image
              src={ChestBox}
              width={10000}
              height={10000}
              className="mx-w-full h-auto w-full"
              alt="Box"
            />
          </div>

          <h5 className="text-normal mb-10 text-center text-xl leading-tight text-white-1000">
            The treasure chest <br /> you have: 16
          </h5>

          <div className="grid grid-cols-2 gap-[0.625rem]">
            <div className="flex h-full w-full items-center justify-center rounded-md bg-primary-1000 p-3">
              <div className="text-normal leading-tigh flex flex-col items-center gap-1.5 text-center text-base text-white-1000">
                <h6 className="relative flex justify-center gap-1.5">
                  Single Open
                </h6>

                <h6 className="relative flex justify-center gap-1.5">
                  <span className="inline-block">400</span>
                  <span className="flex h-5 w-5 items-center justify-center">
                    <Image
                      src={coinGC}
                      width={10000}
                      height={10000}
                      className="mx-w-full h-auto w-full"
                      alt="Store Banner"
                    />
                  </span>
                </h6>

                <h6 className="relative flex scale-75 justify-center gap-1.5 opacity-50 after:absolute after:left-1/2 after:top-[38%] after:h-0.5 after:w-[110%] after:-translate-x-1/2 after:-translate-y-[62%] after:rotate-[-6deg] after:bg-[#8A0C02]">
                  <span className="inline-block">1000</span>
                  <span className="flex h-5 w-5 items-center justify-center">
                    <Image
                      src={coinGC}
                      width={10000}
                      height={10000}
                      className="mx-w-full h-auto w-full"
                      alt="Store Banner"
                    />
                  </span>
                </h6>
              </div>
            </div>

            <div className="flex h-full w-full items-center justify-center rounded-md bg-richBlack-1000 p-3">
              <div className="text-normal leading-tigh flex flex-col items-center gap-1.5 text-center text-base text-white-1000">
                <h6 className="relative flex justify-center gap-1.5">
                  All Open
                </h6>

                <h6 className="relative flex justify-center gap-1.5">
                  <span className="inline-block">600</span>
                  <span className="flex h-5 w-5 items-center justify-center">
                    <Image
                      src={coinGC}
                      width={10000}
                      height={10000}
                      className="mx-w-full h-auto w-full"
                      alt="Store Banner"
                    />
                  </span>
                </h6>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full rounded-[0.625rem] border border-dashed border-steelTeal-1000 p-5">
          <h6 className="text-base font-normal text-steelTeal-1000">
            Game Rule:
          </h6>

          <ol className="list-inside list-decimal text-base font-normal text-steelTeal-1000">
            <li className="my-1.5">
              What's in the treasure chest
              <span className="block">
                Different amount of SC, GC, Chest, Letter Cards (Share Bonus
                Pool after Spell), Maybe NFT in future
              </span>
            </li>

            <li className="my-1.5">
              Requirements to open the treasure chest
              <ol className="list-inside list-[lower-alpha]">
                <li>Email already set</li>
                <li>
                  Level 4 or higher (Buy gold coins and wager, easy to reach)
                </li>
              </ol>
            </li>

            <li className="my-1.5">
              How to get the treasure chest
              <ol className="list-inside list-[lower-alpha]">
                <li>Daily login, regular users can get 2, vip 3</li>
                <li>Spend $10 on the store, get 1 treasure chest</li>
                <li>Be active in the chat room</li>
                <li>Share to get 1 every day</li>
              </ol>
            </li>

            <li className="my-1.5">
              Expiration time of treasure chest
              <span className="block">
                The treasure chest is valid for 2 weeks, please open it in time
                to avoid expiration can not be used.
              </span>
            </li>

            <li className="my-1.5">
              Maximum number of valid treasure chests limit
              <span className="block">
                You can have up to 50 treasure chests at the same time, so
                please open them in time!
              </span>
            </li>
          </ol>
        </div>
      </div>
    </div>
  );
}

export default Chest;
