'use client';

import React, { useState, useEffect, useRef } from 'react';
import CaretDownIcon from '@/assets/icons/Caret-Down';

function Accordion({ items }) {
  const [active, setActive] = useState(false);
  const accordionRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        accordionRef.current &&
        !accordionRef.current.contains(event.target)
      ) {
        setActive(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleAccordion = () => {
    setActive((current) => !current);
  };

  return (
    <div className="mb-7" ref={accordionRef}>
      <button
        onClick={() => toggleAccordion()}
        type="button"
        className={`flex w-full items-center justify-start gap-2.5 ${active ? 'rounded-[10px_10px_0_0]' : 'rounded-[10px]'}  py-2.5 px-7 uppertab:px-4 max-md:px-4 bg-oxfordBlue-1000`}
        aria-controls="dropdown-pages"
        data-collapse-toggle="dropdown-pages"
      >
        {items.title}
        <CaretDownIcon
          className={`${active ? 'rotate-180' : ''} ml-auto h-3 w-3 fill-white-1000 transition-all duration-300`}
        />
      </button>
      <div
        id="dropdown-pages"
        role="region"
        aria-labelledby="dropdown-pages"
        className={`grid overflow-hidden text-sm text-slate-600 transition-all duration-300 ease-in-out bg-cetaceanBlue-1000 ${active ? 'grid-rows-[1fr] space-y-2 py-2 opacity-100 rounded-[0px_0px_10px_10px]' : 'grid-rows-[0fr] opacity-0'}`}
      >
        <ul className="overflow-hidden">{items.content}</ul>
      </div>
    </div>
  );
}

export default Accordion;
