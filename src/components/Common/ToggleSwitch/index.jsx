'use client';

import React, { useState } from 'react';

function ToggleSwitch({ onCheckChange, value }) {
  const [isChecked, setIsChecked] = useState(value);

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
    onCheckChange(!isChecked);
  };

  return (
    <label className="flex cursor-pointer select-none items-center">
      <div className="relative">
        <input
          type="checkbox"
          checked={isChecked}
          onChange={handleCheckboxChange}
          className="peer sr-only"
        />
        <div className="transition-color peer block h-7 w-[70px] rounded-full border border-solid border-oxfordBlue-1000 bg-richBlack-1000 duration-500 ease-linear peer-checked:bg-scarlet-900 max-md:w-14" />
        <div
          className="dot absolute left-0 top-1 flex h-5 w-full items-center justify-evenly rounded-full transition-all duration-500 ease-linear 
            
          peer-checked:[&>.cross-icon>.cross-svg]:fill-white-1000
          peer-checked:[&>.cross-icon]:translate-x-[32px]
          peer-checked:[&>.cross-icon]:scale-[-1] 
          peer-checked:[&>.cross-icon]:pr-0 
          peer-checked:[&>.cross-icon]:max-md:translate-x-[27px] 
          peer-checked:[&>.right-icon>.right-svg]:fill-white-1000
          peer-checked:[&>.right-icon]:-translate-x-[26px] 
          peer-checked:[&>.right-icon]:max-md:-translate-x-[20px]"
        >
          <span className="text-body-color inactive cross-icon pr-0 transition-all duration-500 ease-linear">
            <svg
              className="cross-svg max-md:h-[14px] max-md:w-[14px]"
              width="18"
              height="20"
              viewBox="0 0 18 20"
              fill="#072843"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M17.0711 17.0799C15.6725 18.4784 13.8907 19.4308 11.9509 19.8166C10.0111 20.2025 8.00043 20.0045 6.17317 19.2476C4.3459 18.4907 2.78412 17.209 1.6853 15.5645C0.58649 13.92 1.21811e-07 11.9866 0 10.0088C-1.21811e-07 8.03098 0.586489 6.09758 1.6853 4.45309C2.78412 2.8086 4.3459 1.52687 6.17316 0.769994C8.00043 0.0131176 10.0111 -0.184916 11.9509 0.200936C13.8907 0.586788 15.6725 1.5392 17.0711 2.93772L10 10.0088L17.0711 17.0799Z" />
            </svg>
          </span>

          <span className="active right-icon transition-all duration-500 ease-linear">
            <svg
              className="right-svg"
              width="21"
              height="7"
              viewBox="0 0 21 7"
              fill="#072843"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M3 6.5C4.65685 6.5 6 5.15685 6 3.5C6 1.84315 4.65685 0.5 3 0.5C1.34315 0.5 0 1.84315 0 3.5C0 5.15685 1.34315 6.5 3 6.5Z" />
              <path d="M18 6.5C19.6569 6.5 21 5.15685 21 3.5C21 1.84315 19.6569 0.5 18 0.5C16.3431 0.5 15 1.84315 15 3.5C15 5.15685 16.3431 6.5 18 6.5Z" />
            </svg>
          </span>
        </div>
      </div>
    </label>
  );
}

export default ToggleSwitch;
