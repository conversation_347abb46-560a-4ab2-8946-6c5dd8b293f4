'use client';

import { useUpdateStatusMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

function GhostModeToggle({  value }) {
  const [isChecked, setIsChecked] = useState(value);
  const { userDetails } = useAuthStore();
const queryClient=useQueryClient()
  const updateStatus = useUpdateStatusMutation({
    onSuccess: (response) => {
      if (response?.data?.success)
        setIsChecked(response?.data?.status == 'GHOST_MODE' ? true : false);
      toast.success('Status updated successfully!');
        // queryClient.invalidateQueries(['USER_PROFILE']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });
  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
    updateStatus.mutate({ status: isChecked ? 'AVAILABLE' : 'GHOST_MODE' });
  };

  useEffect(() => {
    setIsChecked(userDetails?.currentStatus == 'GHOST_MODE' ? true : false);
  }, [userDetails]);
  return (
    <label className="inline-flex cursor-pointer items-center">
      <input
        type="checkbox"
        checked={isChecked}
        onChange={handleCheckboxChange}
        className="peer sr-only"
        disabled={updateStatus.isPending}
      />
      <div
        className={`relative h-6 w-12 rounded-full border border-gray-300 shadow-md transition-all duration-300 ${isChecked &&"bg-red-700"}
        `}
      >
        {/* ${            isChecked ? 'bg-red-600' : 'bg-green-600'        } */}

        {/* Status Dot */}
        <div
          className={`absolute top-1 h-4 w-4 rounded-full bg-white-750 transition-all duration-300 ${
            isChecked ? 'left-7' : 'left-1'
          }`}
        ></div>
      </div>
    </label>
  );
}

export default GhostModeToggle;
