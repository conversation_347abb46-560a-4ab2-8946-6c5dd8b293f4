'use client';


function GroupToggleSwitch({ toggleGroup, setToggleGroup }) {

    const handleCheckboxChange = () => {
        setToggleGroup(toggleGroup == "MY_GROUPS" ? "PUBLIC_GROUPS" : "MY_GROUPS")

    };
    return (
        <div className="flex w-full items-center justify-center gap-4 py-3 hover:bg-nav-gradient">
            <div className="flex items-center gap-2">
                <span className="text-base font-normal text-white-1000">My Groups</span>
            </div>
            <div className="flex items-center gap-2">
                <label className="inline-flex cursor-pointer items-center">
                    <input
                        type="checkbox"
                        checked={toggleGroup == "PUBLIC_GROUPS" }
                        onChange={handleCheckboxChange}
                        className="peer sr-only"
                    />
                    <div
                        className={`relative h-6 w-12 rounded-full border border-gray-300 shadow-md transition-all duration-300 ${toggleGroup == "PUBLIC_GROUPS" && "bg-red-700"}
        `}
                    >

                        <div
                            className={`absolute top-1 h-4 w-4 rounded-full bg-white-750 transition-all duration-300 ${toggleGroup == "PUBLIC_GROUPS" ? 'left-7' : 'left-1'
                                }`}
                        ></div>
                    </div>
                </label>
            </div>
            <div className="flex items-center gap-2">
                <span className="text-base font-normal text-white-1000">Public Groups</span>
            </div>


        </div>
    );
}

export default GroupToggleSwitch;
