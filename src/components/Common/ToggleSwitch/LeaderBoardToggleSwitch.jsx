'use client';

import { useUpdateStatusMutation } from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

function LeaderBoardToggle({  value }) {
  const [isChecked, setIsChecked] = useState(value);
  const { userDetails } = useAuthStore();


  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
    // updateStatus.mutate({ status: isChecked ? 'AVAILABLE' : 'GHOST_MODE' });
  };

  useEffect(() => {
    setIsChecked(userDetails?.currentStatus == 'GHOST_MODE' ? true : false);
  }, [userDetails]);
  return (
<label
  className={`inline-flex items-center ${
    userDetails?.currentStatus === 'GHOST_MODE' ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
  }`}
>
  <input
    type="checkbox"
    checked={isChecked}
    onChange={handleCheckboxChange}
    className="peer sr-only"
    disabled={userDetails?.currentStatus === 'GHOST_MODE'}
  />
  <div
    className={`relative h-6 w-12 rounded-full border shadow-md transition-all duration-300 ${
      userDetails?.currentStatus === 'GHOST_MODE'
         &&  'bg-gray-400 border-gray-400'
      
    }`}
  >
    {/* Status Dot */}
    <div
      className={`absolute top-1 h-4 w-4 rounded-full transition-all duration-300 ${
        userDetails?.currentStatus === 'GHOST_MODE'
          ? 'left-1 bg-gray-300'
          : isChecked
          ? 'left-7 bg-white-1000'
          : 'left-1 bg-white-1000'
      }`}
    ></div>
  </div>
</label>

  );
}

export default LeaderBoardToggle;
