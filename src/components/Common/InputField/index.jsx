import React, { useState } from 'react';
import EyeCloseIcon from '@/assets/icons/Eye-Close';
import EyeOpenIcon from '@/assets/icons/Eye-Open';

function InputField({
  type,
  name,
  value,
  placeholder,
  onChange,
  error,
  label,
}) {
  const [showPassword, setShowPassword] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const isPasswordField = type === 'password';

  return (
    <div className="mb-1">
      <label
        htmlFor={name}
        className="mb-1 flex gap-2 text-base font-normal text-steelTeal-1000 relative"
      >
        {label}
        {isPasswordField && (
          <span
            className="flex h-5 w-5 items-center justify-center rounded-full border bg-gray-700 text-white-1000"
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            i
            {showTooltip && (
              <div className="text-white absolute bottom-full left-0 z-10 max-w-[372px] w-full rounded bg-gray-700 px-3 py-0.5 text-sm opacity-90">
                Password must be 8-10 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character
              </div>
            )}
          </span>
        )}
      </label>
      <div className="relative">
        <input
          type={isPasswordField && showPassword ? 'text' : type}
          name={name}
          id={name}
          placeholder={placeholder}
          className={`text-white w-full rounded-md border ${
            error ? 'border-red-500' : 'border-solid border-richBlack-1000'
          } bg-richBlack-1000 p-3 pr-10 text-base font-normal focus:border-steelTeal-1000 focus:shadow-inputInsetShadow focus:outline-none`}
          value={value}
          onChange={onChange}
          autoFocus="autofocus"
          autoComplete="off"
        />
        {isPasswordField && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 -translate-y-1/2 transform"
          >
            {showPassword ? (
              <EyeOpenIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            ) : (
              <EyeCloseIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            )}
          </button>
        )}
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
}

export default InputField;
