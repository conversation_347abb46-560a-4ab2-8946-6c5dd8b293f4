import React from 'react';

function Tooltip({ children, text, position = 'top' }) {
  let positionClasses;

  switch (position) {
    case 'top':
      positionClasses = 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
      break;
    case 'right':
      positionClasses = 'left-full ml-2 top-1/2 transform -translate-y-1/2';
      break;
    case 'bottom':
      positionClasses = 'top-full mt-2 left-1/2 transform -translate-x-1/2';
      break;
    case 'left':
      positionClasses = 'right-full mr-2 top-1/2 transform -translate-y-1/2';
      break;
    default:
      positionClasses = 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
      break;
  }

  return (
    <div className="relative flex items-center">
      <div className="group relative">
        {children}
        <div
          className={`bg-white absolute hidden w-max rounded border border-gray-200 bg-gray-200 p-2 text-sm text-gray-900 group-hover:block ${positionClasses}`}
        >
          {text}
          {/* <div
            className={`absolute h-4 w-4 rotate-45 transform bg-gray-800 ${position === 'top' ? 'left-1/2 top-full -ml-2' : ''} ${position === 'right' ? 'left-full top-1/2 -mt-2' : ''} ${position === 'bottom' ? 'bottom-full left-1/2 -ml-2' : ''} ${position === 'left' ? 'right-full top-1/2 -mt-2' : ''}`}
          /> */}
        </div>
      </div>
    </div>
  );
}

export default Tooltip;
