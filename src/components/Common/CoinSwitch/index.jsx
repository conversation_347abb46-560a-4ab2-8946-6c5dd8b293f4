import { twMerge } from 'tailwind-merge';
import { motion } from 'framer-motion';

import React from 'react';

function CoinSwitch({ checked, setChecked }) {
  return (
    <form className="flex items-center  space-x-4 antialiased">
      <label
        htmlFor="checkbox"
        className={`relative  flex  h-7 w-[60px] cursor-pointer items-center rounded-full border border-transparent px-1 shadow-[inset_0px_0px_12px_rgba(0,0,0,0.25)] transition duration-200 ${checked ? 'bg-cyan-500' : 'border-slate-500 bg-slate-700'}`}
      >
        <motion.div
          initial={{
            width: '20px',
            x: checked ? 0 : 32,
          }}
          animate={{
            height: ['20px', '10px', '20px'],
            width: ['20px', '30px', '20px', '20px'],
            x: checked ? 32 : 0,
          }}
          transition={{
            duration: 0.3,
            delay: 0.1,
          }}
          key={String(checked)}
          className={twMerge(
            'bg-white z-10 block h-[20px] rounded-full shadow-md',
          )}
        />
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
          className="hidden"
          id="checkbox"
        />
      </label>
    </form>
  );
}

export default CoinSwitch;
