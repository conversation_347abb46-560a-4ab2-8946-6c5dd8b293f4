'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useModalStore from '@/store/useModalStore';

function Modal() {
  const { components } = useModalStore();

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 0.5, transition: { duration: 0.2, ease: 'easeOut' } },
    exit: { opacity: 0, transition: { duration: 0.3, ease: 'easeIn' } },
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.85 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.1, ease: 'easeOut' },
    },
    exit: {
      opacity: 0,
      scale: 0.85,
      transition: { duration: 0.1, ease: 'easeIn' },
    },
  };

  return (
    <AnimatePresence>
      {components.length > 0 &&
        components.map((Component, i) => (
          <motion.div
            key={i}
            className="fixed inset-0 z-50 flex h-full w-full items-center justify-center"
          >
            <motion.div
              variants={backdropVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="bg-black absolute inset-0"
              style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
            />

            <motion.div
              variants={modalVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="bg-white relative z-10 w-full max-w-[600px] rounded-lg p-6 shadow-lg"
              style={{ minHeight: '200px' }}
            >
              {Component}
            </motion.div>
          </motion.div>
        ))}
    </AnimatePresence>
  );
}

export default Modal;
