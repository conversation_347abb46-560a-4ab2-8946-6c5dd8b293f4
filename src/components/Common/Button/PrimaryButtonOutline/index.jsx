import React from 'react';

function PrimaryButtonOutline({
  children,
  className = '',
  disabled,
  animated = false, 
  ...props
}) {
  const animatedClasses = `overflow-hidden relative transition-all duration-300 ${
    disabled
      ? ''
      : `ease-in-out active:scale-90 z-[1] 
         before:content-[""] before:w-full before:h-full before:absolute 
         before:bg-primary-1000 before:-top-full before:left-0 
         before:z-[-1] hover:before:top-0 
         before:transition-all before:duration-300 before:ease-in-out`
  }`;

  const baseClasses = `text-white min-h-10 rounded-md border-2 border-solid 
    border-primary-1000 bg-richBlack-500 px-6 py-1.5 text-base font-normal`;

  return (
    <button
      {...props}
      className={`${baseClasses} ${animated ? animatedClasses : ''} ${className}`}
    >
      {children}
    </button>
  );
}

export default PrimaryButtonOutline;
