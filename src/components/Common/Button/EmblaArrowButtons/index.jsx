import React, { useCallback, useEffect, useState } from 'react';
import ArrowCircleLeftIcon from '@/assets/icons/Arrow-Circle-Left';
import ArrowCircleRightIcon from '@/assets/icons/Arrow-Circle-Right';

export const usePrevNextButtons = (emblaApi) => {
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true);
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true);

  const onPrevButtonClick = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollPrev();
  }, [emblaApi]);

  const onNextButtonClick = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback((emblaApi) => {
    setPrevBtnDisabled(!emblaApi.canScrollPrev());
    setNextBtnDisabled(!emblaApi.canScrollNext());
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onSelect(emblaApi);
    emblaApi.on('reInit', onSelect).on('select', onSelect);
  }, [emblaApi, onSelect]);

  return {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  };
};

export function PrevButton(props) {
  const { children, className, ...restProps } = props;

  return (
    <button
      className={`embla__button embla__button--prev group flex h-10 w-10 min-w-10 items-center justify-center rounded-full transition-all duration-300 hover:bg-black-200 active:scale-90 ${className}`}
      type="button"
      {...restProps}
    >
      <ArrowCircleLeftIcon className="h-6 w-6 ArrowCircleLeftIcon fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
      {children}
    </button>
  );
}

export function NextButton(props) {
  const { children, className, ...restProps } = props;

  return (
    <button
      className={`embla__button embla__button--next group flex h-10 w-10 min-w-10 items-center justify-center rounded-full transition-all duration-300 hover:bg-black-200 active:scale-90 ${className}`}
      type="button"
      {...restProps}
    >
      <ArrowCircleRightIcon className="h-6 w-6 ArrowCircleRightIcon fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
      {children}
    </button>
  );
}
