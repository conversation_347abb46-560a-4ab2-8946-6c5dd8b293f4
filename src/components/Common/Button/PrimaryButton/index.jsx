'use client';

import React from 'react';
import <PERSON><PERSON> from 'lottie-react';
import CommonLoader from '../../../../assets/json/button-loader.json';

function PrimaryButton({
  children,
  type = 'button',
  onClick,
  className = '',
  isLoading = false,
  disabled,
}) {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`flex max-h-10 min-h-10 cursor-pointer items-center justify-center rounded-md border-2 border-solid border-primary-1000 bg-primary-1000 px-6 py-1.5 text-base font-normal text-white-1000 transition-all duration-300 active:scale-90 ${className} ${disabled ? 'text-steelTeal-1000 opacity-80' : ''}`}
    >
      {isLoading ? (
        <Lottie animationData={CommonLoader} loop className="h-full w-8" />
      ) : (
        children
      )}
    </button>
  );
}

export default PrimaryButton;
