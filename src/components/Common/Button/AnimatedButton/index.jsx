import React from 'react'

function AnimatedButton({
    children,
    type = 'button',
    className = '',
    disabled,
    onClick,
  }) {
  return (
    <button
      type={type}
      onClick={onClick}
      className={`overflow-hidden flex flex-col items-center w-full max-h-10 min-h-10 cursor-pointer relative justify-center rounded-md border-2 border-solid border-borderColor-100 bg-primary-1000 animationb px-6 py-1.5 text-base font-normal text-white-1000 transition-all duration-300 ease-in-out active:scale-90
        before:content-[''] before:absolute before:w-4 before:h-[120%] before:-right-6 before:animate-whiteShadow before:bg-white-1000 before:blur-[9px] before:rotate-12
        ${className} ${disabled ? 'text-steelTeal-1000 opacity-80' : ''}`}
    >
      <span className='absolute top-[30%] translate-y-full animate-heroOneText'>Need More Coins?</span>
      <span className='text-black-1000 absolute top-[30%] translate-y-full font-bold animate-heroTwoText uppercase'>Buy Now</span>
    </button>
  )
}

export default AnimatedButton;