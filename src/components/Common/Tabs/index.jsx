'use client';

function Tabs({ tabs, classes, selectedTab=0, setSelectedTab=()=>{}, isVisible=true }) {
  const handleTabChange = (index) => {
    // setActiveTab(index);
    setSelectedTab(index);
  };

  return (
    <>
      {isVisible && <div className={`flex justify-start md:justify-start ${classes}`}>
        <ul className="relative w-fit flex items-center justify-start rounded-md overflow-hidden gradient-border-before bg-white">
         {tabs?.map((tab, idx) => (
            <li className="z-[1]" key={tab.label}>
              <button
                type="button"
                aria-current="page"
                onClick={() => handleTabChange(idx)}
                className={`text-base font-normal ${
                  selectedTab === idx
                    ? 'bg-primary-1000 text-white-1000'
                    : 'text-steelTeal-1000'
                } inline-block rounded-md px-5 py-[7px] text-center`}

              >
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>}
      <div className="tabsContent mx-auto w-full">
        {tabs[selectedTab].content}
      </div>
    </>
  );
}

export default Tabs;
