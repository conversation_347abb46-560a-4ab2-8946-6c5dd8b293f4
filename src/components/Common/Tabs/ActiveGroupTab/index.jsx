'use client';

import useActiveGroupStore from '@/store/useActiveGroupStore';

function Tabs({ tabs, classes }) {
  const { activeTab, setActiveTab, myfriends } = useActiveGroupStore();




  return (
    <>
      <div className=" embla w-[100%] max-sm:w-full">
        <div
          className={`flex items-center  md:justify-start max-sm:w-full max-sm:flex-col max-sm:items-start max-sm:gap-4 ${classes}`}
        >
          <h3 className="pr-4 text-2xl font-bold text-white-1000">
            Active Groups
          </h3>
          <ul className="gradient-border-before bg-white relative flex w-fit items-center justify-start overflow-hidden rounded-md">
            {tabs?.map((tab, idx) => (
              <li className="z-[1]" key={tab.label}>
                <button
                  type="button"
                  aria-current="page"
                  onClick={() => setActiveTab(idx)}
                  className={`text-base font-normal ${
                    activeTab === idx
                      ? 'bg-primary-1000 text-white-1000'
                      : 'text-steelTeal-1000'
                  } inline-block rounded-md px-5 py-[7px] text-center`}
                >
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>
   
      </div>
      <div className="tabsContent mx-auto w-full">
        {tabs[activeTab].content}
      </div>
    </>
  );
}

export default Tabs;
