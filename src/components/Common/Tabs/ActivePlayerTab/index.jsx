'use client';

import { useCallback, useState } from 'react';
import OnlineUserImg from '@/assets/images/demo-image/user-img.jpg';
import usePlayerStore from '@/store/usePlayerStore';
import { useGetFriendsListQuery } from '@/reactQuery/chatWindowQuery';
import UserInfo from '@/components/UserInfoModal';
import useModalStore from '@/store/useModalStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import useActivePlayerStore from '@/store/useActivePlayeStore';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import useEmblaCarousel from 'embla-carousel-react';
import { ArrowLeft } from 'lucide-react';
import { ArrowRight } from 'lucide-react';
import MainLoader from '../../Loader/MainLoader';
import Skeleton from 'react-loading-skeleton';
import useAuthStore from '@/store/useAuthStore';

function Tabs({ tabs, classes }) {
  const { activeTab, setActiveTab, myfriends } = useActivePlayerStore();
  const { userDetails } = useAuthStore();
  const handleTabChange = (index) => {
    /* if (myfriends?.length != 0) */ setActiveTab(index);
  };
  const { openModal } = useModalStore();
  const { openUserInfoModal } = useUserInfoStore((state) => state);

  const {
    data: friendsList,
    isLoading: friendsLoading,
    refetch: refetchFriendsList,
  } = useGetFriendsListQuery({
    params: { search: '', playersStatus: 'AVAILABLE' },
  });

  const { livePlayers, isPlayerLoading } = usePlayerStore((state) => state);
  const userStatusColor = (status) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-400 '; // Green with subtle glow
      case 'RECENTLY_ACTIVE':
        return 'bg-[#ffc107] '; // Yellow with subtle glow
      case 'BUSY':
        return 'bg-[#ffa500] '; // Yellow with subtle glow
      case 'AWAY_MODE':
        return 'bg-[#f8f9fa] border border-[#ced4da] '; // Light gray with a slight shadow
      case 'GHOST_MODE':
        return 'bg-[#ff0000] '; // Dark gray with a subtle glow
      default:
        return 'bg-gray-500'; // Default color in case of unknown status
    }
  };
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    dragFree: true,
    skipSnaps: false,
  });

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi],
  );
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi],
  );
  return (
    <>
      <div className=" embla w-[100%] max-sm:w-full">
        <div
          className={`flex items-center  md:justify-start max-sm:w-full max-sm:flex-col max-sm:items-start max-sm:gap-4 ${classes}`}
        >
          <h3 className="pr-4 text-2xl font-bold text-white-1000">
            Active Players
          </h3>
          <ul className="gradient-border-before bg-white relative flex w-fit items-center justify-start overflow-hidden rounded-md">
            {tabs?.map((tab, idx) => (
              <li className="z-[1]" key={tab.label}>
                <button
                  type="button"
                  aria-current="page"
                  onClick={() => handleTabChange(idx)}
                  className={`text-base font-normal ${
                    activeTab === idx
                      ? 'bg-primary-1000 text-white-1000'
                      : 'text-steelTeal-1000'
                  } inline-block rounded-md px-5 py-[7px] text-center`}
                >
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex items-center">
          {/* {
            (activeTab == 0 && myfriends?.length > 0 && friendsList?.rows?.length > 0 || activeTab == 1 && livePlayers?.length > 0) &&
            <button onClick={scrollPrev} className="text-white"> <ArrowLeft /> </button>
          } */}
          <div
            className="embla__viewport scrollbar-none mr-[30px] overflow-auto "
            ref={emblaRef}
          >
            <div className="embla__container mt-4 flex gap-4">
              {activeTab === 0 && (
                <>
                  {friendsLoading ? (
                    <div className="align-content-center flex gap-4 ">
                      <>
                        {new Array(4).fill().map((_, i) => (
                          <Skeleton
                            width={60}
                            height={60}
                            className={` block`}
                            circle={true}
                            key={i}
                          />
                        ))}
                      </>
                    </div>
                  ) : !friendsList?.rows || friendsList?.rows?.length == 0 ? (
                    <div className="text-[1rem] font-[600]">
                      Currently No Friends Are Online
                    </div>
                  ) : (
                    <>
                      {friendsList?.rows?.map((player, i) => (
                        <div
                          className="embla__slide flex flex-col items-center"
                          key={i}
                        >
                          <div
                            className="relative h-[69px]"
                            style={{ width: 'max-content' }}
                          >
                            <div
                              className="mx-auto inline-flex size-[60px] cursor-pointer items-center justify-center rounded-full border border-solid border-richBlack-900"
                              onClick={() => {
                                openUserInfoModal(player?.relationUser?.userId);
                                openModal(<UserInfo />);
                              }}
                            >
                              <ChatAvatar
                                profileImage={
                                  player?.relationUser?.profileImage
                                }
                                firstName={player?.relationUser?.firstName}
                                lastName={player?.relationUser?.lastName}
                                userName={player?.relationUser?.username}
                                imageClassName="size-[60px] rounded-full"
                                imageWidth={60}
                                imageHeight={60}
                                avatarSize={60}
                              />
                              <span
                                className={`absolute bottom-3 right-1 z-[1] size-3 rounded-full ${userStatusColor(
                                  player?.relationUser?.currentStatus,
                                )}`}
                              />
                            </div>
                          </div>
                          <div className="text-center text-[0.75rem] font-normal text-white-1000">
                            {player?.relationUser?.username}
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                </>
              )}
              {activeTab == 1 && (
                <>
                  {isPlayerLoading ? (
                    <div className="align-content-center flex gap-4 ">
                      <>
                        {new Array(4).fill().map((_, i) => (
                          <Skeleton
                            width={60}
                            height={60}
                            className={` block`}
                            circle={true}
                            key={i}
                          />
                        ))}
                      </>
                    </div>
                  ) : !livePlayers || livePlayers?.length == 0 ? (
                    <div className="text-[1rem] font-[600]">
                      Currently No Users Are Online
                    </div>
                  ) : (
                    <>
                      {livePlayers?.map((player, i) => {
                        if (userDetails?.userId !== player?.userId)
                          return (
                            <div
                              className="embla__slide flex flex-col items-center"
                              key={i}
                            >
                              <div
                                className="relative h-[69px]"
                                style={{ width: 'max-content' }}
                              >
                                <div
                                  className="mx-auto inline-flex size-[60px] cursor-pointer items-center justify-center rounded-full border border-solid border-richBlack-900"
                                  onClick={() => {
                                    openUserInfoModal(player?.userId);
                                    openModal(<UserInfo />);
                                  }}
                                >
                                  <ChatAvatar
                                    profileImage={player?.profileImage}
                                    firstName={player?.firstName}
                                    lastName={player?.lastName}
                                    userName={player?.username}
                                    imageClassName="size-[60px] rounded-full"
                                    imageWidth={60}
                                    imageHeight={60}
                                    avatarSize={60}
                                  />

                                  <span className="absolute bottom-3 right-1 z-[1] size-3 rounded-full bg-green-400" />
                                </div>
                              </div>
                              <div className="text-center text-[0.75rem] font-normal text-white-1000">
                                {player.username}
                              </div>
                            </div>
                          );
                      })}
                    </>
                  )}
                </>
              )}
            </div>
            <div />
          </div>
          {/* {
            (activeTab == 0 && myfriends?.length > 0 && friendsList?.rows?.length > 0 || activeTab == 1 && livePlayers?.length > 0) &&
            <button onClick={scrollNext} className="text-white absolute right-0"> <ArrowRight /> </button>
          } */}
        </div>
      </div>
      <div className="tabsContent mx-auto w-full">
        {tabs[activeTab].content}
      </div>
    </>
  );
}

export default Tabs;
