'use client';

import ChatWindow from '@/components/ChatWindow';
import useAuthStore from '@/store/useAuthStore';
import useChatStore from '@/store/useChatStore';
import useGeneralStore from '@/store/useGeneralStore';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
export default function MainSection({ children }) {
  const { openChat, toggleSideMenu } = useGeneralStore((state) => state);
  const { isAuthenticated } = useAuthStore((state) => state);
  const pathname = usePathname();
  const { showChat}=useChatStore()
  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'instant',
    });
  }, []);
  return (
    <div
      className={`${pathname == '/vip' && 'bg-vip-tier-bg bg-cover bg-no-repeat'} 
      ${openChat || showChat ? 'lg:mr-[20.5rem] lg:w-[calc(100%-14.75rem-20.5rem)]' : 'lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)]'}
      ${toggleSideMenu ? 'lg:ml-[6rem]' : ' lg:ml-[14.75rem]'}  blurColor relative z-[2] min-h-dvh overflow-x-hidden px-3 pt-[4.75rem]  transition-all duration-300 ease-in-out md:px-8 md:pt-8 lg:z-[41] lg:ml-[14.75rem] `}
      >
      <div
        className={`mt-40-md-lg mx-auto w-full lg:max-w-[67.5rem] ${isAuthenticated ? '' : 'lg:pt-14'}`}
      >
        {
          showChat? 
          <ChatWindow mobile={true} />
          :
        
        children}
      </div>
    </div>
  );
}
