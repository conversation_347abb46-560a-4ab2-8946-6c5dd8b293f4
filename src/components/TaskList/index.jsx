'use client';

import React from 'react';
import Tabs from '@/components/Common/Tabs';
import Active from './Active';
import Claimed from './Claimed';
import TaskListIcon from '@/assets/icons/Task-List';

function TaskList() {
  const taskListTabs = [
    {
      label: 'Active',
      content: <Active />,
    },
    {
      label: 'Claimed',
      content: <Claimed />,
    },
  ];

  return (
    <section className="section-blur rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-maastrichtBlue-200 px-5 pb-10 pt-3.5">
      <div className="flex min-h-60 flex-wrap items-start  justify-start gap-6 max-sm:flex-col">
        <h3 className="mr-auto flex grow gap-4 text-xl font-normal leading-none text-white-1000 md:text-2xl max-sm:w-full">
          <TaskListIcon className="h-5 w-5 fill-white-1000" />
          <span>Tasklist</span>
        </h3>

        <Tabs tabs={taskListTabs} />
      </div>
    </section>
  );
}

export default TaskList;
