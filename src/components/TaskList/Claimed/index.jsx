import Link from 'next/link';
import React from 'react';
import Image from 'next/image';
import Accordion from '@/components/Common/Accordion';
import coinSC from '../../../assets/images/stock-images/coin-ac.png';
import coinGC from '../../../assets/images/stock-images/coin-gc.png';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import useUserClaimedTasks from '@/hooks/useUserClaimedTasks';
import { motion, AnimatePresence } from 'framer-motion';
import Lottie from 'lottie-react';
import NoData from '@/assets/json/no-data.json';
import NoDataFound from '@/components/Common/NoDataFound';

function Claimed() {
  const { userClaimedTasks: tasks, isUserTasksLoading } = useUserClaimedTasks();

  if (tasks?.length < 1) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ y: 10, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -10, opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex flex-col items-center justify-center text-center text-xl font-semibold text-white-700">
            <NoDataFound className="w-28" />
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }
  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: -10, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {tasks?.map((task) => (
          <Accordion
            key={task.taskId}
            items={{
              title: (
                <Link
                  href="#"
                  className="flex items-center justify-start gap-2.5"
                >
                  <div className="flex flex-wrap items-center gap-2.5 text-xl font-normal text-white-1000 max-lg:text-base uppertab:text-base">
                    {task.taskName}
                    <div className="flex items-center gap-2.5 rounded-[10px] bg-cetaceanBlue-1000 p-2.5">
                      <Image
                        src={coinSC}
                        width={10000}
                        height={10000}
                        className="h-5 w-5"
                        alt="Coin"
                      />
                      <span className="leading-none">{task.awardedAc}</span>
                    </div>
                    {/* <div className="flex items-center gap-2.5 rounded-[10px] bg-cetaceanBlue-1000 p-2.5">
                      <Image
                        src={coinGC}
                        width={10000}
                        height={10000}
                        className="h-5 w-5"
                        alt="Coin"
                      />
                      <span className="leading-none">{task.awardedGc}</span>
                    </div> */}
                  </div>
                </Link>
              ),
              content: (
                <div className="px-10 py-5">
                  <div className="mb-6">
                    <h5 className="text-xl font-bold text-white-1000 max-lg:text-base uppertab:text-base">
                      Mission Target
                    </h5>
                    <ol className="list-inside list-decimal text-xl text-steelTeal-1000 max-lg:text-base uppertab:text-base">
                      <li>{task.taskDescription}</li>
                    </ol>
                  </div>
                  <hr className="my-2 mb-4 h-0.5 border-none bg-oxfordBlue-900" />
                  <div className="mb-6">
                    <h5 className="text-xl font-bold text-white-1000 max-lg:text-base uppertab:text-base">
                      Mission Rewards
                    </h5>
                    <div className="flex items-center gap-2.5 py-3 text-xl font-normal leading-none text-white-1000 max-lg:text-base uppertab:text-base">
                      <Image
                        src={coinSC}
                        width={10000}
                        height={10000}
                        className="h-5 w-5"
                        alt="Coin"
                      />
                      <span>{task.awardedAc}</span>
                      {/* <Image
                        src={coinGC}
                        width={10000}
                        height={10000}
                        className="h-5 w-5"
                        alt="Coin"
                      />
                      <span>{task.awardedGc}</span> */}
                    </div>
                  </div>

                  <PrimaryButtonOutline
                    disabled={true}
                    className="text-white-1000"
                  >
                    Claimed
                  </PrimaryButtonOutline>
                </div>
              ),
            }}
          />
        ))}
      </motion.div>
    </AnimatePresence>
  );
}

export default Claimed;
