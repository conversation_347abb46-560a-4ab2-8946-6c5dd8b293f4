/* eslint-disable no-nested-ternary */

'use client';

import React, { useEffect, useRef } from 'react';
import {
  SendHorizontal,
  CircleArrowDown,
  MessageCircleMore,
} from 'lucide-react';
import GroupChatIcon from '@/assets/icons/GroupChatIcon';
import EmojiIcon from '@/assets/icons/Emoji';
import GIFIcon from '@/assets/icons/GIF';
import SettingIcon from '@/assets/icons/Setting';
import useAuthTab from '@/store/useAuthTab';
import useAuthStore from '@/store/useAuthStore';
import useGroupChatStore from '@/store/useGroupChatStore';
import useModalStore from '@/store/useModalStore';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { Grid } from '@giphy/react-components';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import CloseIcon from '@/assets/icons/CloseIcon';
import MainLoader from '@/components/Common/Loader/MainLoader';
import GroupChatConversion from '@/components/ChatWindow/GroupChatMessages';
import Auth from '@/components/Auth';
import IconButton from '@/components/Common/Button/IconButton';
import useChatWindow from '@/hooks/useChatWindow';

export default function Chat({ groupData, loading }) {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const { setSelectedTab } = useAuthTab((state) => state);
  const { isGroupChatOpen, isGroupPageOpen } = useGroupChatStore(
    (state) => state,
  );
  const { openModal } = useModalStore((state) => state);

  // Refs
  const gifPickerRef = useRef();
  const emojiPickerRef = useRef();

  const {
    message,
    gifMessage,
    setGifMessage,
    showEmojiPicker,
    setShowEmojiPicker,
    setMessage,
    showGifPicker,
    chatContainerRef,
    inputRef,
    handleKeyDown,
    handleSendMessage,
    handleInputChange,
    handleGifPickerToggle,
    handleEmojiPickerToggle,
    newMessagesCount,
    scrollToBottom,
    handleGifSelect,
    searchQuery,
    setSearchQuery,
    fetchGifs,
  } = useChatWindow();

  const handleSetting = () => {
    if (!isAuthenticated) {
      localStorage.setItem('activeTab', 0);
      setSelectedTab(0);
      openModal(<Auth />);
      return;
    }
    router.push('/user/prefrences');
  };

  // Click outside handlers
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        gifPickerRef.current &&
        !gifPickerRef.current.contains(event.target) &&
        showGifPicker
      ) {
        handleGifPickerToggle();
      }
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target) &&
        showEmojiPicker
      ) {
        handleEmojiPickerToggle();
      }
    };

    if (showGifPicker || showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showGifPicker, showEmojiPicker]);

  // Focus input when GIF is selected
  useEffect(() => {
    if (gifMessage && inputRef.current) {
      inputRef.current.focus();
    }
  }, [gifMessage]);

  return (
    <div className="flex h-full flex-col group-chat-section">
      {/* Group Chat Header Tab */}
      {/* <button
        type="button"
        className="font-nunito flex h-10 w-full items-center justify-center gap-1 border-2 border-solid border-transparent border-b-red-1000 bg-transparent px-2.5 py-2.5 text-[0.813rem] text-white-1000"
      >
        <IconButton>
          <GroupChatIcon className="fill-red-1000 transition-all duration-300" />
        </IconButton>
        <span>Group</span>
        <span className="text-white flex h-6 w-6 items-center justify-center rounded-full bg-maastrichtBlue-1000 text-xs">
          {groupData?.groupMembersCount || 0}
        </span>
        {newMessagesCount > 0 && (
          <div className="h-2 w-2 rounded-full bg-green-1000" />
        )}
      </button> */}

      {/* Chat Container */}
      <div className="relative flex min-h-0 shrink grow basis-[0%] flex-col group-chat-page">
        <div
          ref={chatContainerRef}
          className="scrollbar-none flex min-h-0 shrink grow basis-[0%] flex-col overflow-y-auto overflow-x-hidden page-chat"
        >
          {/* {loading && (
            <div className="flex h-full w-full items-center justify-center bg-cetaceanBlue-1000">
              <MainLoader className="w-32" />
            </div>
          )} */}

          <GroupChatConversion isBackNav />
        </div>

        {/* Emoji Picker */}
        {showEmojiPicker && (
          <div
            ref={emojiPickerRef}
            className="absolute bottom-28 right-0 xxs:right-auto"
          >
            <Picker
              autoFocus={false}
              data={data}
              onEmojiSelect={(emoji) => {
                setShowEmojiPicker(false);
                setShowEmojiPicker(false);
                      setMessage(message + emoji.native);
                      if (gifMessage) setGifMessage(null);
              }}
            />
          </div>
        )}

        {/* GIF Picker */}
        {showGifPicker && (
          <div
            className="absolute bottom-28 left-0 z-50 w-full"
            ref={gifPickerRef}
          >
            <div style={{ maxHeight: 300, overflowY: 'scroll' }}>
              <Grid
                key={searchQuery}
                fetchGifs={fetchGifs}
                width={300}
                columns={2}
                gutter={6}
                onGifClick={handleGifSelect}
              />
            </div>
          </div>
        )}

        {/* Chat Input Area */}
        {isGroupPageOpen && (
          <div className="relative bg-oxfordBlue-1000 px-[0.345rem] py-5 shadow-[0px_-4px_10px_var(--richBlack-1000)]">
            <div className="flex items-center justify-between rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-cetaceanBlue-1000 px-1 transition-all duration-100 hover:border-2 hover:border-primary-1000">
              {showGifPicker ? (
                <input
                  ref={inputRef}
                  className="hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                  placeholder="Search GIFs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{ opacity: gifMessage ? '0' : '1' }}
                />
              ) : gifMessage ? (
                <div className="mb-2 flex items-center">
                  <Image
                    src={gifMessage}
                    width={10000}
                    height={10000}
                    className="w-[5.875rem] max-w-full rounded-lg"
                    alt="Selected GIF"
                  />
                  <IconButton
                    onClick={() => setGifMessage(null)}
                    className="z-0 ml-2 h-6 w-6 min-w-6 cursor-pointer"
                  >
                    <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                  </IconButton>
                </div>
              ) : (
                <div className="ml-1 w-full">
                  <input
                    ref={inputRef}
                    className={`hover:bg-cetaceanBlue-400 h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                    placeholder="Type your message"
                    value={message}
                    readOnly={!isAuthenticated}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    style={{ opacity: gifMessage ? '0' : '1' }}
                  />
                </div>
              )}

              {isAuthenticated && (
                <SendHorizontal
                  onClick={handleSendMessage}
                  className="h-8 w-8 rounded-[0.313rem] bg-primary-1000 p-1"
                />
              )}
            </div>

            <div className="mt-3.5 flex items-center justify-between gap-4">
              <div className="flex items-center gap-[0.313rem] text-[0.813rem] font-normal leading-none text-white-1000">
                <span className="mb-1 inline-block h-[0.313rem] w-[0.313rem] rounded-full bg-green-1000" />
                <span>
                  Online:
                  {isAuthenticated ? groupData?.totalOnlineMembers : '-' || '-'}
                </span>
              </div>

              <div className="flex items-center justify-end gap-[0.313rem]">
                {/* <button
                  onClick={handleSetting}
                  type="button"
                  disabled={!isAuthenticated}
                  className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                >
                  <SettingIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                  Setting
                </button> */}
                <button
                  type="button"
                  onClick={handleGifPickerToggle}
                  disabled={!isAuthenticated}
                  className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000  ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                >
                  <GIFIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                  GIF
                </button>
                <button
                  onClick={handleEmojiPickerToggle}
                  type="button"
                  disabled={!isAuthenticated}
                  className={`group flex items-end justify-center gap-[0.313rem] px-[0.313rem] py-1 text-xs font-normal leading-none text-steelTeal-1000 transition-all duration-300 hover:text-white-1000 ${!isAuthenticated ? 'cursor-not-allowed' : ''}`}
                >
                  <EmojiIcon className="h-4 w-4 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                  Emoji
                </button>
              </div>
            </div>
          </div>
        )}

        {/* New Messages Notification */}
        {newMessagesCount > 0 && isGroupChatOpen && (
          <button
            type="button"
            onClick={() => scrollToBottom(true)}
            className="absolute bottom-32 right-2 flex items-center gap-2 rounded-xl bg-tiber-1000 p-2"
          >
            <MessageCircleMore className="text-white-1000 transition-all duration-300" />
            New Messages
            <CircleArrowDown className="text-white-1000 transition-all duration-300" />
          </button>
        )}
      </div>
    </div>
  );
}
