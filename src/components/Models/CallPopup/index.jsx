import DisconnectIcon from '@/assets/icons/DisconnectIcon';
import IncomingCallIcon from '@/assets/icons/IncomingCallIcon';
import IconButton from '@/components/Common/Button/IconButton';
import useCallModalStore from '@/store/useCallModalStore';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import { motion } from 'framer-motion';
import { Minimize, PhoneCall } from 'lucide-react';
import { useEffect, useRef } from 'react';
import Draggable from 'react-draggable';
import Timer from './component/Timer';

import ChatAvatar from '@/components/ChatWindow/ChatAvatar';
import Tooltip from '@/components/Common/Tooltip';
import usePrivateCall, { rtc } from '@/hooks/usePrivateCall';
import ToggleMuteButton from './component/ToggleMuteButton';

function CallPopup() {
  const { setIsMinimized, isMinimized } = useCallModalStore((state) => state);
  const { voiceCall, setVoiceCall } = useVoiceCallStore((state) => state);
  const positionRef = useRef({ x: 0, y: 0 });
  const {
    handleAcceptCall,
    handleDeclineCall,

    isCallActive,
    isCallAccepting,
  } = usePrivateCall();

  useEffect(() => {
    if (!rtc?.client) return;

    rtc?.client.on('user-published', async (user, mediaType) => {
      await rtc?.client.subscribe(user, mediaType);
      if (mediaType === 'audio') {
        rtc.remoteAudioTrack = user.audioTrack;
        rtc?.remoteAudioTrack.play();
      }
    });

    rtc?.client.on('user-unpublished', () => {
      if (rtc?.remoteAudioTrack) {
        rtc?.remoteAudioTrack.stop();
        rtc.remoteAudioTrack = null;
      }
    });

    return () => {
      rtc?.client.removeAllListeners();
    };
  }, []);
  if (!voiceCall) return;

  const MinimizedCall = () => (
    <div
      className="text-white fixed bottom-[62px]  right-4 z-50 flex cursor-pointer items-center rounded-full bg-gray-800 p-2 shadow-lg lg:bottom-4"
      onClick={() => setIsMinimized(false)}
    >
      <PhoneCall className="h-6 w-6 text-green-500" />
      <span className="ml-2">Call in Progress</span>
    </div>
  );
  const handleMinimized = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsMinimized(true);
  };
  const CallContent = () => {
    return (
      <div className="relative h-[320px] w-[223px] max-w-md rounded-lg bg-richBlack-1000 p-4  shadow-lg">
        <div className="drag-handle flex cursor-move justify-between">
          <div className="flex  items-center justify-between  p-2">
            <h3 className="text-white flex text-lg font-semibold">
              {isCallActive ? (
                'Call Connected'
              ) : (
                <>
                  Incoming Call
                  <span className="inline-block w-6 animate-dots overflow-hidden">
                    ...
                  </span>
                </>
              )}
            </h3>
          </div>
          <div className="flex justify-end gap-2 p-2">
            <Tooltip text={'Minimise'}>
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleMinimized(e);
                }}
                onTouchStart={handleMinimized}
                className="h-7 w-7"
              >
                <Minimize className="text-white h-5 w-5" />
              </IconButton>
            </Tooltip>

            {/* <IconButton onClick={(e) => { e.stopPropagation(); handleDeclineCall(); }} className="h-7 w-7">
              <X className="h-5 w-5 text-white" />
            </IconButton> */}
          </div>
        </div>

        <div className="border-t-4 border-indigo-500"></div>

        <div className="mt-[10px] flex flex-col items-center p-4 text-center">
          {/* <CustomImage
            src={voiceCall?.profileImage || profile_url}
            alt="Profile"
            width={90}
            height={90}
            className="h-24 w-24 rounded-full object-cover"
          /> */}
          <ChatAvatar
            profileImage={voiceCall?.profileImage}
            firstName={voiceCall?.firstName || ''}
            lastName={voiceCall?.lastName || ''}
            userName={voiceCall?.username}
            imageClassName="size-[90px] rounded-full object-cover"
            imageWidth={90}
            imageHeight={90}
            avatarSize={90}
          />
          <h4 className="text-white mt-2 text-xl font-medium">
            {voiceCall?.username}
          </h4>
          {isCallActive && (
            <p className="text-white mt-2 text-sm">
              Call Duration: <Timer isActive={isCallActive} />
            </p>
          )}
        </div>

        <div className="flex items-center justify-center gap-6 p-4">
          {!isCallActive && (
            <IconButton
              onClick={() => {
                isCallAccepting ? null : handleAcceptCall();
              }}
              className={` h-9 w-9 rounded-full bg-green-600  ${isCallAccepting ? 'animate-pulse' : 'hover:scale-125'} `}
            >
              <IncomingCallIcon className="text-white h-8 w-8" />
            </IconButton>
          )}

          <IconButton
            onClick={handleDeclineCall}
            className={` h-9 w-9 rotate-[135deg]  rounded-full bg-red-600 hover:scale-125  `}
          >
            <DisconnectIcon className="text-white h-9 w-9 " />
          </IconButton>
          {/* <IconButton onClick={toggleMicrophone} className="h-6 w-6">
            {mutedRef ? <MicOff className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5 text-white" />}
          </IconButton> */}
          <ToggleMuteButton rtc={rtc} />
        </div>
      </div>
    );
  };

  return (
    <>
      {isMinimized ? (
        <MinimizedCall />
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="bg-black absolute inset-0 z-50 flex h-full w-full items-center justify-center bg-opacity-50"
        >
          <Draggable
            handle=".drag-handle"
            bounds="parent"
            defaultPosition={{ x: 0, y: 0 }}
            onStop={(_, data) => {
              positionRef.current = { x: data.x, y: data.y };
            }}
          >
            <div className="m-4">
              <CallContent />
            </div>
          </Draggable>
        </motion.div>
      )}
    </>
  );
}

export default CallPopup;
