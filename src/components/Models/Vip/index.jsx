'use client';

import FaucetIcon from '@/assets/icons/Faucet';
import IconButton from '../../Common/Button/IconButton';
import CloseIcon from '@/assets/icons/CloseIcon';
import useModalStore from '@/store/useModalStore';
import Link from 'next/link';

function Vip() {
  const { closeModal } = useModalStore((state) => state);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <FaucetIcon className="h-5 w-5 fill-white-1000" />
              <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                Faucet ( Unlimited )
              </h3>
            </div>

            <IconButton className="h-6 w-6 min-w-6" onClick={closeModal}>
              <CloseIcon className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="p-20">
            <p className="text-center">
              <Link
                className="text-center underline"
                href="/vip-rules"
                onClick={() => closeModal()}
              >
                VIP Rules
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Vip;
