import useCallModalStore from '@/store/useCallModalStore';
import useCallStore from '@/store/useCallStore';
import { Mic, MicOff } from 'lucide-react';

const MinimizeCallPopup = () => {
  const { setIsMinimized } = useCallModalStore();
  const { toggleMuted, setToggleMuted } = useCallStore();

  return (
    <div
      className="fixed bottom-[62px]  lg:bottom-4 right-4 z-50 flex cursor-pointer items-center rounded-full bg-zinc-800 px-5 py-2 shadow-lg transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-400"
      onClick={() => setIsMinimized(false)}
      role="button"
      tabIndex={0}
      aria-label="Restore call window"
    >
      {/* <Mic className="h-5 w-5 text-green-400" /> */}
      {toggleMuted ? (
        <MicOff className="h-5 w-5 text-red-500" />
      ) : (
        <Mic className="text-white h-5 w-5" />
      )}
      <span className="text-white ml-3 select-none font-medium">
        Call in Progress
      </span>
    </div>
  );
};

export default MinimizeCallPopup;
