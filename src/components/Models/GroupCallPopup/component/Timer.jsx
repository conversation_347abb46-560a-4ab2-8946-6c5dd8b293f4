import useCallStore from '@/store/useCallStore';
import React, { useEffect, useState } from 'react';

const Timer = ({ startTime = 0, isActive = false }) => {
  const { callStartTime, setCallStartTime, callDuration, setCallDuration } = useCallStore();

  // 🟢 Compute elapsed time immediately on render (fixes delay)
  const getElapsedTime = () => {
    if (!callStartTime) return callDuration || startTime;
    return Math.floor((Date.now() - callStartTime) / 1000);
  };

  const [elapsedTime, setElapsedTime] = useState(getElapsedTime);

  useEffect(() => {
    if (!isActive) return;

    if (!callStartTime) {
      setCallStartTime(Date.now() - callDuration * 1000);
    }

    const timerInterval = setInterval(() => {
      const updatedTime = Math.floor((Date.now() - callStartTime) / 1000);
      setElapsedTime(updatedTime);
      setCallDuration(updatedTime);
    }, 1000);

    return () => clearInterval(timerInterval);
  }, [isActive, callStartTime, setCallStartTime]);

  // 🟢 Update instantly when the component re-shows
  useEffect(() => {
    setElapsedTime(getElapsedTime());
  }, [callStartTime, callDuration]);

  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const mins = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  return <span>{formatTime(elapsedTime)}</span>;
};

export default Timer;
