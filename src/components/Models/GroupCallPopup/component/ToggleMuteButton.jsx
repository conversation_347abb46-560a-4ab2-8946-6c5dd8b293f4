import IconButton from '@/components/Common/Button/IconButton';
import Tooltip from '@/components/Common/Tooltip';
import useGroupCall, { rtc } from '@/hooks/useGroupCall';
import useCallStore from '@/store/useCallStore';
import AgoraRTC from 'agora-rtc-sdk-ng';
import { Mic, MicOff } from 'lucide-react';
import { useEffect, useState } from 'react';

const ToggleMuteButton = () => {
  const { isMuted, setIsMuted,toggleMicrophone } = useGroupCall();

  //   const [isMuted, setIsMuted] = useState(false);
  const { toggleMuted, setToggleMuted } = useCallStore();
  // const requestMicrophoneAccess = async () => {
  //   try {
  //     const micTrack = await AgoraRTC.createMicrophoneAudioTrack();
  //     console.log('Microphone access granted');
  //     return micTrack;
  //   } catch (err) {
  //     console.error('Microphone access failed:', err);

  //     const originalError = err?.message || err?.originalError?.name || '';

  //     if (
  //       originalError.includes('NotAllowedError') ||
  //       originalError.includes('PERMISSION_DENIED')
  //     ) {
  //       alert(
  //         "Microphone access is blocked. Please allow it from your browser's settings.",
  //       );
  //     } else if (
  //       originalError.includes('NotFoundError') ||
  //       originalError.includes('NOT_FOUND')
  //     ) {
  //       alert('No microphone device found.');
  //     } else {
  //       alert('An unexpected error occurred while accessing the microphone.');
  //     }

  //     return null;
  //   }
  // };

  // const toggleMicrophone = async () => {
  //   console.log(
  //     '🚀 ~ toggleMicrophone ~ rtc.localAudioTrack:',
  //     rtc.localAudioTrack,
  //   );

  //   const enableMic = async () => {
  //     try {
  //       await rtc.localAudioTrack.setEnabled(true);
  //     } catch (err) {
  //       console.warn('Failed to re-enable mic, requesting access again...');
  //       const micTrack = await requestMicrophoneAccess();
  //       if (!micTrack) return;

  //       rtc.localAudioTrack = micTrack;
  //       await rtc.client.publish(micTrack);
  //     }
  //   };

  //   const disableMic = async () => {
  //     await rtc.localAudioTrack.setEnabled(false);
  //   };

  //   const updateMicState = (muted) => {
  //     setIsMuted(muted);
  //     setToggleMuted(muted);
  //   };

  //   if (rtc.localAudioTrack) {
  //     if (isMuted) {
  //       await enableMic();
  //       updateMicState(false);
  //     } else {
  //       await disableMic();
  //       updateMicState(true);
  //     }
  //   } else {
  //     const micTrack = await requestMicrophoneAccess();
  //     if (!micTrack) return;

  //     rtc.localAudioTrack = micTrack;
  //     await rtc.client.publish(micTrack);
  //     updateMicState(false);
  //   }
  // };

  useEffect(() => {
    setIsMuted(toggleMuted);
  }, []);
  return (
    <Tooltip text={isMuted ? 'Unmute' : 'mute'}>
      <IconButton onClick={toggleMicrophone} className="h-6 w-6">
        {isMuted ? (
          <MicOff className="h-5 w-5 text-red-500" />
        ) : (
          <Mic className="text-white h-5 w-5" />
        )}
      </IconButton>
    </Tooltip>
  );
};

export default ToggleMuteButton;
