'use client';

import React from 'react';
import { X, Shield, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import useModalStore from '@/store/useModalStore';

function KycVerificationModal() {
  const { closeModal } = useModalStore();
  const router = useRouter();

  const handleVerifyNow = () => {
    closeModal();
    router.push('/user/kyc');
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="relative w-full max-w-md my-auto bg-maastrichtBlue-1000 rounded-xl shadow-xl max-h-[calc(100vh-8rem)] overflow-y-auto no-scrollbar">
        {/* Header */}
        <div className="flex flex-shrink-0 items-center justify-between p-6 border-b-2 border-yellow-500/30">
          <div className="flex items-center gap-3">
            <Shield className="h-6 w-6 text-yellow-500" />
            <h2 className="text-xl font-bold text-white-1000">KYC Verification Required</h2>
          </div>
          <button
            onClick={closeModal}
            className="text-gray-400 hover:text-white-1000 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Icon and Message */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="h-8 w-8 text-yellow-500" />
            </div>
            <h3 className="text-lg font-semibold text-white-1000 mb-2">
              Verification Required
            </h3>
            <p className="text-gray-400 text-sm leading-relaxed">
              To purchase packages and access premium features, you need to complete your KYC verification first. 
              This helps us ensure a secure and compliant gaming environment.
            </p>
          </div>

          {/* Benefits */}
          <div className="mb-6 p-4 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border border-yellow-500/30 rounded-xl">
            <h4 className="text-white-1000 font-medium mb-3">After verification, you'll be able to:</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                Purchase coin packages
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                Access premium features
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                Enhanced account security
              </li>
            </ul>
          </div>

          {/* Action Button */}
          <button
            onClick={handleVerifyNow}
            className="w-full py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold rounded-xl hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 shadow-lg hover:shadow-yellow-500/25"
          >
            Verify Now
          </button>

          {/* Security Note */}
          <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-xl">
            <div className="flex items-start gap-2">
              <Shield className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
              <p className="text-blue-200 text-xs">
                Your personal information is encrypted and securely stored. We comply with all data protection regulations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default KycVerificationModal;
