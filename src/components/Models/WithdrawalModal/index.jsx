'use client';

import React, { useState } from 'react';
import { X, Loader2 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import { useUserProfileQuery } from '@/reactQuery/authQuery';
import { useWithdrawalRequestMutation } from '@/reactQuery/paymentQuery';
import { formatValueWithK } from '@/utils/helper';
import WalletIcon from '@/assets/icons/WalletIcon';
import SCicon from '@/assets/icons/SCicon';

function WithdrawalModal() {
  const { closeModal } = useModalStore();
  const { isAuthenticated, userDetails } = useAuthStore();

  const [withdrawalData, setWithdrawalData] = useState({
    amount: '',
    paymentMethod: 'Credit Card',
  });

  const [amountError, setAmountError] = useState('');
  const [redirectUrl, setRedirectUrl] = useState('');
  const { data: profileData, isLoading: profileLoading } = useUserProfileQuery({
    enabled: isAuthenticated,
  });

  const withdrawalMutation = useWithdrawalRequestMutation({
    onSuccess: (data) => {
      toast.dismiss('payment-init');
      const urlFromAPI = data?.data?.praxisCashier?.redirect_url;
      if (urlFromAPI) {
        toast.success('Redirecting to secure payment...', { duration: 2000 });
        setRedirectUrl(urlFromAPI);
      } else {
        resetFormAndClose();
      }
    },
    onError: (error) => {
      console.error('Withdrawal request failed:', error);
      const errorMessage = error.response?.data?.errors?.[0]?.description || 
                          error.response?.data?.message || 
                          error.message || 
                          'Withdrawal request failed. Please try again.';
      toast.error(errorMessage);
    },
  });

  const resetFormAndClose = () => {
    setWithdrawalData({
      amount: '',
      paymentMethod: 'Credit Card',
    });
    setRedirectUrl('');
    closeModal();
  };

  const handleInputChange = (field, value) => {
    setWithdrawalData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === 'amount') {
      const availableBalance = profileData?.userWallet?.wsc || 0;

      if (!value || value === '') {
        setAmountError('');
      } else if (parseFloat(value) <= 0) {
        setAmountError('Amount must be greater than 0');
      } else if (parseFloat(value) > availableBalance) {
        setAmountError('Insufficient balance');
      } else {
        setAmountError('');
      }
    }
  };

  const validateForm = () => {
    const { amount } = withdrawalData;
    const availableBalance = profileData?.userWallet?.wsc || 0;

    if (!amount || parseFloat(amount) <= 0) {
      setAmountError('Please enter a valid withdrawal amount');
      return false;
    }

    if (parseFloat(amount) > availableBalance) {
      setAmountError('Insufficient balance');
      return false;
    }

    setAmountError('');
    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    toast.loading('Initializing secure payment...', { id: 'payment-init' });
    const requestData = {
      amount: parseFloat(withdrawalData.amount),
      userId: +userDetails.userId,
      paymentMethod: withdrawalData.paymentMethod,
    };

    withdrawalMutation.mutate(requestData);
  };

  const availableBalance = profileData?.userWallet?.wsc || 0;
  const isLoading = profileLoading || withdrawalMutation.isLoading;

  if (redirectUrl !== '') {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 px-2 overflow-y-auto no-scrollbar">
      <div className="relative w-full max-w-md sm:max-w-2xl bg-maastrichtBlue-1000 rounded-lg sm:rounded-xl shadow-xl flex flex-col max-h-[calc(100vh-8rem)] sm:max-h-[75vh] my-auto overflow-hidden no-scrollbar">
    
        {/* Header */}
        <div className="flex items-center justify-between border-b border-borderColor-100 px-4 sm:px-6 py-4">
          <div className="flex items-center gap-3 sm:gap-4">
            <button
              onClick={() => {
                toast.dismiss();
                setRedirectUrl('');
              }}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
            >
              ←
            </button>
            <h2 className="text-lg sm:text-2xl font-bold text-white-1000">
              Complete Withdrawal
            </h2>
          </div>
          <button
            onClick={() => {
              toast.dismiss();
              resetFormAndClose();
            }}
            className="rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
          >
            <X className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
        </div>
    
        {/* Info */}
        <div className="text-center mt-4 px-4 sm:px-6">
          <p className="text-sm sm:text-base text-gray-400">
            Amount: {withdrawalData.amount} SC &nbsp;|&nbsp; Method: {withdrawalData.paymentMethod}
          </p>
        </div>
    
        {/* Iframe */}
        <div className="relative flex-1 bg-white rounded-b-lg sm:rounded-b-xl overflow-auto no-scrollbar px-4 sm:px-6 py-4">
          <div className="w-full h-full rounded-xl overflow-hidden">
            <iframe
              src={redirectUrl}
              title="Praxis Withdrawal"
              className="w-full h-[70vh] sm:h-[65vh] min-h-[400px] rounded-xl border-none"
            />
          </div>
        </div>
      </div>
    </div>
    
    );
    
    
    
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="relative w-full max-w-md my-auto bg-maastrichtBlue-1000 rounded-lg shadow-xl max-h-[calc(100vh-8rem)] overflow-y-auto no-scrollbar">
        {/* Header */}
        <div className="flex flex-shrink-0 items-center justify-between p-6 border-b-2 border-yellow-500/30">
          <div className="flex items-center gap-3">
            <WalletIcon className="h-6 w-6 fill-steelTeal-1000" />
            <h2 className="text-xl font-bold text-white-1000">Request Withdrawal</h2>
          </div>
          <button
            onClick={closeModal}
            className="text-gray-400 hover:text-white-1000 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Available Balance */}
          <div className="mb-6 p-4 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border-2 border-yellow-500/30 rounded-xl">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm mb-1">Available Balance</p>
                <div className="flex items-center gap-2">
                  <SCicon className="h-6 w-6" />
                  <span className="text-2xl font-bold text-primary-500">
                    {isLoading ? (
                      <Loader2 className="h-6 w-6 animate-spin" />
                    ) : (
                      formatValueWithK(availableBalance)
                    )}
                  </span>
                  <span className="text-gray-400">SC</span>
                </div>
              </div>
            </div>
          </div>

          {/* Withdrawal Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Amount Input */}
            <div>
              <label className="block text-white-1000 font-medium mb-2">
                Withdrawal Amount
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={withdrawalData.amount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  placeholder="Enter amount"
                  min="0"
                  max={availableBalance}
                  className="w-full p-3 bg-gradient-to-r from-richBlack-1000 to-maastrichtBlue-1000 border-2 border-borderColor-100 rounded-xl text-white-1000 focus:border-yellow-500 focus:from-yellow-500/5 focus:to-yellow-600/5 focus:outline-none transition-all duration-300 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  disabled={isLoading}
                />
                
              </div>

              {/* Error message */}
              {amountError && (
                <p className="text-red-400 text-sm mt-1">
                  {amountError}
                </p>
              )}

              {/* Balance info (only show when no error) */}
              {!amountError && (
                <p className="text-gray-400 text-sm mt-1">
                  Maximum: {formatValueWithK(availableBalance)} SC
                </p>
              )}
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-white-1000 font-medium mb-2">
                Payment Method
              </label>
              <div className="relative">
                <select
                  value={withdrawalData.paymentMethod}
                  onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                  className="w-full p-3 bg-gradient-to-r from-richBlack-1000 to-maastrichtBlue-1000 border-2 border-borderColor-100 rounded-xl text-white-1000 focus:border-yellow-500 focus:from-yellow-500/5 focus:to-yellow-600/5 focus:outline-none appearance-none transition-all duration-300"
                  disabled={isLoading}
                >
                  <option value="Credit Card">Credit Card</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg className="w-4 h-4 fill-gray-400" viewBox="0 0 20 20">
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || !!amountError || !withdrawalData.amount}
              className="w-full py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold rounded-xl hover:from-yellow-400 hover:to-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-yellow-500/25"
            >
              {withdrawalMutation.isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Processing...
                </>
              ) : (
                'Withdraw'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default WithdrawalModal;