import React, { useState, useEffect } from 'react';
import { X, Star, Crown, Zap, Gift, Loader2 } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import useAuthStore from '@/store/useAuthStore';
import { toast } from 'react-hot-toast';
import { usePaymentDepositMutation } from '@/reactQuery/paymentQuery';
import { useGetPackagesQuery } from '@/reactQuery/packagesQuery';
import KycVerificationModal from '../KycVerificationModal';
import { useRouter } from 'next/navigation';

const packagesData = [
  {
    id: 1,
    packageId: 1,
    amount: 9.99,
    name: 'Starter Pack',
    price: '$9.99',
    originalPrice: '$19.99',
    discount: '50% OFF',
    icon: Gift,
    color: 'from-blue-500 to-blue-600',
    features: [
      '100 Gold Coins',
      '50 Silver Coins',
      'Basic Support',
      '7 Days Access'
    ],
    popular: false
  },
  {
    id: 2,
    packageId: 2,
    amount: 24.99,
    name: 'Premium Pack',
    price: '$24.99',
    originalPrice: '$49.99',
    discount: '50% OFF',
    icon: Star,
    color: 'from-purple-500 to-purple-600',
    features: [
      '500 Gold Coins',
      '250 Silver Coins',
      'Priority Support',
      '30 Days Access',
    ],
    popular: true
  },
  {
    id: 3,
    packageId: 3,
    amount: 49.99,
    name: 'VIP Pack',
    price: '$49.99',
    originalPrice: '$99.99',
    discount: '50% OFF',
    icon: Crown,
    color: 'from-yellow-500 to-yellow-600',
    features: [
      '1000 Gold Coins',
      '500 Silver Coins',
      'VIP Support',
      '90 Days Access',
    ],
    popular: false
  },
  {
    id: 4,
    packageId: 4,
    amount: 99.99,
    name: 'Ultimate Pack',
    price: '$99.99',
    originalPrice: '$199.99',
    discount: '50% OFF',
    icon: Zap,
    color: 'from-red-500 to-red-600',
    features: [
      '2500 Gold Coins',
      '1250 Silver Coins',
      '24/7 VIP Support',
      '365 Days Access',
    ],
    popular: false
  }
];

function PackagesModal() {
  const { closeModal } = useModalStore();
  const { userDetails } = useAuthStore();
  const [loadingPackageId, setLoadingPackageId] = useState(null);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [showPraxisIframe, setShowPraxisIframe] = useState(false);
  const [praxisUrl, setPraxisUrl] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const router = useRouter();
  

  const {
    data: apiPackagesData,
    isLoading: packagesLoading,
    error: packagesError
  } = useGetPackagesQuery({
    onSuccess: (data) => {
      console.log('Packages API data received:', data);
    },
    onError: (error) => {
      console.error('Failed to fetch packages:', error);
    }
  });

  useEffect(() => {
    const handleBeforeUnload = (event) => {
      const isPaymentInProgress = loadingPackageId !== null || showPraxisIframe;

      if (isPaymentInProgress) {
        const message = 'Payment is in progress. Are you sure you want to leave? Your payment may be lost.';
        event.preventDefault();
        event.returnValue = message; 
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [loadingPackageId, showPraxisIframe]);



  // Praxis supported payment methods
  const paymentMethods = [
    {
      id: 'credit-card',
      name: 'Credit Card',
      icon: '💳',
      description: 'Secure payment with your card'
    }
  ];

  const paymentMutation = usePaymentDepositMutation({
    onSuccess: (data) => {
      console.log('Payment API response:', data);
      toast.dismiss('payment-init');

      // Check if we got a Praxis redirect URL
      if (data?.data?.praxisCashier?.redirect_url) {
        console.log('Opening Praxis payment iframe:', data.data.praxisCashier.redirect_url);
        toast.success('Redirecting to secure payment...', { duration: 2000 });
        setPraxisUrl(data.data.praxisCashier.redirect_url);
        setShowPraxisIframe(true);
        setShowPaymentMethods(false);
        setLoadingPackageId(null);
      } else {
        // Direct payment success (fallback)
        toast.success('Payment successful! Your coins have been added.');
        console.log('Payment successful:', data);
        setLoadingPackageId(null);
        closeModal();
      }
    },
    onError: (error) => {
      toast.dismiss('payment-init');
      toast.error(error.message || 'Payment failed. Please try again.');
      console.error('Payment error:', error);
      setLoadingPackageId(null);
    },
  });

  const handleBuyNow = (packageData) => {
    if (!userDetails?.userId) {
      toast.error('Please login to purchase packages');
      return;
    }

    const isProfileComplete =
      userDetails?.firstName &&
      userDetails?.lastName &&
      userDetails?.dateOfBirth &&
      userDetails?.phone &&
      userDetails?.addressLine_1 &&
      userDetails?.city &&
      userDetails?.state &&
      userDetails?.countryCode &&
      userDetails?.zipCode &&
      userDetails?.gender;

    if (!isProfileComplete) {
      toast.error('Please complete your profile to purchase packages.');
      closeModal();
      router.push('/user/profile');
      return;
    }

    if (userDetails?.kycStatus !== 'APPROVED') {
      closeModal();
      const { openModal } = useModalStore.getState();
      openModal(<KycVerificationModal />);
      return;
    }

    // Show payment method selection
    setSelectedPackage(packageData);
    setShowPaymentMethods(true);
  };

  const handlePaymentMethodSelect = (paymentMethod) => {
    if (!selectedPackage) return;

    // Show custom confirmation UI
    setSelectedPaymentMethod(paymentMethod);
    setShowConfirmation(true);
  };

  const handleConfirmPayment = () => {
    if (!selectedPackage || !selectedPaymentMethod) return;

    console.log(`✅ User confirmed payment with ${selectedPaymentMethod.name}`);

    // Hide confirmation and start processing
    setShowConfirmation(false);
    setLoadingPackageId(selectedPackage.id);
    setShowPaymentMethods(false);

    const paymentData = {
      amount: selectedPackage.amount,
      userId: +userDetails.userId,
      packageId: selectedPackage.packageId,
      paymentMethod: selectedPaymentMethod.name,
    };

    console.log('🚀 Processing payment with method:', selectedPaymentMethod.name, paymentData);
    toast.loading('Initializing secure payment...', { id: 'payment-init' });
    paymentMutation.mutate(paymentData);
  };

  const handleCancelPayment = () => {
    console.log('❌ Payment cancelled by user');
    // Dismiss any loading toasts
    toast.dismiss('payment-init');
    setShowConfirmation(false);
    setSelectedPaymentMethod(null);
    setLoadingPackageId(null);
  };

  const handleBackToPackages = () => {
    // Dismiss any loading toasts
    toast.dismiss('payment-init');
    setShowPaymentMethods(false);
    setSelectedPackage(null);
    setShowPraxisIframe(false);
    setPraxisUrl('');
    setShowConfirmation(false);
    setSelectedPaymentMethod(null);
    setLoadingPackageId(null);
  };

  const handlePraxisPaymentComplete = () => {
    console.log('Praxis payment completed');
    // Dismiss any loading toasts first
    toast.dismiss('payment-init');
    toast.success('Payment successful! Your coins have been added.');
    setShowPraxisIframe(false);
    setPraxisUrl('');
    setSelectedPackage(null);
    setLoadingPackageId(null);
    closeModal();
  };

  const handlePraxisPaymentCancel = () => {
    console.log('Praxis payment cancelled');
    // Dismiss any loading toasts
    toast.dismiss('payment-init');
    setShowPraxisIframe(false);
    setPraxisUrl('');
    setShowPaymentMethods(true); // Go back to payment methods
    setLoadingPackageId(null);
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-[55] flex justify-center bg-black bg-opacity-50 pt-[3.75rem] pb-16 md:items-center md:p-4"
    >
      <div className="relative h-full w-full max-w-6xl md:h-auto md:max-h-full">
        <div className="flex h-full flex-col rounded-lg bg-maastrichtBlue-1000 shadow-lg md:h-auto">
          {/* Header */}
          <div className="flex flex-shrink-0 items-center justify-between border-b border-borderColor-100 p-6">
            <div className="flex items-center gap-4">
              {(showPaymentMethods || showPraxisIframe || showConfirmation) && (
                <button
                  onClick={showConfirmation ? handleCancelPayment : handleBackToPackages}
                  className="rounded-lg p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
                >
                  ←
                </button>
              )}
              <h2 className="text-2xl font-bold text-white-1000">
                {showPraxisIframe
                  ? 'Complete Payment'
                  : showConfirmation
                    ? 'Confirm Payment'
                    : showPaymentMethods
                      ? 'Select Payment Method'
                      : 'Choose Your Package'
                }
              </h2>
            </div>
            <button
              type="button"
              onClick={() => {
                toast.dismiss('payment-init'); // Dismiss any loading toasts
                closeModal();
              }}
              className="rounded-lg p-2 text-gray-400 hover:bg-gray-700 hover:text-white"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="overflow-y-auto p-6 no-scrollbar">
            {showConfirmation ? (
              /* Payment Confirmation UI */
              <div className="max-w-2xl mx-auto">
                <div className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border-2 border-yellow-500/30 rounded-xl p-8">
                  <div className="text-center mb-6">
                    <div className="text-4xl mb-4">🔒</div>
                    <h3 className="text-2xl font-bold text-white-1000 mb-2">
                      Confirm Your Payment
                    </h3>
                    <p className="text-gray-400">
                      Please review your payment details before proceeding
                    </p>
                  </div>

                  <div className="bg-richBlack-1000 rounded-lg p-6 mb-6 space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Package:</span>
                      <span className="text-white-1000 font-bold">{selectedPackage?.name}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Amount:</span>
                      <span className="text-white-1000 font-bold text-xl">${selectedPackage?.amount}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Payment Method:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-xl">{selectedPaymentMethod?.icon}</span>
                        <span className="text-white-1000 font-bold">{selectedPaymentMethod?.name}</span>
                      </div>
                    </div>
                    <div className="border-t border-borderColor-100 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">Total:</span>
                        <span className="text-yellow-500 font-bold text-2xl">${selectedPackage?.amount}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <div className="text-blue-400 text-xl">ℹ️</div>
                      <div className="text-sm text-blue-200">
                        <strong>Secure Payment:</strong> You will be redirected to our secure payment processor.
                        Your payment information is encrypted and protected.
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4">
                    <button
                      onClick={handleCancelPayment}
                      className="flex-1 min-w-[120px] py-3 px-4 sm:px-6 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-bold text-sm sm:text-base"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleConfirmPayment}
                      className="flex-1 min-w-[140px] py-3 px-4 sm:px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black rounded-lg hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 font-bold hover:scale-105 text-sm sm:text-base"
                    >
                      <span className="block sm:inline">Confirm & Pay</span>
                      <span className="block sm:inline sm:ml-1">${selectedPackage?.amount}</span>
                    </button>
                  </div>
                </div>
              </div>
            ) : showPraxisIframe ? (
              /* Praxis Payment Iframe */
              <div className="max-w-4xl mx-auto">
                <div className="mb-4 text-center">
                  <h3 className="text-xl font-bold text-white-1000 mb-2">
                    Complete Your Payment
                  </h3>
                  <p className="text-gray-400">
                    {selectedPackage?.name} - ${selectedPackage?.amount}
                  </p>
                </div>

                <div className="relative bg-white rounded-lg overflow-hidden" style={{ height: '600px' }}>
                  <iframe
                    src={praxisUrl}
                    width="100%"
                    height="100%"
                    style={{ border: 'none' }}
                    title="Praxis Payment"
                    className="w-full h-full"
                    onLoad={() => console.log('Praxis iframe loaded')}
                  />
                </div>

                {/* <div className="mt-4 flex justify-center gap-4">
                  <button
                    onClick={handlePraxisPaymentCancel}
                    className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    Cancel Payment
                  </button>
                  <button
                    onClick={handlePraxisPaymentComplete}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Payment Complete
                  </button>
                </div> */}
              </div>
            ) : showPaymentMethods ? (
              /* Payment Methods Selection */
              <div className="max-w-3xl mx-auto">
                {/* Header Section */}
                <div className="text-center mb-8">
                

                  <h3 className="text-2xl font-bold text-white-1000 mb-2">
                    {selectedPackage?.name}
                  </h3>
                  <div className="flex items-center justify-center gap-2 mb-3">
                    <span className="text-gray-400">Total Amount:</span>
                    <span className="text-3xl font-bold text-yellow-500">${selectedPackage?.amount}</span>
                  </div>
                  <p className="text-gray-400 text-sm">
                    Select your preferred payment method to continue
                  </p>
                </div>

                {/* Payment Methods */}
                <div className="space-y-4">
                  {paymentMethods.map((method) => {
                    const isLoading = loadingPackageId === selectedPackage?.id;
                    return (
                      <div
                        key={method.id}
                        className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 ${
                          isLoading
                            ? 'border-yellow-500 bg-gradient-to-r from-yellow-500/20 to-yellow-600/20'
                            : 'border-borderColor-100 bg-gradient-to-r from-richBlack-1000 to-maastrichtBlue-1000 hover:border-yellow-500 hover:from-yellow-500/5 hover:to-yellow-600/5'
                        }`}
                      >
                        {/* Animated background effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>

                        <button
                          onClick={() => handlePaymentMethodSelect(method)}
                          disabled={isLoading}
                          className="relative w-full p-6 text-left transition-all duration-300 hover:scale-[1.01] active:scale-[0.99] disabled:cursor-not-allowed"
                        >
                          <div className="flex flex-wrap items-center gap-6">
                            {/* Payment Icon */}
                            <div className={`w-16 h-16 rounded-xl flex items-center justify-center text-3xl transition-all duration-300 ${
                              isLoading
                                ? 'bg-yellow-500/20 border-2 border-yellow-500/50'
                                : 'bg-gradient-to-br from-blue-500/20 to-purple-500/20 border-2 border-blue-500/30 hover:border-yellow-500/50'
                            }`}>
                              {isLoading ? (
                                <Loader2 className="h-8 w-8 animate-spin text-yellow-500" />
                              ) : (
                                method.icon
                              )}
                            </div>

                            {/* Payment Details */}
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h4 className="text-xl font-bold text-white-1000">
                                  {method.name}
                                </h4>
                                {isLoading && (
                                  <span className="inline-flex items-center gap-1 bg-yellow-500 text-black px-3 py-1 rounded-full text-xs font-bold">
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                    Processing...
                                  </span>
                                )}
                                {!isLoading && (
                                  <span className="inline-flex items-center gap-1 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-xs font-semibold border border-green-500/30">
                                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                    Secure
                                  </span>
                                )}
                              </div>

                              <p className="text-gray-400 text-sm mb-3">
                                {isLoading ? 'Initializing secure payment gateway...' : method.description}
                              </p>

                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                  <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                                  <span>Instant Processing</span>
                                </div>
                              </div>
                            </div>

                            {/* Action Arrow */}
                            <div className="flex flex-col items-center gap-2">
                              {!isLoading && (
                                <>
                                  <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center text-black font-bold shadow-lg hover:shadow-yellow-500/25 transition-all duration-300">
                                    →
                                  </div>
                                  <span className="text-xs text-gray-400 font-medium">Click to Pay</span>
                                </>
                              )}
                            </div>
                          </div>
                        </button>
                      </div>
                    );
                  })}
                </div>

                {/* Security Notice */}
                <div className="mt-8 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-blue-400 text-sm">🔒</span>
                    </div>
                    <div>
                      <h5 className="text-blue-200 font-semibold text-sm mb-1">Secure Payment Guarantee</h5>
                      <p className="text-blue-300/80 text-xs leading-relaxed">
                        Your payment information is encrypted using industry-standard SSL technology.
                        We never store your card details and all transactions are processed through our secure payment partners.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* Packages Grid */
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
              {packagesData.map((pkg) => {
                const IconComponent = pkg.icon;
                return (
                  <div
                    key={pkg.id}
                    className={`relative rounded-xl border-2 p-6 transition-all duration-300 hover:scale-105 ${
                      pkg.popular
                        ? 'border-yellow-500 bg-gradient-to-br from-yellow-500/10 to-yellow-600/10'
                        : 'border-borderColor-100 bg-richBlack-1000'
                    }`}
                  >
                    {/* Popular Badge */}
                    {pkg.popular && (
                      <div className="absolute -top-3 left-1/2 -translate-x-1/2 rounded-full bg-yellow-500 px-4 py-1 text-xs font-bold text-black">
                        MOST POPULAR
                      </div>
                    )}

                    {/* Package Icon */}
                    <div className={`mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r ${pkg.color}`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>

                    {/* Package Name */}
                    <h3 className="mb-2 text-xl font-bold text-white-1000">
                      {pkg.name}
                    </h3>

                    {/* Discount Badge */}
                    <div className="mb-3 inline-block rounded-md bg-green-500 px-2 py-1 text-xs font-bold text-white">
                      {pkg.discount}
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-baseline gap-2">
                        <span className="text-3xl font-bold text-white-1000">
                          {pkg.price}
                        </span>
                        <span className="text-lg text-gray-400 line-through">
                          {pkg.originalPrice}
                        </span>
                      </div>
                    </div>

                    {/* Features */}
                    <ul className="mb-6 space-y-2">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-300">
                          <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Buy Button */}
                    <button
                      onClick={() => handleBuyNow(pkg)}
                      disabled={loadingPackageId}
                      className={`w-full rounded-lg py-3 font-bold text-white transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 ${
                        pkg.popular
                          ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700'
                          : `bg-gradient-to-r ${pkg.color} hover:opacity-90`
                      }`}
                    >
                      {loadingPackageId === pkg.id ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : (
                        'Buy Now'
                      )}
                    </button>
                  </div>
                );
              })}
              </div>
            )}


          </div>
        </div>
      </div>
    </div>
  );
}

export default PackagesModal;
