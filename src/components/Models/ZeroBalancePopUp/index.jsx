import IconButton from "@/components/Common/Button/IconButton";
import PrimaryButton from "@/components/Common/Button/PrimaryButton";
import useModalStore from "@/store/useModalStore";
import { X } from 'lucide-react';
const ZeroBalancePopUp = ({ message }) => {
    const { closeModal } = useModalStore((state) => state);
    return (
        <>
            <>
                <div
                    tabIndex="-1"
                    aria-hidden="true"
                    className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
                >
                    <div className="relative w-full max-w-xl p-4">
                        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
                            <div className="flex items-center justify-between p-4">
                                <div className="flex items-center gap-3">

                                    <h3 className="text-white mt-1 text-lg font-semibold leading-none tracking-wide">
                                        User Info
                                    </h3>
                                </div>

                                <IconButton
                                    onClick={() => closeModal()}
                                    className="h-6 w-6 min-w-6"
                                >
                                    <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                                </IconButton>
                            </div>
                            {/* {loading ? (
              <div className="text-white p-4">Loading...</div>
            ) : ( */}
                            <div className="p-4">
                                <div className="mb-4 flex items-center justify-between gap-4">
                                    <div className="flex items-center gap-2">
                                        <div className="relative">
                                            {/* <div className="bg-white p-6 rounded-lg text-center shadow-lg"> */}
                                            <h2 className="text-lg font-semibold">{message}</h2>
                                            <div className="mt-6">
                                                <PrimaryButton type="submit">
                                                    Deposit Now
                                                </PrimaryButton>
                                            </div>
                                            {/* </div> */}
                                        </div>
                                    </div></div></div>
                            {/* )} */}
                        </div></div></div>
            </>

        </>
    );
};

export default ZeroBalancePopUp;
