'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { UserPlus, Handshake } from 'lucide-react'; // Join group icon
import {
  useGetFriendsListQuery,
  useGroupJoinRequestMutation,
  useJoinedGroupMutation,
} from '@/reactQuery/chatWindowQuery';
import { toast } from 'react-hot-toast';
import Tooltip from '@/components/Common/Tooltip';
import IconButton from '@/components/Common/Button/IconButton';
import { X, UserRoundMinus } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import profile_url from '@/assets/images/svg-images/profile-icon.svg';
import { useQueryClient } from '@tanstack/react-query';

const AddFriendsFromSearch = ({ groupId, groupMembers, groupAdminId, groupType}) => {
  const [search, setSearch] = useState('');
  const [selectedMembers, setSelectedMembers] = useState([]);

  const { data: friendsList } = useGetFriendsListQuery({
    enabled: !!search,
    params: { search },
  });
  const { closeModal, clearModals } = useModalStore();
  const queryClient = useQueryClient();
  const { mutate: updateJoinedGroup, isPending } = useJoinedGroupMutation({
    onSuccess: (response) => {
      // toast.success('Successfully joined the group!');
      queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
      closeModal();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });
  const { mutate: sendGroupJoinRequest, isPending: isRequestPending } =
    useGroupJoinRequestMutation({
      onSuccess: (response) => {
        // toast.success('Successfully joined the group!');
        // queryClient.invalidateQueries(['GET_GROUP_LIST_QUERY']);
        toast.success(response?.data?.message);
        closeModal();
      },
      onError: (error) => {
        const message =
          error.response?.data?.errors?.[0]?.description ||
          'Something went wrong';
        toast.error(message);
      },
    });
  const handleJoinedGroup = () => {
    const payload = {
      groupId,
      action: 'add',
      members: selectedMembers,
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully added to the group!');
      },
    });
  };

  const handleMemberSelect = (userId) => {
    setSelectedMembers((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId); // If the user is already selected, remove them
      } else {
        return [...prev, userId]; // Otherwise, add them to the selection
      }
    });
  };

  const handleRemoveMember = (userId) => {
    const payload = {
      groupId,
      action: 'remove',
      members: [userId],
    };

    updateJoinedGroup(payload, {
      onSuccess: () => {
        toast.success('Successfully removed from the group!');
      },
    });
  };

  const handleSendRequest = (userId) => {
    const payload = {
      groupId,

      userId: userId,
    };
    sendGroupJoinRequest(payload);
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <h3 className="text-white mt-1 text-lg font-semibold">
              Add and Remove Member in Group
            </h3>
            <IconButton
              onClick={() => closeModal()}
              className="h-6 w-6 min-w-6"
            >
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="px-2 py-1">
            <div className="mb-1 flex items-center gap-2 bg-maastrichtBlue-1000 px-[0.625rem] py-1">
              <input
                className="h-10 w-full resize-none rounded-[0.625rem] bg-cetaceanBlue-1000 px-[0.625rem] py-2 placeholder:text-steelTeal-1000"
                placeholder="Search friends"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>

            {/* Selected Members Section */}
            {selectedMembers.length > 0 && (
              <div className="mb-4 px-[0.625rem]">
                <h4 className="text-white text-sm font-medium mb-2">
                  Selected Members ({selectedMembers.length})
                </h4>
                <div className="max-h-[120px] overflow-y-auto bg-cetaceanBlue-1000 rounded-[0.625rem] p-2">
                  <div className="flex flex-col gap-2">
                    {selectedMembers.map((userId) => {
                      const selectedFriend = friendsList?.rows?.find(
                        (friend) => friend.relationUser?.userId === userId
                      );
                      if (!selectedFriend) return null;

                      const profile_img = selectedFriend?.relationUser?.profileImage || profile_url;

                      return (
                        <div
                          key={userId}
                          className="flex items-center justify-between gap-2 rounded-lg border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                        >
                          <div className="flex items-center gap-2">
                            <div className="relative h-8 w-8 min-w-8 rounded-full border border-oxfordBlue-1000">
                              <Image
                                src={profile_img}
                                width={32}
                                height={32}
                                className="h-full w-full rounded-full object-cover"
                                alt="Profile"
                              />
                            </div>
                            <span className="text-white text-sm">
                              {selectedFriend.relationUser?.username}
                            </span>
                          </div>
                          <div
                            className="cursor-pointer"
                            onClick={() => handleMemberSelect(userId)}
                          >
                            <X className="h-4 w-4 text-steelTeal-1000 hover:text-white transition-colors" />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            <section className="max-h-[218px] overflow-y-auto pt-1">
              <div className="flex flex-col gap-2">
                {search && friendsList?.rows?.length > 0 ? (
                  friendsList.rows
                    .filter((friend) => friend.relationUser?.userId !== groupAdminId)
                    .map((friend) => {
                    const profile_img =
                      friend?.relationUser?.profileImage || profile_url;
                    const isMember = groupMembers.some(
                      (member) => member.userId === friend.relationUser?.userId,
                    );
                    const isSelected = selectedMembers.includes(
                      friend.relationUser?.userId,
                    ); // Check if this user is selected

                    return (
                      <div
                        key={friend.relationUser?.userId}
                        className="flex items-center justify-between gap-[0.625rem] rounded-xl border border-oxfordBlue-1000 bg-maastrichtBlue-1000 p-2"
                      >
                        <div className="flex flex-row gap-2.5">
                          <div className="relative h-12 w-12 min-w-12 cursor-pointer rounded-full border-2 border-oxfordBlue-1000">
                            <Image
                              src={profile_img}
                              width={10000}
                              height={10000}
                              className="h-full w-full max-w-full rounded-full object-cover object-center"
                              alt="Profile"
                            />
                          </div>
                          <div className="mt-4 flex justify-center">
                            {friend.relationUser?.username}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {isMember ? (
                            <div
                              className="cursor-pointer"
                              onClick={() =>
                                handleRemoveMember(friend.relationUser?.userId)
                              }
                            >
                              <UserRoundMinus className="text-white h-6 w-6" />
                            </div>
                          ) : isSelected ? (
                            <div
                              className="cursor-pointer"
                              onClick={() =>
                                handleMemberSelect(friend.relationUser?.userId)
                              }
                            >
                              <UserRoundMinus className="text-white h-6 w-6" />
                            </div>
                          ) : (
                            <Tooltip text="Add to group" position="left">
                              <UserPlus
                                className="text-white h-6 w-6 cursor-pointer"
                                onClick={() =>
                                  handleMemberSelect(
                                    friend.relationUser?.userId,
                                  )
                                }
                              />
                            </Tooltip>
                          )}
                          {!isMember && groupType!=='public' && (
                            <div
                              className="cursor-pointer"
                              onClick={() =>
                                handleSendRequest(friend.relationUser?.userId)
                              }
                              disabled={true}
                            >
                              <Tooltip text="Request to join" position="left">
                                <Handshake className="text-white h-6 w-6" />
                              </Tooltip>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="mt-10 flex justify-center">No Friends</div>
                )}
              </div>
            </section>
            <button
              onClick={handleJoinedGroup}
              disabled={selectedMembers.length === 0 || isPending}
              className="text-white mt-4 w-full rounded-lg bg-steelTeal-1000 p-2 disabled:opacity-50"
            >
              {isPending ? 'Joining...' : 'Join Group'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddFriendsFromSearch;
