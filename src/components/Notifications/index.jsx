'use client';
import useChatWindow from '@/hooks/useChatWindow';
import {
  useNotificationsQueryInfinite,
  useUpdateNotificationMutation,
} from '@/reactQuery/generalQueries';
import useAuthStore from '@/store/useAuthStore';
import { formatDateTime } from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useRef } from 'react';
import MainLoader from '../Common/Loader/MainLoader';

const NotificationsPage = () => {
  const { isAuthenticated } = useAuthStore((state) => state);
  const observerRef = useRef(null);
  const { setSection } = useChatWindow();
  const queryClient = useQueryClient();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, status } =
    useNotificationsQueryInfinite({
      enabled: !!isAuthenticated,
    });

  const lastGroupElementRef = useCallback(
    (node) => {
      if (observerRef.current) observerRef.current.disconnect();

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        },
        { rootMargin: '200px' },
      );

      if (node) observerRef.current.observe(node);
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage],
  );
  const updateNotification = useUpdateNotificationMutation({
    onSuccess: (response) => {
      if (response?.data?.success) {
        queryClient.resetQueries({
          queryKey: ['GET_NOTIFICATIONS_LIST_QUERY'],
          exact: true,
          // Optionally: refetchActive: true, refetchInactive: true
        });
      }
      // queryClient.invalidateQueries(['notificationsDetails']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
    },
  });
  const handleNotificationClick = (data) => {
    const { referenceType } = data;
    if (referenceType == 'FRIEND_REQUEST') {
      setSection('Friends');
    } else if (referenceType == 'GROUP_CHAT' || referenceType == 'GROUP_JOIN') {
      setSection('GroupChat');
    } else if (referenceType == 'PRIVATE_CHAT') {
      setSection('PrivateChat');
    }
    // setShowNotificationPopup(false);
    updateNotification.mutate({ notificationIds: [data?.notificationId] });
  };
  return (
    <div>
      <section className="mb-10 rounded-lg shadow-container  sm:mt-10 sm:p-4">
        <div className="mb-6 flex items-center justify-between gap-4">
          <h6 className="text-white text-xl font-bold">Notifications</h6>
        </div>
        {status == 'pending' ? (
          <section className="mb-10 flex h-[50dvh] items-center  justify-center rounded-lg p-4 shadow-container">
            <div className="text-white text-center">
              <MainLoader className="w-32" />
            </div>
          </section>
        ) : data?.notifications?.length == 0 ? (
          <section className="mb-10 flex h-[50dvh] items-center  justify-center rounded-lg p-4 shadow-container  ">
            <div className="text-gray-400 text-center">
              <p>No notifications found</p>
            </div>
          </section>
        ) : (
          <div className="flex flex-col gap-2">
            {data?.notifications?.map((notification, index) => {
              const isLast = index === data.notifications.length - 1;
              return (
                <div
                  key={notification?.id ?? index}
                  ref={isLast ? lastGroupElementRef : null}
                  className="cursor-pointer rounded bg-[#80808024] p-2"
                  onClick={() => handleNotificationClick(notification)}
                >
                  <p>{notification?.message}</p>
                  <p>{formatDateTime(notification?.createdAt)}</p>
                </div>
              );
            })}

            {isFetchingNextPage && (
              <div className="text-white mt-4 text-center">Loading more...</div>
            )}
          </div>
        )}
      </section>
    </div>
  );
};

export default NotificationsPage;
