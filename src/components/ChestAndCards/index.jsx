import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import TreasureIcon from '@/assets/icons/Treasure';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import Chest from './Chest';
import Cards from './Cards';
import useChestAndCartStore from '@/store/useChestAndCartStore';

function ChestAndCardModal() {
  const { clearModals } = useModalStore((state) => state);
  const { activeTab, setActiveTab } = useChestAndCartStore((state) => state);

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center"
    >
      <div className="relative max-h-[90vh] w-full max-w-xl overflow-y-auto">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg ">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <TreasureIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              <h3 className="mt-1 cursor-pointer text-lg font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                Cards
              </h3>
            </div>
            <div className="flex items-center gap-2">
              <IconButton
                onClick={() => clearModals()}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="p-4">
            <div className="mb-4 flex items-center justify-center">
              <div className="flex gap-2 rounded-full bg-cetaceanBlue-1000 p-1">
                <button
                  type="button"
                  className={`flex items-center justify-center  rounded-full px-5 py-3 font-bold  ${activeTab === 'chest' ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
                  onClick={() => setActiveTab('chest')}
                >
                  Chest
                </button>
                <button
                  type="button"
                  className={`flex items-center justify-center  rounded-full px-5 py-3 font-bold  ${activeTab === 'cards' ? 'bg-oxfordBlue-1000 text-white-1000' : 'text-steelTeal-1000'}`}
                  onClick={() => setActiveTab('cards')}
                >
                  Card
                </button>
              </div>
            </div>
            {activeTab === 'chest' ? <Chest /> : <Cards />}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChestAndCardModal;
