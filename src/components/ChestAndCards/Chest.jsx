import React, { useState, useEffect } from 'react';
import chest from '@/assets/images/stock-images/chest.webp';
import Image from 'next/image';
import { Forward } from 'lucide-react';
import useClaim from '@/hooks/useClaim';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import HistoryModal from './HistoryModal';
import { useCmsPageDetailsQuery } from '@/reactQuery/generalQueries';
import MainLoader from '../Common/Loader/MainLoader';

function Chest() {
  const { isAuthenticated } = useAuthStore((state) => state);
  const { userDetails, userWallet } = useAuthStore((state) => state);
  const { openModal } = useModalStore((state) => state);

  const [remainingChests, setRemainingChests] = useState();

  const { claimChest, claimMultipleChests } = useClaim(setRemainingChests);

  useEffect(() => {
    setRemainingChests(userDetails?.userMeta?.totalUnopenedChestCount);
  }, [userDetails?.userMeta?.totalUnopenedChestCount]);

  const {
    data: cmsPageData,
    isLoading,
    error,
  } = useCmsPageDetailsQuery({
    enabled: isAuthenticated,
    pageSlug: 'game-rules-chest',
  });

  return (
    <div className="text-center">
      <div className="flex items-start justify-between">
        <button
          onClick={() => openModal(<HistoryModal />)}
          className="text-1.5xl capitalize text-white-1000 underline"
        >
          History
        </button>
      </div>
      <div className="w-18 my-4 flex items-center justify-center">
        <Image src={chest} alt="chest" height={125} />
      </div>
      <p className="text-white mb-4">
        The treasure chest you have: {remainingChests}
      </p>
      <div className="mb-4 flex justify-center">
        <button
          className="text-gray mr-4 w-1/2 rounded-lg bg-primary-1000 px-12 py-6"
          onClick={() => claimChest()}
        >
          Single Open {userDetails?.coinToWithdrawOneChest} SC
        </button>
        <button
          className="text-white w-1/2 rounded-lg bg-gray-700 px-6 py-3"
          onClick={() =>
            claimMultipleChests(
              userDetails?.coinToWithdrawOneChest * remainingChests,
            )
          }
        >
          All Open{' '}
          {userDetails?.coinToWithdrawOneChest &&
            remainingChests &&
            userDetails?.coinToWithdrawOneChest * remainingChests}
          SC
        </button>
      </div>
      <div className="text-white border-spacing-1 rounded-lg border border-dashed border-white-1000 p-4 text-left">
        <h4 className="mb-2 font-bold">Game Rule:</h4>
        {isLoading ? (
          <MainLoader className={'w-20'} />
        ) : (
          <div
            className="content-para"
            dangerouslySetInnerHTML={{ __html: cmsPageData?.content?.EN }}
          />
        )}
        {/* <ol className="list-inside list-decimal text-base font-normal text-steelTeal-1000">
          <li className="my-1.5">
            What's in the treasure chest
            <span className="block">
              Different amount of AC, GC, Chest, Letter Cards (Share Bonus Pool
              after Spell), Maybe NFT in future
            </span>
          </li>
          <li className="my-1.5">
            Requirements to open the treasure chest
            <ol className="list-inside list-[lower-alpha]">
              <li>Email already set</li>
              <li>
                Level 4 or higher (Buy gold coins and wager, easy to reach)
              </li>
            </ol>
          </li>
          <li className="my-1.5">
            How to get the treasure chest
            <ol className="list-inside list-[lower-alpha]">
              <li>Daily login, regular users can get 2, vip 3</li>
              <li>Spend $10 on the store, get 1 treasure chest</li>
              <li>Be active in the chat room</li>
              <li>Share to get 1 every day</li>
            </ol>
          </li>
          <li className="my-1.5">
            Expiration time of treasure chest
            <span className="block">
              The treasure chest is valid for 2 weeks, please open it in time to
              avoid expiration can not be used.
            </span>
          </li>
          <li className="my-1.5">
            Maximum number of valid treasure chests limit
            <span className="block">
              You can have up to 50 treasure chests at the same time, so please
              open them in time!
            </span>
          </li>
        </ol> */}
      </div>
    </div>
  );
}

export default Chest;
