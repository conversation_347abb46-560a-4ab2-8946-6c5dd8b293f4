import React from 'react';
import { X } from 'lucide-react';

import IconButton from '../Common/Button/IconButton';
import TreasureIcon from '@/assets/icons/Treasure';
import useModalStore from '@/store/useModalStore';
import Image from 'next/image';
import useChestAndCartStore from '@/store/useChestAndCartStore';

function ClaimResult({ claimResult }) {
  const { setActiveTab } = useChestAndCartStore((state) => state);
  const { closeModal } = useModalStore((state) => state);

  const handleViewClick = () => {
    setActiveTab('cards');
    closeModal();
  };

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative w-full max-w-xl p-4">
        <div className="rounded-lg bg-maastrichtBlue-1000 shadow-lg">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-3">
              <TreasureIcon className="fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              <h3 className="mt-1 cursor-pointer text-lg font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
                Congratulations
              </h3>
            </div>
            <div className="flex items-center gap-2">
              <IconButton
                onClick={() => closeModal()}
                className="h-6 w-6 min-w-6"
              >
                <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
              </IconButton>
            </div>
          </div>
          <div className="p-4 pb-10 text-center">
            <div className="flex items-center justify-center">
              {claimResult?.cardImage && (
                <div className="text-white mb-4">
                  <div className="flex gap-2">
                    <Image
                      width={200}
                      height={300}
                      src={claimResult.cardImage}
                      alt="Card Image"
                    />
                  </div>
                </div>
              )}
            </div>
            <button className=" underline" onClick={handleViewClick}>
              View
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ClaimResult;
