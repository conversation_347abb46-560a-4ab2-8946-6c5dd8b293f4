'use client';

import ArrowCircleDownIcon from '@/assets/icons/Arrow-Circle-Down';
import ArrowCircleLeftIcon from '@/assets/icons/Arrow-Circle-Left';
import CallGoldenIcon from '@/assets/icons/CallGoldenIcon';
import CallIcon from '@/assets/icons/CallIcon';
import ChatIcon from '@/assets/icons/Chat';
import OnlineUserImg from '@/assets/images/demo-image/user-img.jpg';
import useLivePlayers from '@/hooks/useLivePlayers';
import usePrivateCall from '@/hooks/usePrivateCall';
import {
  useCreateFriendsRequest,
  useUnFriendsRequest,
} from '@/reactQuery/chatWindowQuery';
import useActiveGroupStore from '@/store/useActiveGroupStore';
import useAuthStore from '@/store/useAuthStore';
import useModalStore from '@/store/useModalStore';
import usePrivateChatStore from '@/store/usePrivateChatStore';
import useUserInfoStore from '@/store/useUserInfoStore';
import { useOpenChatWindow } from '@/utils/chat';
import useEmblaCarousel from 'embla-carousel-react';
import { Clock, UserRound, UserRoundPlus } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import 'swiper/css';
import 'swiper/css/navigation';
import { Mousewheel } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import GameCard from '../Common/GameCard';
import UserInfo from '../UserInfoModal';
import ActiveGroupModel from './ActiveGroupModal';
import useActiveGroups from '@/hooks/useActiveGroup';
import UserAvatar from '../DefaultImage';
import useGroupChatStore from '@/store/useGroupChatStore';
import useChatWindow from '@/hooks/useChatWindow';
import useVoiceCallStore from '@/store/useVoiceCallStore';
import useGroupCall from '@/hooks/useGroupCall';
import { handleGameClick, useGameUtils } from '@/utils/game';
import CallAndPlay from '../../assets/images/CallAndPlay.png';
import { userStatusColor } from '@/utils/helper';
import ChatAvatar from '../ChatWindow/ChatAvatar';
import IconButton from '../Common/Button/IconButton';

function ActiveGroupSlider({
  className = '',
  gamesList,
  id,
  isAuthenticated,
  tab,
  ...props
}) {
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    slidesToScroll: 2,
    align: 'start',
    dragFree: false,
  });

  const {
    myGroups,
    publicGroups,
    myGroupsLoading,
    publicGroupsLoading,
    activeTab,
    setActiveTab,
  } = useActiveGroupStore((state) => state);
  const { onlinePlayers } = useLivePlayers();
  const { myPlayerLoading, refetchPublicGroups } = useActiveGroups();
  const {
    setIsPrivateChatOpen,
    isCallActive: isPrivateCallActive,
    searchUserName,
    setSearchUserName,
  } = usePrivateChatStore((state) => state);
  const openChatWindow = useOpenChatWindow();
  const { initiateCall, disconnectCall } = usePrivateCall();
  const { openModal } = useModalStore();
  const { openUserInfoModal } = useUserInfoStore((state) => state);
  const {
    setIsGroupChatOpen,
    setGroupId,
    setGroupChat,
    setGroupName,
    isCallActive,
    userId,
  } = useGroupChatStore((state) => state);
  const { setSection, section } = useChatWindow();
  const { setVoiceCall, voiceCall } = useVoiceCallStore((state) => state);
  const { handleClickGame } = useGameUtils();
  const { handleJoinCall, handleDeclineCall, callInitialized } = useGroupCall();
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    // setSelectedIndex(emblaApi.selectedScrollSnap());
    setCanScrollPrev(emblaApi.canScrollPrev());
    setCanScrollNext(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const mutationRequest = useCreateFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);

      refetchPublicGroups();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchPublicGroups();
    },
  });

  const mutationUnFriendRequest = useUnFriendsRequest({
    onSuccess: (response) => {
      toast.success(response?.data?.message);
      refetchPublicGroups();
    },
    onError: (error) => {
      toast.error(error.response.data.errors.map((e) => e.description));
      refetchPublicGroups();
    },
  });

  const unFriend = (unfriendUserId) => {
    mutationUnFriendRequest.mutate({ unfriendUserId: +unfriendUserId });
  };
  const sendFriendRequest = (id) => {
    mutationRequest.mutate({
      requesteeId: +id,
    });
  };

  const openChat = (groupId, groupName) => {
    // queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setSection('GroupChat');
    setIsGroupChatOpen(true);
    setIsPrivateChatOpen(false);
    if (searchUserName != '') {
      setSearchUserName('');
    }
    openChatWindow();
  };
  const { userDetails } = useAuthStore((state) => state);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const swiperRef = useRef(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const totalGames = activeTab === 0 ? myGroups?.length : publicGroups?.length;
  const noNavigationNeeded = isMobile ? totalGames <= 2 : totalGames <= 5;

  const handleSlidePrev = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        Math.max(swiperRef.current.activeIndex - (isMobile ? 2 : 5), 0),
      );
    }
  };

  const handleSlideNext = () => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(
        swiperRef.current.activeIndex + (isMobile ? 2 : 5),
      );
    }
  };
  const handleJoinAndPlay = (
    gameName,
    providerName,
    groupName,
    callLogId,
    groupId,
    groupCallMembers,
  ) => {
    setVoiceCall({
      channelName: groupName,
      callLogId: callLogId,
      groupId: groupId,
      groupCallMembers: [
        {
          username: userDetails?.username,
          profileImage: userDetails?.profileImage,
          firstName: userDetails?.firstName,
          lastName: userDetails?.lastName,
        },
        ...groupCallMembers,
      ],
    });
    handleJoinCall({
      channelName: groupName,
      callLogId: callLogId,
      groupId: groupId,
      groupCallMembers: [
        {
          username: userDetails?.username,
          profileImage: userDetails?.profileImage,
          firstName: userDetails?.firstName,
          lastName: userDetails?.lastName,
        },
        ...groupCallMembers,
      ],
      isConnectedPlay: true,
    });
    handleClickGame(gameName, providerName);
  };

  return (
    <div className="max-w-containerWidth mx-auto w-full">
      {((activeTab === 0 && myGroups?.length != 0) ||
        (activeTab == 1 && publicGroups?.length > 0)) && (
        <div className="absolute right-0 top-7 flex flex-row items-center gap-2 ">
          <div className="flex gap-2">
            <button
              onClick={handleSlidePrev}
              disabled={isBeginning || noNavigationNeeded}
            >
              <ArrowCircleLeftIcon
                className={`size-7 fill-steelTeal-1000 transition-opacity ${
                  isBeginning || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>

            <button
              onClick={handleSlideNext}
              disabled={isEnd || noNavigationNeeded}
            >
              <ArrowCircleDownIcon
                className={`size-7 -rotate-90 fill-steelTeal-1000 transition-opacity ${
                  isEnd || noNavigationNeeded
                    ? 'cursor-not-allowed opacity-30'
                    : 'cursor-pointer'
                }`}
              />
            </button>
          </div>
          {/* <button
            type="button"
            className={`${!canScrollPrev ? 'opacity-40' : 'opacity-100'} size-8.5 rounded-px_10 embla__button embla__button--prev bg-primaryBg hover:bg-charcoalBlack-500 flex items-center  justify-center transition-all duration-300`}
            onClick={scrollPrev}
          >
            <ArrowCircleLeftIcon className="size-7 fill-steelTeal-1000" />
          </button>
          <button
            type="button"
            className={`${!canScrollNext ? 'opacity-40' : 'opacity-100'} size-8.5 rounded-px_10 embla__button embla__button--next bg-primaryBg hover:bg-charcoalBlack-500 flex items-center justify-center transition-all duration-300`}
            onClick={scrollNext}
          >
            <ArrowCircleDownIcon className="size-7 -rotate-90 fill-steelTeal-1000" />
          </button> */}
        </div>
      )}
      <div className={`${className}`}>
        <div className="embla m-auto">
          <div
          // className="embla__viewport overflow-hidden  pb-7 pt-5 max-md:pb-4"
          // ref={emblaRef}
          >
            <Swiper
              breakpoints={{
                0: {
                  slidesPerView: 2,
                  slidesPerGroup: 2,
                  spaceBetween: 7,
                },
                500: {
                  slidesPerView: 3,
                  slidesPerGroup: 3,
                  spaceBetween: 14,
                },
                768: {
                  slidesPerView: 4,
                  slidesPerGroup: 4,
                  spaceBetween: 14,
                },
                1024: {
                  slidesPerView: 5,
                  slidesPerGroup: 5,
                  spaceBetween: 14,
                },
              }}
              modules={[Mousewheel]}
              mousewheel={{ forceToAxis: true }}
              onSwiper={(swiper) => {
                swiperRef.current = swiper;
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
              className="!px-1"
            >
              {/* <div className=""> */}
              {(activeTab === 0 ? myGroups : publicGroups)?.map((user) => (
                <SwiperSlide
                  key={user?.id}
                  style={{ overflow: 'visible', padding: '10px 0' }}
                >
                  <div className="">
                    <GameCard
                      src={user?.gameUrl}
                      width={200}
                      height={200}
                      gameId={user?.gameId}
                      isFavorite={user?.isFavorite}
                      alt={user?.gameName}
                      onClick={() => {
                        if (
                          Boolean(user?.isUserExist && !isPrivateCallActive)
                        ) {
                          handleJoinAndPlay(
                            user?.gameName,
                            user?.providerName,
                            user?.groupName,
                            user?.callLogId,
                            user?.groupId,
                            user?.users,
                          );
                        } else {
                          handleClickGame(user?.gameName, user?.providerName);
                        }
                      }}
                    />
                    <div className="inline-flex w-full items-center  justify-between   py-1">
                      <div className="inline-flex  items-center gap-2 ">
                        <div className="relative inline-flex size-[30px] shrink-0 cursor-pointer rounded-full  border border-solid border-richBlack-900 ">
                          {user?.groupProfile ? (
                            <Image
                              src={user?.groupProfile || OnlineUserImg}
                              alt=""
                              width={50}
                              height={50}
                              className="w-full rounded-full object-cover object-center"
                            />
                          ) : (
                            <UserAvatar firstName={user?.groupName} size={30} />
                          )}

                          <span
                            className={`absolute bottom-0  right-0 z-[1] size-2 rounded-full ${userStatusColor(user?.currentStatus || 'AVAILABLE')}`}
                          ></span>
                        </div>
                        <h4 className="text-white max-w-[85px]  text-base font-normal">
                          {user?.groupName}
                        </h4>
                      </div>
                      {
                        <div className="flex items-center gap-1">
                          <Image
                            src={CallAndPlay}
                            width={25}
                            height={25}
                            className={` hover:cursor-pointer`}
                            // alt={alt}
                            onClick={() => {
                              if (
                                Boolean(
                                  user?.isUserExist && !isPrivateCallActive,
                                )
                              ) {
                                handleJoinAndPlay(
                                  user?.gameName,
                                  user?.providerName,
                                  user?.groupName,
                                  user?.callLogId,
                                  user?.groupId,
                                  user?.users,
                                );
                              } else {
                                handleClickGame(
                                  user?.gameName,
                                  user?.providerName,
                                );
                              }
                            }}
                            loading="eager"
                          />

                          {
                            callInitialized?.groupId === user?.groupId ? (
                              <IconButton
                                disabled
                                className="bg-steelTeal-1000/20 h-6 w-6 animate-pulse rounded-full"
                              >
                                <CallIcon className="fill-steelTeal-500 h-5 w-5 opacity-70" />
                              </IconButton>
                            ) : (
                              !isPrivateCallActive &&
                              Boolean(user?.isUserExist) &&
                              (isCallActive ? (
                                voiceCall?.channelName == user.groupName && (
                                  <CallIcon
                                    className={` ${isCallActive ? 'fill-red-600' : 'text-blue-400'} size-4.1 cursor-pointer`}
                                    onClick={() => {
                                      if (isCallActive) {
                                        handleDeclineCall();
                                      } else {
                                        setVoiceCall({
                                          channelName: user?.groupName,
                                          callLogId: user?.callLogId,
                                          groupId: user?.groupId,
                                        });
                                        handleJoinCall({
                                          channelName: user?.groupName,
                                          callLogId: user?.callLogId,
                                          groupId: user?.groupId,
                                          groupCallMembers: [
                                            {
                                              username: userDetails?.username,
                                              profileImage:
                                                userDetails?.profileImage,
                                              firstName: userDetails?.firstName,
                                              lastName: userDetails?.lastName,
                                            },
                                            ...user?.users,
                                          ],
                                          isConnectedPlay: true,
                                        });
                                      }
                                    }}
                                  />
                                )
                              ) : (
                                <CallGoldenIcon
                                  className={` ${isCallActive ? 'fill-red-600' : 'text-blue-400'} size-4.1 cursor-pointer`}
                                  onClick={() => {
                                    setVoiceCall({
                                      channelName: user?.groupName,
                                      callLogId: user?.callLogId,
                                      groupId: user?.groupId,
                                    });
                                    handleJoinCall({
                                      channelName: user?.groupName,
                                      callLogId: user?.callLogId,
                                      groupId: user?.groupId,
                                      groupCallMembers: [
                                        {
                                          username: userDetails?.username,
                                          profileImage:
                                            userDetails?.profileImage,
                                          firstName: userDetails?.firstName,
                                          lastName: userDetails?.lastName,
                                        },
                                        ...user?.users,
                                      ],
                                      isConnectedPlay: true,
                                    });
                                  }}
                                />
                              ))
                            )

                            // <CallIcon className="size-3.5 cursor-pointer fill-groupUserBorder" />
                          }

                          <ChatIcon
                            className="size-4.1 h-[20px] w-[20px] cursor-pointer fill-red-400"
                            onClick={() =>
                              openChat(user?.groupId, user?.groupName)
                            }
                          />
                        </div>
                      }
                    </div>
                    <div className="py-2 pl-[18px]">
                      {user?.users?.map(
                        (player, index) =>
                          index < 3 && (
                            <div
                              className="inline-flex w-full max-w-[181px] items-center  justify-between rounded-full border border-solid border-secondaryBorder px-2 py-1"
                              key={index}
                            >
                              <div className="inline-flex items-center gap-2 ">
                                <div className="relative inline-flex size-[30px] shrink-0 cursor-pointer">
                                  <div className="pointer-events-none absolute inset-0 z-10 size-[30px] rounded-full border border-solid border-richBlack-900"></div>
                                  <div
                                    className="size-[30px] overflow-hidden rounded-full"
                                    onClick={() => {
                                      openUserInfoModal(player?.userId);
                                      openModal(<UserInfo />);
                                    }}
                                  >
                                    <ChatAvatar
                                      profileImage={player?.profileImage}
                                      firstName={
                                        player?.firstName ||
                                        player?.username ||
                                        'User'
                                      }
                                      lastName={player?.lastName || ''}
                                      userName={player?.username}
                                      imageClassName="h-full w-full rounded-full object-cover object-center"
                                      imageWidth={30}
                                      imageHeight={30}
                                      avatarSize={30}
                                    />
                                  </div>

                                  <span
                                    className={`absolute bottom-0  right-0 z-20 size-2 rounded-full ${userStatusColor(player?.currentStatus || 'AVAILABLE')}`}
                                  ></span>
                                </div>
                                <h4 className="text-white max-w-[85px] truncate text-base font-normal">
                                  {player?.username}
                                </h4>
                              </div>
                            </div>
                          ),
                      )}
                    </div>
                    {user?.users?.length > 3 && (
                      <PrimaryButtonOutline
                        className="!h-[30px] !min-h-[30px] w-full max-w-[102px] !p-1"
                        onClick={() => {
                          openModal(<ActiveGroupModel activeGroup={user} />);
                        }}
                      >
                        View more
                      </PrimaryButtonOutline>
                    )}
                  </div>
                </SwiperSlide>
              ))}
              {((activeTab === 0 && myGroups?.length == 0) ||
                (activeTab == 1 && publicGroups?.length == 0)) && (
                <div className="mt-4 text-[1rem] font-[600]">
                  Currently No Groups Are Active
                </div>
              )}
              {/* </div> */}
            </Swiper>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActiveGroupSlider;
