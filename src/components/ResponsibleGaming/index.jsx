'use client';

import React, { useState } from 'react';
import Tabs from '../Common/Tabs';
import PrimaryButton from '../Common/Button/PrimaryButton';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import PurchaseLimit from './PurchaseLimit';
import RiskLimit from './RiskLimit';
import SelfExclusion from './SelfExclusion';

const TextInput = ({ label, placeholder, value, onChange }) => {
  return (
    <div className="mb-4">
      <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
        {label}
      </label>
      <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
        <input
          type="text"
          className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
          placeholder={placeholder}
          value={value}
          onChange={onChange}
        />
      </div>
    </div>
  );
};

function ResponsibleGaming() {
  const [loginPreventDuration, setLoginPreventDuration] = useState('');

  const taskListTabs = [
    {
      label: 'Purchase Limit',
      content: <PurchaseLimit />,
    },
    {
      label: 'Risk Limit',
      content: <RiskLimit />,
    },
    {
      label: 'Self Exclusion',
      content: <SelfExclusion />,
    },
  ];

  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  return (
    <div>
      {/* <div className="flex justify-center md:justify-start">
        <ul className="before:opacity-1 relative flex w-full items-center justify-start before:pointer-events-none before:absolute before:left-0 before:right-0 before:h-auto before:w-full before:rounded-md before:border before:border-steelTeal-1000 before:p-5">
          <li className="z-[1] w-1/3">
            <button
              type="button"
              aria-current="page"
              className="inline-block w-full rounded-md bg-primary-1000 px-6 py-3 text-center text-base font-bold text-white-1000 max-sm:px-2 max-xs:px-1 uppertab:px-2"
            >
              Purchase Limit
            </button>
          </li>
          <li className="z-[1] w-1/3">
            <button
              type="button"
              aria-current="page"
              className="inline-block w-full rounded-md px-6 py-3 text-center text-base font-normal text-steelTeal-1000 max-sm:px-2 max-xs:px-1 uppertab:px-2"
            >
              Risk Limit
            </button>
          </li>
          <li className="z-[1] w-1/3">
            <button
              type="button"
              aria-current="page"
              className="inline-block w-full rounded-md px-6 py-3 text-center text-base font-normal text-steelTeal-1000 max-sm:px-2 max-xs:px-1 uppertab:px-2"
            >
              Self Exclusion
            </button>
          </li>
        </ul>
      </div> */}

      <Tabs tabs={taskListTabs} />

      <div className="mt-12 hidden max-sm:mt-10">
        <div className="grid grid-cols-1 gap-6 desktop:gap-4 uppertab:gap-3">
          <p className="pb-6 text-xl text-steelTeal-1000">
            You’ll be restricted from logging-in for the time you select here!
          </p>

          <TextInput
            label="Prevent me to login for"
            placeholder="1 Minute"
            value={loginPreventDuration}
            onChange={(e) => setLoginPreventDuration(e.target.value)}
          />
        </div>
      </div>

      <div className="mt-12 flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
        <PrimaryButton type="submit">Save</PrimaryButton>
        <PrimaryButtonOutline>Cancel</PrimaryButtonOutline>
      </div>
    </div>
  );
}

export default ResponsibleGaming;
