import React, { useState } from 'react';

function TextInput({ label, placeholder, value, onChange }) {
  return (
    <div className="mb-4">
      <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
        {label}
      </label>
      <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
        <input
          type="text"
          className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
          placeholder={placeholder}
          value={value}
          onChange={onChange}
        />
      </div>
    </div>
  );
}

function RiskLimit() {
  const [loginPreventDuration, setLoginPreventDuration] = useState('');
  return (
    <div className="mt-12 max-sm:mt-10">
      <div className="grid grid-cols-1 gap-6 desktop:gap-4 uppertab:gap-3">
        <p className="pb-6 text-xl text-steelTeal-1000">
          You’ll be restricted from logging-in for the time you select here!
        </p>

        <TextInput
          label="Prevent me to login for"
          placeholder="1 Minute"
          value={loginPreventDuration}
          onChange={(e) => setLoginPreventDuration(e.target.value)}
        />
      </div>
    </div>
  );
}

export default RiskLimit;
