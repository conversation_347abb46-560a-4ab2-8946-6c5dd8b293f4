import React, { useState } from 'react';

function TextInput({ label, placeholder, value, onChange }) {
  return (
    <div className="mb-4">
      <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
        {label}
      </label>
      <div className="text-white relative w-full rounded-md bg-tiber-1000 font-normal">
        <input
          type="text"
          className="text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-steelTeal-1000 "
          placeholder={placeholder}
          value={value}
          onChange={onChange}
        />
      </div>
    </div>
  );
}

function PurchaseLimit() {
  const [singleLimit, setSingleLimit] = useState('');
  const [dailyLimit, setDailyLimit] = useState('');
  const [weeklyLimit, setWeeklyLimit] = useState('');
  const [monthlyLimit, setMonthlyLimit] = useState('');
  return (
    <div className="mt-12 max-sm:mt-10">
      <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1 desktop:gap-4 uppertab:grid-cols-1 uppertab:gap-3">
        <TextInput
          label="Single Limit"
          placeholder="Single Limit"
          value={singleLimit}
          onChange={(e) => setSingleLimit(e.target.value)}
        />
        <TextInput
          label="Daily Limit"
          placeholder="Daily Limit"
          value={dailyLimit}
          onChange={(e) => setDailyLimit(e.target.value)}
        />
        <TextInput
          label="Weekly Limit"
          placeholder="Weekly Limit"
          value={weeklyLimit}
          onChange={(e) => setWeeklyLimit(e.target.value)}
        />
        <TextInput
          label="Monthly Limit"
          placeholder="Monthly Limit"
          value={monthlyLimit}
          onChange={(e) => setMonthlyLimit(e.target.value)}
        />
      </div>
    </div>
  );
}

export default PurchaseLimit;
