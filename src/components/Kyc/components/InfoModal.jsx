import React from 'react';
import PropTypes from 'prop-types';

const InfoModal = ({
  title,
  textTitle,
  textContent,
  handleOnClick,
  buttonLabel = 'Continue',
  hideButton = false,
}) => {
  return (
    <div className="w-full max-w-md mx-auto bg-[#1c1c1e] border border-white/10 text-white rounded-2xl shadow-2xl px-6 py-8 space-y-6">
      {/* Title */}
      {title && (
        <h2 className="text-2xl font-bold text-white tracking-tight">{title}</h2>
      )}

      {/* Content */}
      <div className="space-y-2">
        <h3 className="text-base sm:text-lg font-semibold text-gray-100">
          {textTitle}
        </h3>
        <p className="text-sm sm:text-base text-gray-400 leading-relaxed">
          {textContent}
        </p>
      </div>

      {/* Button */}
      {!hideButton && (
        <div>
          <button
            onClick={handleOnClick}
            className="w-full min-h-10 rounded-md border-2 border-solid border-primary-1000 bg-richBlack-500 px-6 py-1.5 text-base font-normal text-white transition duration-200 hover:bg-richBlack-400"
          >
            {buttonLabel}
          </button>
        </div>
      )}
    </div>
  );
};

InfoModal.propTypes = {
  title: PropTypes.string.isRequired,
  textTitle: PropTypes.string.isRequired,
  textContent: PropTypes.string.isRequired,
  handleOnClick: PropTypes.func,
  buttonLabel: PropTypes.string,
  hideButton: PropTypes.bool,
};

export default InfoModal;
