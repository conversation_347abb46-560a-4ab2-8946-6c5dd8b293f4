'use client';

import { createVeriff<PERSON>rame } from "@veriff/incontext-sdk";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

import useAuthStore from "@/store/useAuthStore";
import AccVerifyQuery from "@/reactQuery/accVerifyQuery";
import InfoModal from "./components/InfoModal";
import { useQueryClient } from '@tanstack/react-query';

function Kyc() {
  const [initKYCData, setInitKYCData] = useState(null);
  const [initializeKYC, setInitializeKYC] = useState(false);
  // const userDetails = useUserInfoStore((state) => state.userDetails);
  // const user = useUserInfoStore((state) => state);
  const { userDetails, setUserDetails } = useAuthStore((state) => state);
  // useEffect(() => {
  //   setInitializeKYC(true); // Trigger <PERSON>Y<PERSON> automatically on page load
  // }, []);
  const queryClient = useQueryClient();

  const errorHandler = (errData) => {
    if (errData?.errorCode === 3035) {

    } else if (errData?.errorCode === 3072) {

    } else if (errData?.errorCode === 3073) {

    }
  };

  const successHandler = (data) => {
    if (!data?.success) {

    }
  };

  const { data, refetch } = AccVerifyQuery.initKYCQuery({
    enabled: initializeKYC,
    errorHandler,
    successHandler,
  });

  useEffect(() => {
    if (data?.success) {
      setInitKYCData(data?.verification);
    }
  }, [data]);

  useEffect(() => {
    if (initKYCData?.url) {
      const veriffFrame = createVeriffFrame({
        url: initKYCData.url,
        onEvent: function (msg) {
          if (msg === "CANCELED" || msg === "FINISHED") {
            closeVeriffFrame();
            setInitKYCData(null);
            if (msg === "FINISHED") {
              setUserDetails({ ...userDetails, veriffStatus: "REQUESTED" });
            }
          }
        },
      });

      const closeVeriffFrame = () => {
        veriffFrame?.close();
        queryClient.invalidateQueries({ queryKey: ['USER_PROFILE'] })
      };

      return () => closeVeriffFrame();
    }
  }, [initKYCData]);

  const handleVeriffKYC = () => {
    if (userDetails?.kycStatus === 'REQUESTED') {
      toast.success('KYC verification already submitted');
      return;
    }

    if (userDetails?.kycStatus === 'APPROVED') {
      toast.success('KYC already approved');
      return;
    }

    setInitializeKYC(true);
    initializeKYC && refetch();
  };

  const getKycStateUI = () => {
    const status = userDetails?.kycStatus;
    const reason = userDetails?.moreDetails?.veriffReason;
  
    switch (status) {
      case 'APPROVED':
        return {
          title: 'KYC Approved',
          textTitle: 'Verification Complete',
          textContent: 'Your documents have been approved. No further action needed.',
          showButton: false,
        };
      case 'REQUESTED':
        return {
          title: 'KYC Submitted',
          textTitle: 'Verification in Progress',
          textContent: 'Our team is reviewing your documents. Please wait for approval.',
          showButton: false,
        };
      case 'PENDING':
        return {
          title: 'KYC Required',
          textTitle: 'Start Your Verification',
          textContent: 'You have not completed your KYC yet. Please begin the verification process to continue.',
          showButton: true,
          buttonLabel: 'Start Verification',
        };
      case 'REJECTED':
        return {
          title: 'KYC Rejected',
          textTitle: 'Submission Declined',
          textContent: `Reason: ${reason || 'Your submission did not meet the requirements. Please try again.'}`,
          showButton: true,
          buttonLabel: 'Re-submit Verification',
        };
      case 'RE-REQUESTED':
        return {
          title: 'KYC Re-Verification',
          textTitle: 'Additional Documents Needed',
          textContent: `Reason: ${reason || 'Additional information is required to verify your identity.'}`,
          showButton: true,
          buttonLabel: 'Re-submit Verification',
        };
      default:
        return {
          title: 'Start Your KYC',
          textTitle: 'Complete Your Verification',
          textContent: 'Upload your documents to begin the KYC verification process.',
          showButton: true,
          buttonLabel: 'Start Verification',
        };
    }
  };

  const {
    title,
    textTitle,
    textContent,
    showButton,
    buttonLabel = 'Start Verification',
  } = getKycStateUI();

  return (
    <>
      <div id="veriff-root" />
      <InfoModal
        title={title}
        textTitle={textTitle}
        textContent={textContent}
        handleOnClick={handleVeriffKYC}
        buttonLabel={buttonLabel}
        hideButton={!showButton}
      />
    </>
  );
}

export default Kyc;
