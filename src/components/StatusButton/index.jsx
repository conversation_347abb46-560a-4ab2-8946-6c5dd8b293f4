import { Ghost } from 'lucide-react';
import StatusToggleSwitch from '../Common/ToggleSwitch/StatusToggleSwitch';
import GhostGoldenIcon from '@/assets/icons/GhostGoldenIcon';
// import StatusIndicator from './StatusIndicator';

function StatusButton({ isActive, onClose }) {
  return (
    <div
      className="flex w-full items-center justify-between gap-2.5 p-2.5 hover:bg-nav-gradient"
      onClick={() => {
        if (onClose) onClose();
      }}
    >
      <div className="flex items-center gap-2">
        <GhostGoldenIcon className="fill-current h-5 w-5 text-gray-300" />
        <span className="text-base font-normal text-white-1000">Ghost Mode</span>
      </div>
      <div className="flex items-center gap-2">
        {/* <StatusIndicator isActive={isActive} /> */
        }
        <StatusToggleSwitch />
      </div>
    </div>
  );
}

export default StatusButton;
