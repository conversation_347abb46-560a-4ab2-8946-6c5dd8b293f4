import React, { useEffect, useState } from 'react';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import useSignUp from '@/hooks/useSignUp';
import InputField from '@/components/Common/InputField';
import { signUpSchema } from '@/schemas/auth';
import ButtonLoader from '@/components/Common/Loader/ButtonLoader';
import FacebookIcon from '@/assets/icons/FacebookIcon';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import TwitterIcon from '@/assets/icons/TwitterIcon';
import DiscordIcon from '@/assets/icons/DiscordIcon';
import WhatsappIcon from '@/assets/icons/WhatsappIcon';
import GoogleIcon from '@/assets/icons/GoogleIcon';
import Image from 'next/image';
import OnlyFans from '@/assets/icons/onlyfansIcon.svg';
import TwitchIcon from '@/assets/icons/TwitchIcon';
import ForcedEmailModal from '@/components/Models/ForcedEmailModal';
import {
  useFacebookLoginMutation,
  useGoogleLoginMutation,
  useTwitchLoginMutation,
  useDiscordLoginMutation,
} from '@/reactQuery/authQuery';
import useModalStore from '@/store/useModalStore';
import { useGoogleLogin } from '@react-oauth/google';
import { useSearchParams, usePathname } from 'next/navigation';

function SignUp() {
  const [formData, setFormData] = useState({
    userName: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    // acceptTerms: false,
    over18: false,
  });
  const { closeModal, openModal } = useModalStore((state) => state);
  const {
    signUp,
    isLoading,
    googleMutation,
    facebookMutation,
    discordMutation,
    twitchMutation,
  } = useSignUp();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  useEffect(() => {
    const code = searchParams.get('code');
    let timeout;

    if (code) {
      timeout = setTimeout(() => {
        if (pathname === '/discord-login') {
          const payload = {
            code,
            isSignup: true,
          };
          discordMutation.mutate(payload);
        } else if (pathname === '/twitch-signup') {
          const payload = {
            code,
            isSignup: true,
          };
          twitchMutation.mutate(payload);
        }
      }, 500); // 500ms delay to prevent quick re-triggers
    }

    return () => clearTimeout(timeout);
  }, [pathname, searchParams]);

  useEffect(() => {
    // Load the Facebook SDK
    window.fbAsyncInit = function () {
      FB.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v11.0',
      });
      FB.AppEvents.logPageView();
    };

    (function (d, s, id) {
      let js;
      const fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
  }, []);

  const [formErrors, setFormErrors] = useState({
    userName: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    // acceptTerms: false,
    over18: false,
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.over18) {
      toast.error(
        'You must confirm that you are at least 18 years old to proceed!',
      );
      return;
    }

    try {
      signUpSchema.parse({ ...formData });
      signUp({
        ...formData,
        password: btoa(formData.password),
        confirmPassword: btoa(formData.confirmPassword),
      });
    } catch (validationError) {
      setFormErrors(validationError.formErrors.fieldErrors);
    }
  };
  // const googleMutation = useGoogleLoginMutation({
  //   onSuccess: (response) => {
  //     console.log(response, "::::::::::response for google login")
  //   },
  //   onError: (error) => {
  //     console.log(error, " :::google login")
  //     toast.error(res?.message || "Failed to login with Google.");
  //   },
  // });

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      if (tokenResponse) {
        const userData = {
          credential: tokenResponse.access_token,
          isSignup: true,
          isTermsAccepted: true,
        };
        googleMutation.mutate(userData);
      }
    },
    onError: (errorResponse) =>
      console.log(errorResponse, ':::::google res login'),
  });

  const handleFacebookClick = (e) => {
    e.preventDefault();
    FB.login(
      function (response) {
        if (response && response.authResponse && response.authResponse.userID) {
          FB.api(
            `/${response.authResponse.userID}`,
            { fields: ['first_name', 'last_name', 'email'] },
            function (_response) {
              responseFacebook(_response);
            },
          );
        }
      },
      { scope: 'public_profile,email' },
    );
  };

  const responseFacebook = (response) => {
    const userData = {
      firstName: response.first_name,
      lastName: response.last_name,
      userId: response.id,
      email: response.email,
      isSignup: true,
      isTermsAccepted: true,

      isForceEmail: false,
    };

    if (response && response.email) {
      facebookMutation.mutate(userData);
    } else {
      openModal(<ForcedEmailModal userData={userData} />);
    }
  };

  const handleChange = (e) => {
    const { name, value, checked } = e.target;

    if (name === 'over18' || name === 'acceptTerms') {
      setFormData({
        ...formData,
        [name]: checked,
      });
      setFormErrors({
        ...formErrors,
        [name]: false,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value.trim(),
      });
      setFormErrors({
        ...formErrors,
        [name]: '',
      });
    }
  };
  const signInWithDiscord = () => {
    const discordAuthUrl = `https://discord.com/oauth2/authorize?client_id=${process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(process.env.NEXT_PUBLIC_DISCORD_REDIRECT_URI)}&scope=${process.env.NEXT_PUBLIC_DISCORD_SCOPE}`;

    const width = 500;
    const height = 600;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;

    window.open(
      discordAuthUrl,
      '_self',
      `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`,
    );
  };

  const signInWithTwitch = () => {
    const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_TWITCH_REDIRECT_URI;
    const scope = 'openid user:read:email user:read:follows';
    console.log(redirectUri, ':::::::redirectUri');
    const authUrl = `https://id.twitch.tv/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}`;

    window.location.href = authUrl;
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid grid-cols-2 gap-2 max-xs:grid-cols-1">
        <InputField
          type="text"
          name="firstName"
          value={formData.firstName}
          placeholder="Enter First Name"
          onChange={handleChange}
          error={formErrors.firstName}
          label="First Name"
        />
        <InputField
          type="text"
          name="lastName"
          value={formData.lastName}
          placeholder="Enter Last Name"
          onChange={handleChange}
          error={formErrors.lastName}
          label="Last Name"
        />
        <InputField
          type="text"
          name="userName"
          value={formData.userName}
          placeholder="Enter User Name"
          onChange={handleChange}
          error={formErrors.userName}
          label="User Name"
        />
        <InputField
          type="text"
          name="email"
          value={formData.email}
          placeholder="Enter Your Email"
          onChange={handleChange}
          error={formErrors.email}
          label="Email"
        />
        <InputField
          type="password"
          name="password"
          value={formData.password}
          placeholder="Enter Your Password"
          onChange={handleChange}
          error={formErrors.password}
          label="Password"
        />
        <InputField
          type="password"
          name="confirmPassword"
          value={formData.confirmPassword}
          placeholder="Enter Confirm Password"
          onChange={handleChange}
          error={formErrors.confirmPassword}
          label="Confirm Your Password"
        />
      </div>

      <div className="mt-3 flex gap-3">
        <input
          name="over18"
          id="over18"
          type="checkbox"
          checked={formData.over18}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check "
        />
        <label
          htmlFor="over18"
          className="text-white text-[0.813rem] font-normal !leading-tight md:text-base cursor-pointer"
        >
          I am at least 18 years old and not a resident of the restricted
          states.
        </label>
      </div>

      {/*  <div className="mt-1 flex gap-3">
        <input
          name="acceptTerms"
          id="conditions"
          type="checkbox"
          checked={formData.acceptTerms}
          onChange={handleChange}
          className="border-white h-5 w-5 min-w-5 cursor-pointer appearance-none rounded border border-solid bg-transparent bg-contain bg-center bg-no-repeat checked:bg-primary-1000 checked:bg-checkbox-check"
        />
        <label
          htmlFor="conditions"
          className="text-white cursor-pointer text-[0.813rem] font-normal md:text-base !leading-tight"
        >
          I accept the Terms and Conditions
        </label>
      </div>

      {(formErrors.acceptTerms || formErrors.over18) && (
        <p className="mt-1 text-red-500">
          {formErrors.acceptTerms || formErrors.over18}
        </p>
      )} */}
      <div className="mt-6 flex w-full flex-wrap items-center justify-between gap-2 md:justify-between max-xxs:flex-col">
        <div className="flex items-center gap-2 pr-4 max-xxs:flex-col max-xxs:pr-0">
          <PrimaryButton type="submit" disabled={isLoading}>
            {isLoading ? <ButtonLoader /> : 'Sign Up'}
          </PrimaryButton>

          <span>or</span>
        </div>
        <div className="flex flex-wrap items-center justify-end gap-2 max-xxs:justify-center [&>button:hover]:bg-richBlack-500 [&>button]:flex [&>button]:h-10 [&>button]:w-10 [&>button]:items-center [&>button]:justify-center [&>button]:rounded-md [&>button]:bg-richBlack-1000 [&>button]:p-2 [&>button]:transition-all [&>button]:duration-200 [&>button]:ease-in-out">
          <button type="button" onClick={handleFacebookClick}>
            <FacebookIcon />
          </button>
          <button type="button" onClick={signInWithDiscord}>
            <DiscordIcon />
          </button>
          {/* twitch */}
          <button type="button" onClick={signInWithTwitch}>
            <TwitchIcon />
          </button>
          <button type="button" onClick={() => handleGoogleLogin()}>
            <GoogleIcon />
          </button>
          {/* <Link href='/'><GoogleIcon /></Link> */}
          {/* <button>
            <Image
              src={OnlyFans}
              width={10000}
              height={10000}
              className="h-5 w-5"
            />
          </button> */}
        </div>
      </div>

      {/* <div className='flex items-center justify-end gap-2 [&>a]:bg-richBlack-1000 [&>a]:p-2 [&>a]:rounded-md border-t-2 border-solid border-richBlack-1000 pt-4 mt-4'>
        <Link href='/'><FacebookIcon /></Link>
        <Link href='/'><TwitterIcon /></Link>
        <Link href='/'><DiscordIcon /></Link>
        <Link href='/'><WhatsappIcon /></Link>
        <Link href='/'><GoogleIcon /></Link>
      </div> */}
    </form>
  );
}

export default SignUp;
