'use client';

import useAuthStore from '@/store/useAuthStore';
import ActiveGroupSlider from '../ActiveGroupSlider';
import Tabs from '../Common/Tabs/ActiveGroupTab';
import Image from 'next/image';
const ActiveGroup = () => {
  // const [activeTab, setActiveTab] = useState(0);
  
  const { isAuthenticated } = useAuthStore();
  const taskListTabs = [
    {
      label: 'My Groups',
      content: (
        <ActiveGroupSlider
          tab={'friends'}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
      ),
    },
    {
      label: 'Public',
      content: (
        <ActiveGroupSlider
          tab={'public'}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
      ),
      // content: <SelfExclusion />,
    },
  ];
  if (!isAuthenticated) {
    return null;
  }
  return (
    <div className="relative mb-9 mt-10 rounded-lg pb-4">
      <div className="flex flex-col items-start ">
        <Tabs
          tabs={taskListTabs}
          // activeTab={activeTab}
          // setActiveTab={setActiveTab}
        />
       
      </div>
    </div>
  );
};

export default ActiveGroup;
