'use client';
import React from 'react';
import { X } from 'lucide-react';
import useModalStore from '@/store/useModalStore';
import IconButton from '../Common/Button/IconButton';
import Image from 'next/image';

function DropCompletedModal({ chat }) {
  const { closeModal } = useModalStore((state) => state);

  const handleCloseModal = () => {
    closeModal();
  };

  if (!show) return null;

  return (
    <div
      tabIndex="-1"
      aria-hidden="true"
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
    >
      <div className="relative max-h-[90vh] w-full max-w-sm">
        <div className="rounded-lg bg-maastrichtBlue-1000 p-6 shadow-lg">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-md mt-1 cursor-pointer font-semibold leading-none tracking-wide text-steelTeal-1000 hover:text-steelTeal-1000">
              {chat?.rainDrop?.message}
            </h3>
            <IconButton onClick={handleCloseModal} className="h-6 w-6 min-w-6">
              <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
            </IconButton>
          </div>
          <div className="text-center">
            <div className="mb-3">
              <Image
                src={dropDetails?.user?.profileImage || '/default-avatar.png'}
                alt={dropDetails?.user?.username || 'User'}
                width={80}
                height={80}
                className="rounded-full"
              />
            </div>
            <h5 className="text-white text-lg font-semibold">
              {dropDetails?.user?.username}
            </h5>
            <p className="text-white mt-2 text-sm">Drop is completed</p>
          </div>
          <div className="mt-6 flex justify-center">
            <button
              onClick={onViewHistory}
              className="text-white rounded-lg bg-primary-1000 px-4 py-2 transition-all duration-300 hover:bg-primary-800"
            >
              View History
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DropCompletedModal;
