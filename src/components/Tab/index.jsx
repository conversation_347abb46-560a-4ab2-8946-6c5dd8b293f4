'use client';

import { useLayoutEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

function TabButton({ tabs, defaultActiveTab, className, onTabClick }) {
  const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id);
  const pathname = usePathname();

  useLayoutEffect(() => {
    const activeRoute = pathname.split('/')[1] || 'casino';
    setActiveTab(activeRoute);
  }, [pathname]);

  return (
    <div
      className={`bg-cetaceanBlue-900 flex gap-2 rounded-lg p-1  ${className}`}
    >
      {tabs.map((tab) => (
        <Link
          key={tab.id}
          href={tab.href || '#'}
          onClick={() => {
            setActiveTab(tab.id);
            if (onTabClick) onTabClick();
          }}
          className={`border-white-110 flex items-center justify-center rounded-md border px-5 py-2 text-base font-medium transition-all ${
            activeTab === tab.id
              ? 'bg-primary-700 text-white-1000'
              : 'hover:bg-cetaceanBlue-800 text-white-750'
          }`}
        >
          {tab.icon && <tab.icon className="mr-2 h-5 w-5" />}
          {tab.label}
        </Link>
      ))}
    </div>
  );
}

export default TabButton;
