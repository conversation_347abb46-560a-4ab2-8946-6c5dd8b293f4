'use client';

import GreenbarBonus from '@/app/game/GreenbarBonus';
import WalletIcon from '@/assets/icons/WalletIcon';
import useChatSection from '@/hooks/useChatSection';
import useHelperHook from '@/hooks/useHelperHook';
import { useSocketManager } from '@/hooks/useSocketManager';
import { useUserProfileQuery } from '@/reactQuery/authQuery';
import { useGetBannersQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useAuthTab from '@/store/useAuthTab';
import useChatStore from '@/store/useChatStore';
import useGameStore from '@/store/useGameStore';
import useGeneralStore from '@/store/useGeneralStore';
import useModalStore from '@/store/useModalStore';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import chat from '../../assets/icons/chat.png';
import brandLogo from '../../assets/images/logo/brand-logo.svg';
import Auth from '../Auth';
import ChatHeader from '../ChatWindow/ChatHeader';
import PrimaryButton from '../Common/Button/PrimaryButton';
import PrimaryButtonOutline from '../Common/Button/PrimaryButtonOutline';
import WalletMenu from '../WalletMenu';
function Header() {
  const { isAuthenticated, setUserDetails } = useAuthStore((state) => state);
  const router = useRouter();
  const { gameId } = useParams();
  const { setSection } = useChatSection();
  const { clearModals, openModal } = useModalStore((state) => state);

  const { setShowHomePopup } = useGeneralStore((state) => state);
  const { clearUserAuth, logout } = useHelperHook();
  // const [showChatHeader, setShowChatHeader] = useState(false)
  const [showChatIcon, setShowChatIcon] = useState(true);
  useSocketManager();
  const {
    setShowChat,
    showChatHeader,
    setShowChatHeader,
    setChatHeaderActiveTab,
  } = useChatStore();

  const { setIFrameHeight } = useGameStore();
  const pathname = usePathname();
  useEffect(() => {
    window.addEventListener('logout', async (event) => {
      console.log('event', event);
      // if (!localStorage.getItem('isAuthenticated')) {
      setSection('PublicChat');
      clearUserAuth();
      localStorage.clear();
      clearModals();
      router.push('/');
      // }
    });
  }, []);

  const { setSelectedTab } = useAuthTab((state) => state);
  const { components: modalComponents } = useModalStore((state) => state);

  const {
    openMenu,
    setOpeMenu,
    openChat,
    setOpenChat,
    setBanners,
    setActiveMenu,
    setToggleSideMenu,
  } = useGeneralStore((state) => state);

  const { data: bannersData } = useGetBannersQuery({ enabled: true });
  const [showWallet, setShowWallet] = useState(false);

  useEffect(() => {
    if (bannersData) setBanners(bannersData);
  }, [bannersData]);

  const { data } = useUserProfileQuery({
    enabled: isAuthenticated,
  });

  useEffect(() => {
    if (data) setUserDetails(data);
  }, [data, setUserDetails]);

  useEffect(() => {
    // setIFrameHeight('calc(100vh - 80px)');
    if (pathname?.split('/')?.includes('originals')) {
      const handleMessage = (event) => {
        const { eventName, data } = event.data || {};

        if (eventName === 'GAME_CONTAINER_HEIGHT') {
          setIFrameHeight(`${data?.gameContainerHeight}px`);
        }
      };

      window.addEventListener('message', handleMessage);

      // Cleanup
      return () => {
        window.removeEventListener('message', handleMessage);
      };
    } else {
      if (window?.innerWidth < 1024) {
        setIFrameHeight('calc(100vh - 80px) ');
      } else {
        setIFrameHeight('calc(100vh - 140px)');
      }
    }
  }, [pathname]);
  useEffect(() => {
    const handleResize = () => {
      const isWideScreen = window.matchMedia('(min-width: 1280px)').matches;
      const laptop = window.matchMedia('(min-width: 1024px)').matches;
      setShowChatIcon(!isWideScreen);
      setOpenChat(isWideScreen);
      setOpenChat(laptop);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [setOpenChat]);

  return (
    <>
      {showChatHeader && (
        <div
          className={`header-blur fixed left-0 top-0 z-[49] mb-10 flex h-[3.75rem] w-full items-center justify-between bg-oxfordBlue-1000  px-3 transition-all duration-300 ease-in-out lg:z-40  lg:ml-[14.75rem] lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)] lg:shadow-header`}
        >
          <ChatHeader setShowChatHeader={setShowChatHeader} />
        </div>
      )}
      <header
        className={`${showChatHeader ? 'hidden' : ''} ${isAuthenticated ? '' : '!z-[42]'} ${openChat ? 'lg:mr-[20.5rem] lg:w-[calc(100%-14.75rem-20.5rem)]' : 'lg:mr-[0rem] lg:w-[calc(100%-14.75rem-0rem)]'} header-blur fixed left-0 top-0 z-[52] mb-10 flex h-[3.75rem] w-full items-center  justify-between bg-oxfordBlue-1000 px-3 transition-all duration-300  ease-in-out lg:z-40 lg:ml-[14.75rem] lg:pr-12 lg:shadow-header`}
      >
        <Link href="/" className="inline-block lg:hidden">
          <Image
            src={brandLogo}
            width={100}
            height={100}
            className="min-w-[5rem] max-w-[5rem]"
            onClick={() => {
              if (window && window.innerWidth < 946) {
                setActiveMenu('/');
                setOpenChat(false);
              }
            }}
          />
        </Link>

        <div className=" relative m-[10px] w-full max-w-[62dvw] sm:ml-0 lg:hidden">
          {isAuthenticated && gameId && <GreenbarBonus />}
        </div>
        {!isAuthenticated && (
          <div className="flex grow items-center justify-end">
            <div className="flex w-full items-center justify-end gap-2">
              <PrimaryButtonOutline
                className="max-md:min-h-8 max-md:px-4 max-md:text-sm"
                onClick={() => {
                  localStorage.setItem('activeTab', 0);
                  setSelectedTab(0);
                  openModal(<Auth />);
                }}
              >
                Login
              </PrimaryButtonOutline>
              <PrimaryButton
                className="max-md:min-h-8 max-md:px-4 max-md:text-sm"
                onClick={() => {
                  localStorage.setItem('activeTab', 1);
                  setSelectedTab(1);
                  openModal(<Auth />);
                }}
              >
                Register
              </PrimaryButton>
            </div>
          </div>
        )}
        <div className="flex items-center gap-2">
          <div className="relative inline-block lg:hidden">
            {isAuthenticated && (
              <button
                onClick={() => {
                  setShowWallet((prev) => !prev);
                  setOpeMenu(false);
                }}
                className="flex h-7 w-7 items-center justify-center"
                type="button"
              >
                <WalletIcon className="h-7 w-7" />
              </button>
            )}
            {showWallet && (
              <WalletMenu
                showWallet={showWallet}
                onClose={() => setShowWallet(false)}
              />
            )}
          </div>

          {showChatIcon && (
            <button
              className="flex h-7 w-7 items-center justify-center lg:hidden"
              onClick={() => {
                setShowHomePopup(true);
                setOpeMenu(false);
                setShowWallet(false);
                setShowChatHeader(!showChatHeader);
                setChatHeaderActiveTab('lobby');
              }}
              type="button"
            >
              <Image
                src={chat}
                className="h-7 w-7 transition-all duration-300"
              />
            </button>
          )}

          <button
            type="button"
            onClick={() => {
              // Prevent sidebar opening if any modal is open
              if (modalComponents.length > 0) {
                return;
              }
              setOpeMenu(!openMenu);
              setToggleSideMenu(false);
              setShowWallet(false);
            }}
            className="flex h-7 w-7 items-center justify-center lg:hidden"
          >
            <img
              src="https://flatironsmineralclub.org/wp-content/themes/fmc/img/menu_icon.png"
              alt="Menu"
              className="h-7 w-7 object-contain"
            />
          </button>
        </div>
      </header>
    </>
  );
}

export default Header;
