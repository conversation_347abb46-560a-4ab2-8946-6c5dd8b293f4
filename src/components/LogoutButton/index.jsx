'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import LogoutIcon from '@/assets/icons/Logout';
import useHelperHook from '@/hooks/useHelperHook';
import { removeAccessToken } from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query'; // Import queryClient hook
import useModalStore from '@/store/useModalStore';
import LogoutGoldenIcon from '@/assets/icons/LogoutGoldenIcon';
function LogoutButton({ onClose }) {
  const { logout,isLoading } = useHelperHook();
  const router = useRouter();
  const queryClient = useQueryClient(); // Get the query client
  const { clearModals } = useModalStore((state) => state);

  const handleLogout = () => {
    if(!isLoading){
      logout();
      clearModals()
      // window.dispatchEvent(new Event('logout'));
      router.push('/');
      if (onClose) onClose();
    }
  };

  return (
    <button
      type="button"
      className="flex w-full items-center justify-start gap-2.5 rounded-[0.375rem] p-2.5 hover:bg-nav-gradient"
      onClick={handleLogout}
    >
      <LogoutGoldenIcon className="h-5 w-5 fill-steelTeal-1000" />
      <span className="text-base font-normal text-white-1000">Log Out</span>
    </button>
  );
}

export default LogoutButton;
