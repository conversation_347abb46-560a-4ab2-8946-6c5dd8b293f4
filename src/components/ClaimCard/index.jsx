'use client'

import useModalStore from "@/store/useModalStore";
import IconButton from "../Common/Button/IconButton";
import { X } from "lucide-react";

const ClaimCard = ({bonusAmount, bonusAmountGC}) => {
    const { clearModals } = useModalStore((state) => state);

    return (
        <div className="w-full fixed left-0 right-0 top-0 z-50 bg-black-900 flex items-center justify-center h-full">
            <div className="max-w-[701px] w-full bg-bonusBg bg-center bg-cover rounded-xl relative">

                <IconButton
                    onClick={clearModals}
                    className="h-6 w-6 min-w-6 absolute z-[3] right-4 top-4"
                >
                    <X className="h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
                </IconButton>

                <div className="flex">
                    <div className="py-[60px] max-sm:py-8 pl-10 max-sm:px-4 pr-2 w-full shrink-0 relative z-[1] max-sm:max-w-full max-sm:flex max-sm:flex-col max-sm:justify-center max-sm:items-center max-sm:text-center">
                        <h4 className="text-2xl max-sm:text-base font-normal text-white max-sm:!leading-none">You’ve Claimed Your Green Bar Bonus!</h4>
                        <h2 className="text-5xl max-sm:text-2xl font-bold text-green-400">Congratulations!</h2>
                        <div className="flex flex-col gap-3">
                            <p className="text-base max-sm:text-xs font-normal text-white max-w-[300px] max-sm:max-w-60 w-full leading-5 max-sm:leading-4 mt-1">Your GBB has been successfully added to your account.
                                Use it soon to boost your play and get closer to your next Mystery Box!
                            </p>
                            <p className="text-base max-sm:text-xs font-bold text-green-400 max-w-[300px] max-sm:max-w-60 w-full leading-5 max-sm:leading-4 mt-1">You’ve received  {bonusAmount && ` +${bonusAmount} SC`}
                            {bonusAmountGC && `${bonusAmount ? ' and' : ''} +${bonusAmountGC} GC`} match bonus!</p>
                            {/* <p className="text-base max-sm:text-xs font-normal text-white max-w-[300px] max-sm:max-w-60 w-full leading-5 max-sm:leading-4 mt-1">It will be automatically applied to your next deposit.</p> */}
                            <p className="text-base max-sm:text-xs font-light  max-w-[300px] max-sm:max-w-60 w-full leading-5 max-sm:leading-4 mt-1">Stay active and keep an eye out for your next one!
                            </p>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    )
}
export default ClaimCard