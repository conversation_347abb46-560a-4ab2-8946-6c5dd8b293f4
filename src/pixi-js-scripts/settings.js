import useSpinWheelStore from '@/store/useSpinWheelStore';

const basePath = '/assets/pixi-assets/';
const { spinWheelData } = useSpinWheelStore.getState();
// const spinWheelData = {}

export const path = {
  imagePath: `${basePath}images/`,
  spriteSheetsPath: `${basePath}spriteSheets/`,
};

export const settings = {
  originalWidth: 400,
  originalHeight: 400,
  backgroundColor: 0x00000,
  backgroundAlpha: 0,
  div: () => document.getElementById('pixi-spin-wheel'),
};

export const spinWheelGCSettings = {
  fontFamily: 'Arial',
  fontSize: 48,
  fill: 0x201010,
  align: 'center',
  fontWeight: 'bolder',
};

export const spinWheelSCSettings = {
  fontFamily: 'Arial',
  fontSize: 36,
  fill: 0x201010,
  align: 'center',
  fontWeight: 'bolder',
};

// values are in reversed order and 16 is 0 ex:( 0, 15 , 14 , 13 , 12 , 11 ,10 ,9 ,8 ,7 ,6 ,5 ,4 ,3 ,2 ,1 ).
export const wheelContentValues = spinWheelData;

export const aspectRatio = settings.originalWidth / 1550;
