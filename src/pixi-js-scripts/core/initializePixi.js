let currentPixiApp = null;
export let pixiApp;

const loadAssetsPixi = () =>
  import('./assetsLoad.js')
    .then(({ default: loadAssets }) => loadAssets && loadAssets())
    .catch((error) => {
      console.error('Error during assets loading in Pixi:', error);
      throw error;
    });

const loadPixiApp = () =>
  import('./appPixi.js')
    .then(({ default: createPixiApp }) => {
      if (currentPixiApp) {
        currentPixiApp.destroyPixi(true);
      }

      pixiApp = createPixiApp();
      currentPixiApp = pixiApp;

      return pixiApp;
    })
    .catch((error) => {
      console.error('Error during Pixi.js initialization:', error);
      throw error;
    });

const loadGameUI = () =>
  import('./gameUI.js')
    .then(({ createUI }) => {
      createUI();
    })
    .catch((error) => {
      console.error('Error loading gameUI.js:', error);
      throw error;
    });

const gameTickerStart = () =>
  import('./gameLogic.js')
    .then(({ startGameTicker }) => {
      startGameTicker();
    })
    .catch((error) => {
      console.error('Error loading gameLogic.js:', error);
      throw error;
    });

export const loadPixiAssets = () =>
  loadAssetsPixi()
    .then(() => console.log())
    .catch((error) => {
      console.error('Error loading pixi assets:', error);
    });

export const destroyPixiApp = () => {
  if (currentPixiApp) {
    currentPixiApp.destroyPixi(true);
    currentPixiApp = null;
  }
};

export const init = () =>
  loadAssetsPixi()
    .then(() => loadPixiApp())
    // .then(() => currentPixiApp.resizePixiApp(700, 500))
    .then(() => loadGameUI())
    .then(() => gameTickerStart())
    .catch((error) => {
      console.error('Error during Pixi initialization:', error);
    });
