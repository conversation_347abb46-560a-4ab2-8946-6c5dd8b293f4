export function spriteSet(spriteName, textureName, containerName) {
  spriteName.texture = textureName;
  containerName.addChild(spriteName);
  spriteName.anchor.set(0.5);
  return spriteName;
}

export function animatedSpriteSet(
  arrayName,
  containerName = rootContainer,
  positionX = 0,
  positionY = 0,
  scaleX = 1,
  scaleY = 1,
  visible = true,
  alpha = 1,
) {
  let sprite = new PIXI.AnimatedSprite(arrayName);
  containerName.addChild(sprite);
  sprite.position.set(positionX, positionY);
  sprite.scale.set(scaleX, scaleY);
  sprite.visible = visible;
  sprite.alpha = alpha;
  sprite.anchor.set(0.5);
  return sprite;
}

// Bg image will scale up or down to fill the background completely
export function backgroundScale(widthBG, heightBG) {
  let scale = 1;

  if (originalWidth / widthBG < originalHeight / heightBG) {
    scale = originalHeight / heightBG;
  } else {
    scale = originalWidth / widthBG;
  }

  return scale;
}

// array from the spriteSheet
// --> pushToArray(promiseAssets.chips.data.frames, promiseAssets.ImgName.textures[i])
export function pushToArray(objectFrames, animArray) {
  let variab = sortAnimation(Object.keys(objectFrames));
  for (let i of variab) {
    arreyName.push(animArray);
  }
}

function sortAnimation(unsortedArray) {
  unsortedArray.sort((a, b) => {
    if (a < b) {
      return -1;
    }
    if (a > b) {
      return 1;
    }
    return 0;
  });

  unsortedArray.sort((a, b) => {
    if (a.length < b.length) {
      return -1;
    }
    if (a.length > b.length) {
      return 1;
    }
    return 0;
  });

  return unsortedArray;
}
