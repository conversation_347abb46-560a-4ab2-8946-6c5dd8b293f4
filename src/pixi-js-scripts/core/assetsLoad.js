import { path } from '../settings.js';

let assetsLoaded = false;

async function loadAssets() {
  PIXI.Assets.addBundle('Images', {
    wheel: path.imagePath + '/wheel.png',
    marker: path.imagePath + '/marker.png',
    sc: path.imagePath + '/rewardCash.png',
    gc: path.imagePath + '/rewardCoin.png',
    banner: path.imagePath + '/banner.png',
    claim: path.imagePath + '/claim.png',
    button: path.imagePath + '/button.png',
  });

  await PIXI.Assets.loadBundle('Images');
  assetsLoaded = true;
}

export default loadAssets;
