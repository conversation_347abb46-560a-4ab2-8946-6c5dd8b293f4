import * as corePixiInit from './core/initializePixi.js';

export const pixiApplicationInit = () => corePixiInit.init();

export const pixiAssetsLoad = () => corePixiInit.loadPixiAssets();

export const pixiApplicationDestroy = () => corePixiInit.destroyPixiApp();

// document
//     .getElementById("assetsLoadButton")
//     .addEventListener("click", () => pixiAssetsLoad());

// document
//     .getElementById("pixiLoadButton")
//     .addEventListener("click", () => pixiApplicationInit());

// document
//     .getElementById("backButton")
//     .addEventListener("click", () => pixiApplicationDestroy());
