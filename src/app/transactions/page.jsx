'use client';

import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Select from 'react-select';
import { subDays } from 'date-fns';
import { useState, useEffect } from 'react';
import { usePlayerTransactions } from '@/hooks/useTransactions';
import { coinFilterOption, transactionFilterOptions } from '@/config/general';
import {
  convertTo24HourTime,
  formatDate,
  formatDateMDY,
  formatDateMDYAnd24Hours,
} from '@/utils/customizedDate';
import Loader from './components/Loader';
import Pagination from './components/Pagination';

const ITEMS_PER_PAGE = 10;
const INITIAL_DATE_RANGE = {
  start: formatDate(subDays(new Date(), 30)),
  end: formatDate(new Date()),
};

const TableHeaders = [
  { id: 'gameId', label: 'Game ID' },
  { id: 'gameName', label: 'Game Name' },
  { id: 'dateTime', label: 'Date & Time' },
  { id: 'amount', label: 'Amount' },
  // { id: 'status', label: 'Status' },
  { id: 'type', label: 'Type' },
  { id: 'coinType', label: 'Coin Type' },
];

const Type = {
  bet: 'Bet',
  win: 'Win',
  lost: 'Lost',
  'green-bonus': 'Green Bonus',
};
const customSelectStyles = {
  control: (base) => ({
    ...base,
    backgroundColor: '#102A3E',
    borderColor: '#1A3A4E',
    color: 'white',
    borderRadius: '6px',
  }),
  option: (base, state) => ({
    ...base,
    backgroundColor: state.isSelected ? '#1A3A4E' : '#102A3E',
    color: 'white',
    ':hover': { backgroundColor: '#1A3A4E' },
  }),
  singleValue: (base) => ({
    ...base,
    color: 'white',
  }),
  menu: (base) => ({
    ...base,
    backgroundColor: '#102A3E',
  }),
};

function DateInput({ label, selected, onChange, minDate, maxDate }) {
  return (
    <div className="max-w-[250px w-full]">
      {/* <label className="text-white mb-2 block text-sm font-semibold">
        {label}
      </label> */}
      <DatePicker
        selected={selected}
        onChange={onChange}
        showYearDropdown
        dateFormat="dd/MM/yyyy"
        minDate={minDate}
        maxDate={maxDate || new Date()}
        // className="text-white w-full rounded-md border border-gray-700 bg-transparent p-2"
        className="text-white w-full rounded-md border border-solid border-maastrichtBlue-1000  bg-maastrichtBlue-1000 p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100"
      />
    </div>
  );
}

function SelectInput({ label, value, onChange, options }) {
  return (
    <div className="flex w-full max-w-[250px] flex-col space-y-2">
      <div className="flex flex-col space-y-2">
        {/* <label className="text-darkGray-1000 text-sm font-medium">
          {label}
        </label> */}
        <Select
          value={value}
          onChange={onChange}
          options={options}
          styles={customSelectStyles}
          placeholder="All"
          isSearchable
          components={{ IndicatorSeparator: () => null }}
          className="form-input-select"
          classNamePrefix="form-input"
        />
      </div>
    </div>
  );
}

function TransactionTable({ transactions, isLoading }) {
  if (isLoading) return <Loader />;
  return (
    <table className="min-w-full divide-y divide-gray-700 bg-transparent">
      <thead>
        <tr>
          {TableHeaders.map(({ id, label }) => (
            <th
              key={id}
              className="text-primary-400 whitespace-nowrap px-4 py-3 text-start text-sm font-bold capitalize"
            >
              {label}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {transactions?.length > 0 ? (
          transactions.map((transaction) => (
            <tr key={transaction.transactionId}>
              <td className="text-white px-4 py-2">
                {transaction?.gameId || transaction?.id}
              </td>
              <td className="text-white px-4 py-2">
                {transaction?.MasterCasinoGame?.name}
              </td>
              <td className="text-white px-4 py-2">
                {formatDateMDYAnd24Hours(transaction?.createdAt)}
              </td>

              <td className="text-white px-4 py-2">
                {Number((transaction?.amount || 0).toFixed(2)).toLocaleString() || 0}
              </td>
              {/* <td className="px-4 py-2 text-red-500">
                {transaction?.status || 'N/A'}
              </td> */}
              <td
                className={`px-4 py-2 ${
                  transaction?.actionType === 'win'
                    ? 'text-emerald-400'
                    : transaction?.actionType === 'lost'
                      ? 'text-red-500'
                      : transaction?.actionType === 'bet'
                        ? 'text-white'
                        : transaction?.actionType === 'green-bonus'
                          ? 'text-emerald-400'
                          : 'text-gray-700'
                }`}
              >
                {Type?.[transaction?.actionType] || transaction?.actionType}
              </td>
              <td className="text-white px-4 py-2">
                {transaction?.amountTypeName}
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td
              colSpan={TableHeaders.length}
              className="px-4 py-2 text-center text-gray-400"
            >
              No Record Found!
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}

function Transaction() {
  const [dateRange, setDateRange] = useState(INITIAL_DATE_RANGE);
  const [page, setPage] = useState(1);
  const [payload, setPayload] = useState({
    page: 1,
    limit: ITEMS_PER_PAGE,
    startDate: formatDateMDY(INITIAL_DATE_RANGE.start),
    endDate: formatDateMDY(INITIAL_DATE_RANGE.end),
  });
  const [filter, setFilter] = useState(null);
  const [coinType, setCoinType] = useState(null);

  const { transactions, isLoading, refetch } = usePlayerTransactions(payload);

  useEffect(() => {
    refetch();
  }, [payload, refetch]);

  const handleDateChange = (date, type) => {
    setDateRange((prev) => ({ ...prev, [type]: date }));
    setPayload((prev) => ({
      ...prev,
      page: 1,
      startDate: type === 'start' ? formatDateMDY(date) : prev.startDate,
      endDate: type === 'end' ? formatDateMDY(date) : prev.endDate,
    }));
    setPage(1);
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
    setPayload((prev) => ({ ...prev, page: newPage }));
  };

  const handleFilterChange = (selected) => {
    setFilter(selected);
    setPayload((prev) => ({
      ...prev,
      page: 1,
      purpose: selected?.value || null,
    }));
  };

  const handleCoinFilterChange = (selected) => {
    setCoinType(selected);
    setPayload((prev) => ({
      ...prev,
      page: 1,
      amountType: selected?.value || null,
    }));
  };

  const totalPages = Math.ceil((transactions?.count || 0) / ITEMS_PER_PAGE);

  return (
    <div className="rounded-xl border-2 border-solid border-maastrichtBlue-1000 bg-black-1000 px-5 py-[50px] max-xl:py-6 max-xxs:px-3">
      <div className="flex flex-col md:flex-row md:space-x-6 max-md:space-x-0 max-md:space-y-6">
        <DateInput
          label="Start Date"
          selected={dateRange.start}
          onChange={(date) => handleDateChange(date, 'start')}
          maxDate={dateRange.end}
        />
        <DateInput
          label="End Date"
          selected={dateRange.end}
          onChange={(date) => handleDateChange(date, 'end')}
          minDate={dateRange.start}
        />
        <SelectInput
          label="Filter"
          value={filter}
          onChange={handleFilterChange}
          options={transactionFilterOptions}
        />
        <SelectInput
          label="Coin Filter"
          value={coinType}
          onChange={handleCoinFilterChange}
          options={coinFilterOption}
        />
      </div>

      <div className="mt-6 w-full overflow-x-auto">
        <TransactionTable
          transactions={transactions?.rows}
          isLoading={isLoading}
        />
        {totalPages > 1 && (
          <Pagination
            className="mt-4"
            totalPages={totalPages}
            currentPage={page}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
}

export default Transaction;
