import React from "react";

const Loader = ({size=64,color="text-secondary-500"}) => {
  return (
      <div className=" flex flex-auto flex-col justify-center items-center p-4 md:p-5">
        <div className="flex justify-center">
          <div
            className={`animate-spin inline-block border-[4px] border-current border-t-transparent ${color} rounded-full`}
            role="status"
            aria-label="loading"
            style={{
              width: `${size}px`,
              height: `${size}px`,
            }}
          >
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      </div>
  );
};

export default Loader;
