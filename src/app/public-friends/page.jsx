'use client';

import FriendChatIcon from '@/assets/icons/FriendChatIcon';
import TabContainer from '@/components/Friends-PublicGroup/TabContainer';
import useAuthStore from '@/store/useAuthStore';

function PublicFriendOrGroup() {
  const { isAuthenticated, userWallet, coin } = useAuthStore((state) => state);

  return (
    <section
      className={`mb-10 rounded-lg  shadow-container ${isAuthenticated ? 'md:mt-10' : ''} `}
    >
      <div className="mb-6 flex items-center gap-3 ">
        <FriendChatIcon fill={'white'} />
        <h6 className="text-white text-xl font-bold capitalize">
          Friends And Groups
        </h6>
      </div>
      <div>
        <TabContainer />
      </div>
    </section>
  );
}

export default PublicFriendOrGroup;
