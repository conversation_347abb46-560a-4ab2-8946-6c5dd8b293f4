'use client';

import React, { useEffect, useState } from 'react';
import ShoppigCartIcon from '@/assets/icons/ShoppigCartIcon';
import DeliveryAddressForm from '@/components/Inventory/DeliveryAddressForm';
import OrderSummary from '@/components/Inventory/OrderSummary';
import OrderSuccess from '@/components/Inventory/OrderSuccess';
import useAuthStore from '@/store/useAuthStore';

function CheckoutProcessPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const { userDetails } = useAuthStore((state) => state);

  const nextStep = () => setCurrentStep((prevStep) => prevStep + 1);
  const previousStep = () => setCurrentStep((prevStep) => prevStep - 1);

  // useEffect(() => {
  //   if (userDetails?.userMeta?.defaultDeliveryAddress && currentStep === 1) {
  //     setCurrentStep(2);
  //   }
  // }, [userDetails]);

  return (
    <div>
      <div className="relative m-[0_auto] mt-7 flex w-full max-w-[600px] items-center justify-between">
        <div className="flex flex-col items-center justify-between gap-3 text-white-1000">
          <span
            className={`flex h-10 w-10 items-center justify-center rounded-full ${currentStep === 1 ? 'bg-scarlet-900' : 'bg-steelTeal-1000'} text-white-1000`}
          >
            1
          </span>
          Delivery address
        </div>
        <div className='flex flex-col items-center justify-between gap-3 text-steelTeal-1000 before:absolute before:left-[30%] before:top-[25%] before:h-[3px] before:w-[calc(50%_-_118px)] before:-translate-x-[54%] before:-translate-y-[46%] before:bg-steelTeal-1000 before:content-[""] after:absolute after:right-[12%] after:top-[25%] after:h-[3px] after:w-[calc(50%_-_116px)] after:-translate-x-[11%] after:-translate-y-[46%] after:bg-steelTeal-1000 after:content-[""] max-md:hidden max-md:before:hidden max-md:after:hidden'>
          <span
            className={`flex h-10 w-10 items-center justify-center rounded-full ${currentStep === 2 ? 'bg-scarlet-900' : 'bg-steelTeal-1000'} text-white-1000`}
          >
            2
          </span>
          Order Summary
        </div>
        <div className="flex flex-col items-center justify-between gap-3 text-steelTeal-1000 max-md:hidden">
          <span
            className={`flex h-10 w-10 items-center justify-center rounded-full ${currentStep === 3 ? 'bg-scarlet-900' : 'bg-steelTeal-1000'} text-white-1000`}
          >
            3
          </span>
          Order Success
        </div>
      </div>

      {currentStep === 1 && (
        <DeliveryAddressForm
          onNext={nextStep}
          userMeta={userDetails?.userMeta}
        />
      )}
      {currentStep === 2 && (
        <OrderSummary
          onNext={nextStep}
          onPrevious={previousStep}
          userMeta={userDetails?.userMeta}
        />
      )}
      {currentStep === 3 && <OrderSuccess />}
    </div>
  );
}

export default CheckoutProcessPage;
