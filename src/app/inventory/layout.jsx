import React from 'react';
import ShoppigCartIcon from '@/assets/icons/ShoppigCartIcon';
import Link from 'next/link';
import Cart from '@/components/Inventory/Cart';

function InventoryLayout({ children }) {
  return (
    <div>
      <section className="section-blur rounded-[0.625rem] border-2 border-cetaceanBlue-1000 bg-maastrichtBlue-200 px-5 pb-10 pt-3.5">
        <div className="flex flex-wrap items-center gap-6 max-sm:flex-col">
          <h3 className="mr-auto flex grow items-center gap-4 text-xl font-normal leading-none text-white-1000 md:text-2xl max-sm:w-full">
            <ShoppigCartIcon className="h-5 w-5 fill-white-1000" />
            <Link href="/inventory">
              <span>Inventory</span>
            </Link>
          </h3>
        </div>
        {children}
      </section>
      <Cart />
    </div>
  );
}

export default InventoryLayout;
