'use client';

import React, { useState, useEffect, useCallback } from 'react';
import FavoriteStrokeIcon from '@/assets/icons/Favorite-Stroke';
import FilterIcon from '@/assets/icons/FilterIcon';
import SearchIcon from '@/assets/icons/SearchIcon';
import ShoppigCartIcon from '@/assets/icons/ShoppigCartIcon';
import useInventories from '@/hooks/useInventories';
import ProductCard from '@/components/Inventory/ProductCard';
import useCartStore from '@/store/useCartStore';
import Cart from '@/components/Inventory/Cart';
import useCart from '@/hooks/useCartHook';
import Link from 'next/link';
import useAuthStore from '@/store/useAuthStore';
import { fixedTo4DecimalPlaces } from '@/utils/helper';

function IconButton({ icon: Icon, label, className, onClick, badge }) {
  return (
    <button
      className={`group relative flex h-11 justify-center gap-2 rounded-[0.625rem] bg-oxfordBlue-1000 p-3 transition-all duration-300 hover:bg-primary-1000 ${className}`}
      onClick={onClick}
    >
      {Icon && (
        <Icon className="m-auto h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
      )}
      <span className="w-max">{label}</span>
      {badge && (
        <div className="absolute -right-1 -top-1 flex h-[17px] w-[17px] items-center justify-center rounded-full bg-scarlet-900 text-xs group-hover:bg-white-1000 group-hover:text-scarlet-900">
          {badge}
        </div>
      )}
    </button>
  );
}

function Inventory() {
  const [searchQuery, setSearchQuery] = useState('');
  const { cart, toggleShowCart, showCart } = useCartStore((state) => state);
  const { userWallet, userDetails } = useAuthStore((state) => state);
  const { cartItems } = useCart();

  const {
    refetch,
    inventories,
    inventoriesLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInventories(searchQuery);

  const handleScroll = useCallback(() => {
    if (
      window.innerHeight + document.documentElement.scrollTop !==
        document.documentElement.offsetHeight ||
      !hasNextPage ||
      isFetchingNextPage
    ) {
      return;
    }
    fetchNextPage();
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <div>
      <div className="m-[0_auto] max-w-[944px]">
        <div className="my-8 flex items-center justify-between gap-[0.375rem] sm:gap-3">
          <div className="flex h-11 w-full max-w-[30.875rem] items-center gap-4 rounded-[0.625rem] bg-oxfordBlue-1000 px-2 py-1">
            <SearchIcon />
            <input
              type="text"
              className="h-full w-full bg-transparent text-sm font-normal placeholder:text-white-1000"
              placeholder="Search by product name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex w-full items-center justify-end gap-[0.375rem] sm:max-w-[15.75rem] sm:gap-3">
            <Link
              href={'/inventory/orders'}
              className={`group relative flex h-11 w-max min-w-max justify-center gap-2 rounded-[0.625rem] bg-oxfordBlue-1000 p-3 transition-all duration-300 hover:bg-primary-1000 `}
            >
              My Orders
            </Link>
            <IconButton icon={FilterIcon} label="Filter" />
            <IconButton
              icon={ShoppigCartIcon}
              label="Cart"
              badge={cartItems?.rows?.length}
              onClick={() => toggleShowCart()}
            />
          </div>
        </div>
        <div className="max-w-[30.875rem flex w-full items-center rounded-[0.625rem] bg-oxfordBlue-1000 px-4 py-2 text-green-1000">
          <span>Total Redeemable Coins: </span>
          <span>&nbsp; {userDetails?.userWallet?.wsc} SC</span>
        </div>

        <div className="grid grid-cols-4 gap-12 max-xxl:grid-cols-3 max-xxl:gap-6 max-xl:grid-cols-4 max-md:grid-cols-3 max-sm:grid-cols-2 max-xxs:grid-cols-1 ">
          {inventories?.pages?.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </div>
  );
}

export default Inventory;
