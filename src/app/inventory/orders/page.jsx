'use client';

import React from 'react';
import Image from 'next/image';
import FilterIcon from '@/assets/icons/FilterIcon';
import ShoppigCartIcon from '@/assets/icons/ShoppigCartIcon';
import useCartStore from '@/store/useCartStore';
import {
  useAllOrdersQuery,
  useCancelOrderMutation,
} from '@/reactQuery/inventoryQuery';
import useCart from '@/hooks/useCartHook';

function IconButton({ icon: Icon, label, className, onClick, badge }) {
  return (
    <button
      type="button"
      className={`group relative flex h-11 justify-center gap-2 rounded-[0.625rem] bg-oxfordBlue-1000 p-3 transition-all duration-300 hover:bg-primary-1000 ${className}`}
      onClick={onClick}
    >
      <Icon className="m-auto h-5 w-5 fill-steelTeal-1000 transition-all duration-300 group-hover:fill-white-1000" />
      <span className="max-lg:hidden">{label}</span>
      {badge && (
        <div className="absolute -right-1 -top-1 flex h-[17px] w-[17px] items-center justify-center rounded-full bg-scarlet-900 text-xs group-hover:bg-white-1000 group-hover:text-scarlet-900">
          {badge}
        </div>
      )}
    </button>
  );
}

function MyOrders() {
  const { data: allOrders, loading: ordersLoading } = useAllOrdersQuery({
    enabled: true,
  });
  const { cartItems } = useCart({});
  const { cart, toggleShowCart, showCart } = useCartStore((state) => state);
  const { mutate: cancelOrder, isLoading } = useCancelOrderMutation();

  const handleCancelOrder = (inventoryCartId) => {
    cancelOrder({ inventoryCartId });
  };

  return (
    <div>
      <div className="m-[0_auto] max-w-[944px]">
        <div className="my-8 flex items-center justify-between gap-[0.375rem] sm:gap-3">
          <h3 className="mr-auto flex grow items-center gap-4 text-xl font-normal leading-none text-white-1000 md:text-2xl max-sm:w-full">
            {/* <ShoppigCartIcon className="h-5 w-5 fill-white-1000" /> */}
            <span>My Orders</span>
          </h3>

          <div className="flex w-full items-center justify-end gap-[0.375rem] sm:max-w-[15.75rem] sm:gap-3">
            <IconButton icon={FilterIcon} label="Filter" />
            <IconButton
              icon={ShoppigCartIcon}
              label="Cart"
              badge={cartItems?.rows?.length}
              onClick={() => toggleShowCart()}
            />
          </div>
        </div>
        <div className="max-h-[70vh] overflow-y-auto p-4">
          {allOrders?.rows?.map(
            ({
              inventory,
              price,
              orderStatus,
              redeemApprovalStatus,
              deliveryStatus,
              inventoryCartId,
              orderConfirmedAt,
              estimatedDeliveryDate,
              quantity,
            }) => (
              <div
                key={inventoryCartId}
                className="mb-6 flex items-center justify-between gap-4 max-sm:flex-col"
              >
                <div className="max flex w-full  items-center gap-5 max-sm:flex-col">
                  <Image
                    src={inventory?.inventoryImages[0]}
                    width={10000}
                    height={10000}
                    className="h-[200px] w-[200px] rounded-lg object-contain max-xxl:h-36 max-xxl:w-36 max-xl:h-[150px] max-xl:w-[150px] max-md:h-24 max-md:w-24"
                    alt="product image"
                  />

                  <div className="w-full max-w-[485px]">
                    <div>
                      <h4 className="mb-1.5 text-xl text-white-1000 max-xxl:text-base max-sm:text-center">
                        {' '}
                        {inventory?.name}
                      </h4>
                      <p className="mb-1.5 text-base text-white-1000 max-xxl:text-xs max-sm:text-center">
                        {price} Tickets
                      </p>
                      <p className="text-base text-white-1000 max-xxl:text-xs max-sm:text-center">
                        Qty: {quantity}
                      </p>
                    </div>

                    <div className="mt-10 w-full max-w-[391px] max-xxl:mt-1 max-md:mt-0">
                      <div>
                        <div className="relative m-[0_auto] mt-7 flex w-full max-w-[600px] items-center justify-between">
                          <div className="flex flex-col items-center justify-between gap-3 capitalize text-white-1000">
                            <span
                              className={`z-[2] flex h-3 w-3 items-center justify-center rounded-full ${redeemApprovalStatus === 'approve' ? (orderStatus === 'confirm' ? 'bg-scarlet-900' : 'bg-slateGray-500') : 'bg-slateGray-500'} text-white-1000`}
                            />
                            {redeemApprovalStatus === 'approve'
                              ? orderStatus === 'confirm'
                                ? 'Ordered'
                                : 'Pending'
                              : redeemApprovalStatus === 'pending'
                                ? 'Pending'
                                : 'Rejected'}
                          </div>
                          <div className='flex flex-col items-center justify-between gap-3 capitalize text-steelTeal-1000 before:absolute before:left-[29%] before:top-[4px] before:h-[4px]  before:w-[41%] before:-translate-x-[54%] before:bg-steelTeal-1000  before:content-[""] after:absolute after:right-[6%] after:top-[4px] after:h-[4px] after:w-[41%] after:-translate-x-[7%] after:bg-slateGray-500 after:content-[""] max-xs:before:w-[39%] max-xs:before:-translate-x-[49%] max-xs:after:right-[8%] max-xs:after:w-[39%] max-xs:after:-translate-x-[9%]'>
                            <span
                              className={`z-[2] flex h-3 w-3 items-center justify-center rounded-full ${deliveryStatus === 'dispatched' || deliveryStatus === 'failed' ? 'bg-scarlet-900' : 'bg-slateGray-500'}  text-white-1000`}
                            />
                            {deliveryStatus === 'failed'
                              ? 'Failed'
                              : 'Dispatched'}
                          </div>
                          <div className="flex flex-col items-center justify-between gap-3 capitalize text-steelTeal-1000">
                            <span
                              className={`z-[2] flex h-3 w-3 items-center justify-center rounded-full ${deliveryStatus === 'delivered' ? 'bg-scarlet-900' : 'bg-slateGray-500'} text-white-1000`}
                            />
                            Delivered
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-6 max-sm:flex-row max-sm:justify-between">
                  <div>
                    <p className="text-sm uppercase text-slateGray-500 max-xxl:text-xs">
                      Order Placed
                    </p>
                    <p className="text-xl text-white-1000 max-xxl:text-base">
                      {new Date(orderConfirmedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm uppercase text-slateGray-500 max-xxl:text-xs">
                      Estimated Delivery Date
                    </p>
                    <p className="text-xl text-white-1000 max-xxl:text-base">
                      {estimatedDeliveryDate
                        ? new Date(estimatedDeliveryDate).toLocaleDateString()
                        : 'N/A'}
                    </p>
                  </div>
                </div>
                {redeemApprovalStatus === 'pending' &&
                  orderStatus === 'confirm' && (
                    <button
                      className="rounded bg-red-600 px-2 py-1 text-white-1000"
                      onClick={() => handleCancelOrder(inventoryCartId)}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Cancelling...' : 'Cancel Order'}
                    </button>
                  )}
              </div>
            ),
          )}
        </div>
      </div>
    </div>
  );
}

export default MyOrders;
