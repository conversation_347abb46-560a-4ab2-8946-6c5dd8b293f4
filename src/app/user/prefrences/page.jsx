'use client';

import React, { useEffect, useState } from 'react';
import ToggleSwitch from '@/components/Common/ToggleSwitch';
import { usePreferences, useUpdatePreferences } from '@/reactQuery/authQuery';

function PrefrencesPage() {
  // State Declaration starts here
  const [preferenceData, setPreferenceData] = useState([]);
  const [updatePreference, setUpdatePreference] = useState({});
  // State Declaration ends here

  // Integrating get preference API starts here
  const mutation = usePreferences({
    onSuccess: (response) => {
      setPreferenceData(response.data.data);
    },
    onError: (error) => {
      console.log('error**', error);
    },
  });

  useEffect(() => {
    mutation.mutate();
  }, []);
  // Integrating get preference API ends here

  // Integrating update preference API starts here
  const onToggleChange = (ele, key) => {
    preferenceData.map((item) => {
      if (item.key === key) {
        item.userSelection = ele;
        setUpdatePreference({
          key,
          userSelection: ele,
        });
      }
    });
  };

  useEffect(() => {
    if (Object.keys(updatePreference).length > 0)
      mutationPreference.mutate(updatePreference);
  }, [updatePreference]);

  const mutationPreference = useUpdatePreferences({
    onSuccess: (response) => {
      mutation.mutate();
    },
    onError: (error) => {
      console.log('error**', error);
    },
  });

  // Integrating update preference API starts here

  return (
    <div>
      <div>
        {preferenceData &&
          preferenceData.map((e) => (
            <div className="mb-8 flex items-center justify-between max-md:mb-6">
              <p className="text-xl font-normal text-white-1000 max-sm:text-base uppertab:text-base">
                {e.value}
              </p>

              <div className="">
                <ToggleSwitch
                  onCheckChange={(item) => onToggleChange(item, e.key)}
                  value={e.userSelection}
                />
              </div>
            </div>
          ))}
      </div>

      <div className="flex items-center justify-center rounded-md border border-solid border-steelTeal-1000 p-8 max-sm:p-4 max-sm:py-6 uppertab:p-4 uppertab:py-6">
        <p className="text-center text-base font-normal text-steelTeal-1000">
          * You can see your hidden info, but others can't *
        </p>
      </div>
    </div>
  );
}

export default PrefrencesPage;
