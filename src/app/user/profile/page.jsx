'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import Select from 'react-select';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import PrimaryButtonOutline from '@/components/Common/Button/PrimaryButtonOutline';
import PrimaryButton from '@/components/Common/Button/PrimaryButton';
import {
  useCountriesQuery,
  useStateQuery,
  useUpdateProfileMutation,
} from '@/reactQuery/authQuery';
import useAuthStore from '@/store/useAuthStore';
import CalendarHeartIcon from '@/assets/icons/Calendar-Heart';
import { updateProfileSchema } from '@/config/schema';
import { phoneOptions } from '@/config/general';
import {
  eighteenYearsAgo,
  validateAlphabetInput,
  validateNameInput,
  validateNumberInput,
  validateTextInput,
  validateZipCodeInput,
} from '@/utils/helper';
import { useQueryClient } from '@tanstack/react-query';
import { formatDateMDY } from '@/utils/customizedDate';
import { genderConst } from '@/utils/gender';

function ProfilePage() {
  const { userDetails } = useAuthStore((state) => state);
  const [inputValue, setInputValue] = useState('');
  const genderRefValue = useRef();
  const { data: countriesData, isLoading: countriesLoading } =
    useCountriesQuery({});
  const getCountriesOptions = () => {
    // uncomment to enable all countries
    return countriesLoading
      ? []
      : countriesData
          ?.filter((item) => item?.name === 'United States')
          .map((item) => ({
            countryId: item?.countryId,
            value: item?.code,
            label: item?.name,
          }));
  };

  const getDefaultValues = () => ({
    firstName: userDetails?.firstName || '',
    lastName: userDetails?.lastName || '',
    username: userDetails?.username || '',
    dob: userDetails?.dateOfBirth || null,
    phoneCode: phoneOptions.find(
      (option) => option.value === userDetails?.phoneCode,
    ) || phoneOptions[0],
    phoneNumber: userDetails?.phone || '',
    address: userDetails?.addressLine_1 || '',
    city: userDetails?.city || '',
    state: userDetails?.state
      ? { value: userDetails?.state, label: userDetails?.state }
      : null,
    country: userDetails?.countryCode && countriesData
      ? getCountriesOptions()?.find(
          (option) => option?.countryId === userDetails?.countryCode,
        )
      : null,
    gender: userDetails?.gender
      ? { value: userDetails?.gender, label: userDetails?.gender }
      : null,
    zipCode: userDetails?.zipCode || '',
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    clearErrors,
  } = useForm({
    resolver: yupResolver(updateProfileSchema),
    defaultValues: getDefaultValues(),
  });

  // watcher to watch onchange in form  field
  const watchCountry = watch('country');
  const watchState = watch('state');
  const watchGender = watch('gender');
  const queryClient = useQueryClient();

  const {
    data: stateData,
    isLoading: stateLoading,
    refetch,
  } = useStateQuery({
    countryCode: watchCountry?.value,
    enabled: false,
  });
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        genderRefValue.current &&
        !genderRefValue.current.contains(event.target)
      ) {
        setInputValue('');
      }
    };

    if (inputValue != '') {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [inputValue]);

  useEffect(() => {
    if (watchCountry?.value) {
      refetch({ countryCode: watchCountry?.value });
    }
  }, [watchCountry?.value]);

  // const getGenderOptions = () =>{
  //   return genderConst?.map(item => {value: item.value})
  // }

  const getStateOptions = () => {
    return stateLoading
      ? []
      : stateData?.map((item) => ({
          value: item?.name,
          label: item?.name,
        }));
  };

  useEffect(() => {
    if (userDetails && countriesData) {
      const updatedValues = getDefaultValues();
      setInputValue('');
      reset(updatedValues);
    }
  }, [userDetails, reset, countriesData]);

  const updateUserDetails = useUpdateProfileMutation({
    onSuccess: (response) => {
      if (response?.data?.success)
        toast.success('Your profile updated successfully!');
      queryClient.invalidateQueries(['USER_PROFILE']);
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Something went wrong';
      toast.error(message);
    },
  });

  const onSubmit = (formValues) => {
    const formData = new FormData();
  
    for (const [key, value] of Object.entries(formValues)) {
      if (value instanceof File) {
        formData.append(key, value); 
      } else if (typeof value !== 'object' || value === null) {
        formData.append(key, value ?? '');
      }
    }
  
    formData.set('state', formValues?.state?.value || '');
    formData.set('country', formValues?.country?.countryId || '');
    formData.set('gender', formValues?.gender?.value || '');
    formData.set('phoneCode', formValues?.phoneCode?.value || '');
    formData.set('dateOfBirth', formatDateMDY(formValues?.dob) || '');
    formData.set('mobileNo', formValues?.phoneNumber || '');
  
    if (formValues?.profileImage instanceof File) {
      formData.set('profileImage', formValues.profileImage);
    }
  
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }
    updateUserDetails.mutate(formData);
  };
  

  return (
    <div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-2 gap-6 max-sm:grid-cols-1 desktop:gap-4 uppertab:grid-cols-1 uppertab:gap-3">
          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              First Name
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="first-name"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.firstName ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateNameInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                    maxLength={20}
                  />
                )}
              />
            </div>
            {errors.firstName && (
              <p className="text-red-500">{errors.firstName.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Last Name
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="last-name"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.lastName ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateNameInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                    maxLength={20}
                  />
                )}
              />
            </div>
            {errors.lastName && (
              <p className="text-red-500">{errors.lastName.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Username
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="username"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    disabled
                    placeholder="username"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 cursor-not-allowed ${errors.username ? 'border-red-500' : ''}`}
                    onChange={(e) => {
                      if (validateTextInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                  />
                )}
              />
            </div>
            {errors.username && (
              <p className="text-red-500">{errors.username.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Date of Birth
            </label>
            <div />
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal [&>.react-datepicker-wrapper]:w-full">
              <Controller
                name="dob"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    showYearDropdown
                    scrollableYearDropdown
                    yearDropdownItemNumber={105}
                    placeholderText="MM/DD/YYYY"
                    onChange={(date) =>
                      field.onChange(
                        date !== 'Invalid Date' && date !== null ? date : '',
                      )
                    }
                    selected={field.value}
                    utcOffset={0}
                    onKeyDown={(e) => {
                      e.preventDefault();
                    }}
                    maxDate={eighteenYearsAgo()}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.dob ? 'border-red-500' : ''}`}
                  />
                )}
              />

              <div className="pointer-events-none absolute right-4 top-1/2 translate-y-[-50%]">
                <CalendarHeartIcon className="fill-steelTeal-1000" />
              </div>
            </div>
            {errors.dob && <p className="text-red-500">{errors.dob.message}</p>}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Phone
            </label>
            <div className="flex gap-2">
              <div className="w-full max-w-[75px]">
                <Select
                  name="phoneCode"
                  className="mobile-input-select"
                  classNamePrefix="mobile-input"
                  placeholder="+XX"
                  onChange={(val) => setValue('phoneCode', val)}
                  options={phoneOptions}
                  defaultValue={getDefaultValues().phoneCode}
                />
              </div>
              <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
                <Controller
                  name="phoneNumber"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      value={field.value || ''}
                      type="text"
                      placeholder="XX-XXXX-XXXX"
                      className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.phoneNumber ? 'border-red-500' : ''}`}
                      onChange={(e) => {
                        if (validateNumberInput(e.target.value)) {
                          field.onChange(e);
                        }
                      }}
                    />
                  )}
                />
              </div>
            </div>
            {errors.phoneNumber && (
              <p className="text-red-500">{errors.phoneNumber.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Address
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    name="address"
                    placeholder="address"
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.address ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.address && (
              <p className="text-red-500">{errors.address.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              City
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="city"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="city"
                    name="city"
                    maxLength={30}
                    onChange={(e) => {
                      if (validateAlphabetInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.city ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.city && (
              <p className="text-red-500">{errors.city.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Country
            </label>
            <div className="">
              <Select
                name="country"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select Country"
                onChange={(val) => {
                  setValue('country', val);
                  setValue('state', null);
                  clearErrors('country');
                }}
                options={getCountriesOptions()}
                value={watchCountry}
              />
            </div>
            {errors.country && (
              <p className="text-red-500">{errors.country.message}</p>
            )}
          </div>

          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              State
            </label>
            <div className="">
              <Select
                name="state"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select State"
                onChange={(val) => {
                  setValue('state', val);
                  clearErrors('state');
                }}
                options={getStateOptions()}
                value={watchState}
              />
            </div>
            {errors.state && (
              <p className="text-red-500">{errors.state.message}</p>
            )}
          </div>
          <div className="">
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Zip Code
            </label>
            <div className="text-white relative w-full rounded-md bg-maastrichtBlue-1000 font-normal">
              <Controller
                name="zipCode"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    value={field.value || ''}
                    type="text"
                    placeholder="Enter zip code"
                    maxLength={9}
                    onChange={(e) => {
                      if (validateZipCodeInput(e.target.value)) {
                        field.onChange(e);
                      }
                    }}
                    className={`text-white w-full rounded-md border border-solid border-transparent bg-transparent p-[15.5px] text-base font-normal leading-none placeholder-steelTeal-1000 focus:border focus:border-solid focus:border-borderColor-100 ${errors.city ? 'border-red-500' : ''}`}
                  />
                )}
              />
            </div>
            {errors.zipCode && (
              <p className="text-red-500">{errors.zipCode.message}</p>
            )}
          </div>
          <div className="" ref={genderRefValue}>
            <label className="mb-1 block text-base font-normal capitalize text-steelTeal-1000">
              Gender
            </label>
            <div className="">
              <Select
                name="gender"
                className="form-input-select"
                classNamePrefix="form-input"
                placeholder="Select Gender"
                onChange={(val) => {
                  setValue('gender', val);
                  clearErrors('gender');
                  setInputValue('')
                }}
                options={genderConst}
                value={watchGender}
                isSearchable
                inputValue={inputValue} // Controlled input
                onInputChange={(newValue, { action }) => {
                  if (action === 'input-change') {
                    // Allow only alphabets
                    if (/^[A-Za-z]*$/.test(newValue)) {
                      setInputValue(newValue); // Update only if valid
                    }
                  } else if (action === 'set-value' || action === 'menu-close') {
                    setInputValue('');
                  }
                }}
                filterOption={(option, searchText) => {
                  // Show all options if search is empty
                  if (!searchText) return true;
                  return option.label
                    .toLowerCase()
                    .includes(searchText.toLowerCase());
                }}
              />
            </div>
            {errors.gender && (
              <p className="text-red-500">{errors.gender.message}</p>
            )}
          </div>
        </div>

        <div className="mt-[30px] flex justify-center gap-4 rounded-md border border-steelTeal-1000 p-4 max-sm:mt-6">
          <PrimaryButton type="submit" isLoading={updateUserDetails?.isPending}>
            Save
          </PrimaryButton>
          <PrimaryButtonOutline
            type="button"
            onClick={() => reset(getDefaultValues())}
          >
            Cancel
          </PrimaryButtonOutline>
        </div>
      </form>
    </div>
  );
}

export default ProfilePage;
