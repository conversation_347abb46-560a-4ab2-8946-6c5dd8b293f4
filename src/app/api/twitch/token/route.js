import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(req) {
  const { code } = await req.json();

  if (!code) {
    return NextResponse.json({ error: 'Authorization code is missing' }, { status: 400 });
  }

  const clientId = process.env.NEXT_PUBLIC_TWITCH_CLIENT_ID;
  const clientSecret = process.env.TWITCH_CLIENT_SECRET;
  const redirectUri = process.env.TWITCH_REDIRECT_URI;

  try {
    const tokenResponse = await axios.post('https://id.twitch.tv/oauth2/token', null, {
      params: {
        client_id: clientId,
        client_secret: clientSecret,
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      },
    });

    const { access_token } = tokenResponse.data;

    const userResponse = await axios.get('https://api.twitch.tv/helix/users', {
      headers: {
        Authorization: `Bearer ${access_token}`,
        'Client-Id': clientId,
      },
    });

    const userData = userResponse.data.data[0];

    return NextResponse.json({ user: userData });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to authenticate with Twitch' }, { status: 500 });
  }
}
