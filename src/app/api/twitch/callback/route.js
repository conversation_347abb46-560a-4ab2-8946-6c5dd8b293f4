import { NextResponse } from 'next/server';

export async function GET(req) {
  const url = new URL(req.url);
  const code = url.searchParams.get('code');

  if (!code) {
    return NextResponse.json({ error: 'Authorization code not found' }, { status: 400 });
  }

  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:6005';
  const redirectUrl = `${baseUrl}/twitch-signup?code=${code}`;

  return NextResponse.redirect(redirectUrl);
}
