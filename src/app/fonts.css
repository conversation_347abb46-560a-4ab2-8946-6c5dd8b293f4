/* Digitalt Font Start Here */
@font-face {
  font-family: 'Digitalt';
  src: url('../assets/fonts/Digitalt.eot');
  src: url('../assets/fonts/Digitalt.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Digitalt.woff2') format('woff2'),
    url('../assets/fonts/Digitalt.woff') format('woff'),
    url('../assets/fonts/Digitalt.ttf') format('truetype'),
    url('../assets/fonts/Digitalt.svg#Digitalt') format('svg');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

.font-digitalt {
  font-family: 'Digitalt' !important;
}


/* Nunito Font Start Here */
@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-ExtraLight.eot');
  src: url('../assets/fonts/Nunito-ExtraLight.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-ExtraLight.woff2') format('woff2'),
    url('../assets/fonts/Nunito-ExtraLight.woff') format('woff'),
    url('../assets/fonts/Nunito-ExtraLight.ttf') format('truetype'),
    url('../assets/fonts/Nunito-ExtraLight.svg#Nunito-ExtraLight') format('svg');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Light.eot');
  src: url('../assets/fonts/Nunito-Light.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Light.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Light.woff') format('woff'),
    url('../assets/fonts/Nunito-Light.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Light.svg#Nunito-Light') format('svg');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Regular.eot');
  src: url('../assets/fonts/Nunito-Regular.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Regular.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Regular.woff') format('woff'),
    url('../assets/fonts/Nunito-Regular.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Regular.svg#Nunito-Regular') format('svg');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Medium.eot');
  src: url('../assets/fonts/Nunito-Medium.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Medium.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Medium.woff') format('woff'),
    url('../assets/fonts/Nunito-Medium.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Medium.svg#Nunito-Medium') format('svg');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-SemiBold.eot');
  src: url('../assets/fonts/Nunito-SemiBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-SemiBold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-SemiBold.woff') format('woff'),
    url('../assets/fonts/Nunito-SemiBold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-SemiBold.svg#Nunito-SemiBold') format('svg');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Bold.eot');
  src: url('../assets/fonts/Nunito-Bold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Bold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Bold.woff') format('woff'),
    url('../assets/fonts/Nunito-Bold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Bold.svg#Nunito-Bold') format('svg');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-ExtraBold.eot');
  src: url('../assets/fonts/Nunito-ExtraBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-ExtraBold.woff2') format('woff2'),
    url('../assets/fonts/Nunito-ExtraBold.woff') format('woff'),
    url('../assets/fonts/Nunito-ExtraBold.ttf') format('truetype'),
    url('../assets/fonts/Nunito-ExtraBold.svg#Nunito-ExtraBold') format('svg');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('../assets/fonts/Nunito-Black.eot');
  src: url('../assets/fonts/Nunito-Black.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/Nunito-Black.woff2') format('woff2'),
    url('../assets/fonts/Nunito-Black.woff') format('woff'),
    url('../assets/fonts/Nunito-Black.ttf') format('truetype'),
    url('../assets/fonts/Nunito-Black.svg#Nunito-Black') format('svg');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

.font-nunito {
  font-family: 'Nunito' !important;
}


/* League Spartan Font Start Here */
@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Thin.eot');
  src: url('../assets/fonts/LeagueSpartan-Thin.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Thin.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Thin.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Thin.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Thin.svg#LeagueSpartan-Thin') format('svg');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-ExtraLight.eot');
  src: url('../assets/fonts/LeagueSpartan-ExtraLight.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-ExtraLight.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-ExtraLight.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-ExtraLight.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-ExtraLight.svg#LeagueSpartan-ExtraLight') format('svg');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Light.eot');
  src: url('../assets/fonts/LeagueSpartan-Light.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Light.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Light.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Light.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Light.svg#LeagueSpartan-Light') format('svg');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Regular.eot');
  src: url('../assets/fonts/LeagueSpartan-Regular.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Regular.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Regular.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Regular.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Regular.svg#LeagueSpartan-Regular') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Medium.eot');
  src: url('../assets/fonts/LeagueSpartan-Medium.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Medium.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Medium.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Medium.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Medium.svg#LeagueSpartan-Medium') format('svg');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-SemiBold.eot');
  src: url('../assets/fonts/LeagueSpartan-SemiBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-SemiBold.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-SemiBold.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-SemiBold.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-SemiBold.svg#LeagueSpartan-SemiBold') format('svg');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Bold.eot');
  src: url('../assets/fonts/LeagueSpartan-Bold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Bold.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Bold.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Bold.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Bold.svg#LeagueSpartan-Bold') format('svg');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-ExtraBold.eot');
  src: url('../assets/fonts/LeagueSpartan-ExtraBold.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-ExtraBold.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-ExtraBold.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-ExtraBold.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-ExtraBold.svg#LeagueSpartan-ExtraBold') format('svg');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'League Spartan';
  src: url('../assets/fonts/LeagueSpartan-Black.eot');
  src: url('../assets/fonts/LeagueSpartan-Black.eot?#iefix') format('embedded-opentype'),
    url('../assets/fonts/LeagueSpartan-Black.woff2') format('woff2'),
    url('../assets/fonts/LeagueSpartan-Black.woff') format('woff'),
    url('../assets/fonts/LeagueSpartan-Black.ttf') format('truetype'),
    url('../assets/fonts/LeagueSpartan-Black.svg#LeagueSpartan-Black') format('svg');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

.font-league-spartan {
  font-family: 'League Spartan' !important;
}