'use client';

import { useParams } from 'next/navigation';
import Image from 'next/image';
import toast from 'react-hot-toast';
import { useGetGroupDetailQuery } from '@/reactQuery/chatWindowQuery';
import { useQueryClient } from '@tanstack/react-query';
import { Upload, Loader2 } from 'lucide-react';
import useGroupChatStore from '@/store/useGroupChatStore';
import useGeneralStore from '@/store/useGeneralStore';
import GroupImg from '@/assets/images/stock-images/group-img.jpg';
import { useRef, useState } from 'react';
import { useUpdateGroupBannerMutation } from '@/reactQuery/authQuery';
import BannerLoader from '@/components/BannerLoader';
import useAuthStore from '@/store/useAuthStore';
import Chat from '../../../components/GroupPageChat/page';
import ChatAvatar from '@/components/ChatWindow/ChatAvatar';

function GroupPage() {
  const { groupname } = useParams();
  const { setOpenChat } = useGeneralStore((state) => state);
  const { userDetails } = useAuthStore((state) => state);
  const queryClient = useQueryClient();
  const fileInputRef = useRef(null);
  const [previewBanner, setPreviewBanner] = useState(null);
  const [isUpdatingBanner, setIsUpdatingBanner] = useState(false);
  const [imageErrors, setImageErrors] = useState({});

  const {
    setIsGroupChatOpen,
    setGroupId,
    setGroupName,
    setIsGroupPageOpen,
    activeGroupPageTab,
    setActiveGroupPageTab,
  } = useGroupChatStore((state) => state);

  const originalGroupName = groupname?.replace(/-/g, ' ');

  const { data: groupData, isLoading } = useGetGroupDetailQuery({
    groupName: originalGroupName,
  });
  const isAdmin = userDetails?.userId == groupData?.groupAdmin;

  const updateBannerMutation = useUpdateGroupBannerMutation({
    onSuccess: (data) => {
      setIsUpdatingBanner(false);

      if (data?.groupBanner) {
        queryClient.setQueryData(
          ['GET_GROUP_DETAIL_QUERY', originalGroupName],
          (oldData) => {
            if (oldData) {
              return {
                ...oldData,
                groupBanner: data.groupBanner,
              };
            }
            return oldData;
          },
        );
        setPreviewBanner(null);
      } else {
        queryClient
          .invalidateQueries({
            queryKey: ['GET_GROUP_DETAIL_QUERY', originalGroupName],
          })
          .then(() => {
            setPreviewBanner(null);
          });
      }

      toast.success('Banner updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating banner:', error);
      setPreviewBanner(null);
      setIsUpdatingBanner(false);
    },
  });

  const handleBannerClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setIsUpdatingBanner(true);

      const reader = new FileReader();
      reader.onload = () => {
        setPreviewBanner(reader.result);
      };
      reader.readAsDataURL(file);

      const formData = new FormData();
      formData.append('groupId', groupData?.id);
      formData.append('groupBanner', file);
      updateBannerMutation.mutate(formData);
    }
  };

  const openChat = (groupId, groupName) => {
    setActiveGroupPageTab('chat');
    setOpenChat(!openChat);
    setIsGroupPageOpen(true);
    queryClient.invalidateQueries({ queryKey: ['GET_GROUP_CHATS_QUERY'] });
    setGroupId(groupId);
    setGroupName(groupName);
    setIsGroupChatOpen(true);
  };

  const getBannerImage = () => {
    if (previewBanner) return previewBanner;
    return groupData?.groupBanner || GroupImg;
  };

  return (
    <div className="text-white min-h-screen border-gray-700  bg-black-850">
      <div className="relative w-full overflow-hidden rounded-t-3xl bg-maastrichtBlue-1000">
        {(updateBannerMutation.isPending || isUpdatingBanner || isLoading) &&
        !previewBanner ? (
          <BannerLoader />
        ) : (
          <>
            <Image
              src={getBannerImage()}
              alt={`${groupData?.groupName} banner`}
              height={400}
              width={1200}
              quality={90}
              priority
              className="aspect-[6/1] h-full w-full max-w-[1080px] object-cover object-center"
            />
            <h1 className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-lg bg-black-500 px-3 py-1 text-3xl font-bold">
              {groupData?.groupName}
            </h1>
          </>
        )}

        {isAdmin && (
          <>
            <button
              onClick={handleBannerClick}
              disabled={updateBannerMutation.isPending || isUpdatingBanner}
              className="absolute bottom-4 ml-2 flex items-center gap-2 rounded-lg bg-primary-1000 bg-opacity-70 px-3 py-2 text-sm font-medium text-white-1000 hover:bg-opacity-80 disabled:cursor-not-allowed disabled:opacity-50"
              type="button"
            >
              {updateBannerMutation.isPending || isUpdatingBanner ? (
                <Loader2 size={20} className="animate-spin" />
              ) : (
                <Upload size={20} />
              )}
            </button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />
          </>
        )}
      </div>

      <div className="mx-auto max-w-7xl px-4 py-3">
        <div className="mb-4 border-b border-gray-700">
          <nav className="flex space-x-4">
            <button
              className={`px-4 py-2 font-semibold ${
                activeGroupPageTab === 'about'
                  ? 'text-white border-b-2 border-red-600'
                  : 'hover:text-white text-gray-400'
              }`}
              type="button"
              onClick={() => setActiveGroupPageTab('about')}
            >
              About
            </button>
            <button
              className={`px-4 py-2 font-semibold ${
                activeGroupPageTab === 'chat'
                  ? 'text-white border-b-2 border-red-600'
                  : 'hover:text-white text-gray-400'
              }`}
              type="button"
              onClick={() => openChat(groupData?.id, groupData?.groupName)}
            >
              Chat
            </button>
            <button
              className={`px-4 py-2 font-semibold ${
                activeGroupPageTab === 'members'
                  ? 'text-white border-b-2 border-red-600'
                  : 'hover:text-white text-gray-400'
              }`}
              type="button"
              onClick={() => setActiveGroupPageTab('members')}
            >
              Members
            </button>
          </nav>
        </div>

        <div className="flex flex-col gap-6 md:flex-row">
          {activeGroupPageTab === 'about' && (
            <div className="w-full">
              <div className="rounded-lg bg-maastrichtBlue-1000 p-6 shadow">
                <h2 className="mb-4 text-xl font-semibold">
                  About {groupData?.groupName}
                </h2>
                <p className="text-gray-300">{groupData?.groupDescription}</p>
              </div>
            </div>
          )}

          {activeGroupPageTab === 'chat' && (
            <div className="w-full">
              <Chat groupData={groupData} loading={isLoading} />
            </div>
          )}

          {activeGroupPageTab === 'members' && (
            <div className="w-full">
              <div className="rounded-lg bg-maastrichtBlue-1000 p-6 shadow">
                <h2 className="mb-4 text-xl font-semibold">
                  Members ({groupData?.groupMembersCount})
                </h2>
                <div className="space-y-4">
                  {groupData?.members?.map((member) => (
                    <div key={member.id} className="flex items-center gap-3">
                      {member.user.profileImage &&
                      member.user.profileImage.startsWith('http') &&
                      !imageErrors[member.user.userId] ? (
                        <Image
                          src={member.user.profileImage}
                          alt={member.name}
                          height={40}
                          width={40}
                          className="h-10 w-10 rounded-full object-cover"
                          onError={() => {
                            setImageErrors(prev => ({
                              ...prev,
                              [member.user.userId]: true
                            }));
                          }}
                        />
                      ) : (
                        <ChatAvatar
                          profileImage={member.user.profileImage}
                          firstName={member.user.firstName}
                          lastName={member.user.lastName}
                          userName={member.user.username}
                          imageClassName="h-10 w-10 rounded-full object-cover"
                          imageWidth={40}
                          imageHeight={40}
                          avatarSize={40}
                        />
                      )}
                      <div>
                        <p className="font-medium">{member?.user?.firstName}</p>
                        <p
                          className={`text-sm text-gray-400 ${member.isAdmin ? 'text-green-400' : 'text-gray-400'}`}
                        >
                          {member.isAdmin ? 'Admin' : 'Member'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GroupPage;
