import { Inter } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import Script from 'next/script';
import './globals.css';
import Head from 'next/head';
import { SkeletonTheme } from 'react-loading-skeleton';
import Header from '@/components/Header';
import SideMenu from '@/components/SideMenu';
import ChatWindow from '@/components/ChatWindow';
import Footer from '@/components/Footer';
import { ReactQueryClientProvider } from '@/components/ReactQueryClientProvider/ReactQueryClientProvider';
import MainSection from '@/components/Common/MainSection';
import Modal from '@/components/Common/Modal/page';
import Auth from '@/components/Auth';
import BottomMenu from '@/components/BottomMenu';
import { GoogleOAuthProvider } from '@react-oauth/google';
import CallModal from '@/components/Common/Modal/CallModal';
import GlobalUIWrapper from '@/components/GlobalUIWrapper';
import ChatWrapper from '@/components/ChatWrapper';
import { Suspense } from 'react';
import MainLoader from '@/components/Common/Loader/MainLoader';
import ClientSuspenseWrapper from '@/components/GlobalUIWrapper/ClientSuspenseError';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'FansBets',
  description: 'online casino platform',
};
export const dynamic = 'force-dynamic';
export default async function RootLayout({ children }) {
  return (
    <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_ID}>
      <html lang="en">
        <Head>
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <body className={inter.className}>
          <div className="bg-black-1000">
            {/* <ClientWrapper> */}
            {/* <Suspense
              fallback={
                <div className="text-white bg-black relative flex min-h-screen items-center justify-center overflow-hidden">
                  <div className="z-10 w-32">
                    <MainLoader />
                  </div>

                 
                  <div className="absolute left-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]"></div>

                  <div className="absolute right-[-100px] top-0 h-full w-[200px] rounded-full bg-red-600 opacity-30 blur-[97px]"></div>
                </div>
              }
            > */}

            <ClientSuspenseWrapper>
              <ReactQueryClientProvider>
                <SkeletonTheme baseColor="rgb(5, 20, 56)" highlightColor="#444">
                  <Header />
                  <SideMenu />
                  {/* <Auth /> */}
                  <Toaster position="top-right" />
                  <CallModal />
                  <GlobalUIWrapper>
                    <MainSection>{children}</MainSection>
                  </GlobalUIWrapper>
                  <ChatWrapper />
                  {/* <ChatWindow /> */}

                  <Modal />
                  <Footer />
                  <BottomMenu />
                  {/* <Notice /> */}
                </SkeletonTheme>
              </ReactQueryClientProvider>
            </ClientSuspenseWrapper>
            {/* </Suspense> */}

            {/* </ClientWrapper> */}
          </div>
          <Script src="/pixi-legacy.min.js" />
        </body>
      </html>
    </GoogleOAuthProvider>
  );
}
