'use client';

import MainLoader from '@/components/Common/Loader/MainLoader';
import { useGameLaunchQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import useGameStore from '@/store/useGameStore';
import { unSlugify } from '@/utils/helper';
import { useEffect } from 'react';
import GreenbarBonus from '../../GreenbarBonus';

function GamePage({ params }) {
  const { isAuthenticated, coin } = useAuthStore((state) => state);
  const { gameDetails, setGameDetails, iframeHeight } = useGameStore();

  const { data, isLoading } = useGameLaunchQuery({
    params: {
      coin,
      gameName: unSlugify(params?.gameId),
      gameProvider: unSlugify(params?.provider),
      isMobile: window?.innerWidth < 441 ? true : false,
    },
    enabled: isAuthenticated,
  });
  useEffect(() => {
    setGameDetails(data);
  }, [data]);

  return isLoading ? (
    <div className="flex h-96 items-center justify-center">
      <MainLoader className="w-32" />
    </div>
  ) : gameDetails ? (
    <div className="relative w-full">
      {/* Progress Bar */}
      <div className=": hidden lg:block">
        <GreenbarBonus />
      </div>

      {/* Game Iframe */}
      <iframe
        // title="game"
        className={`-mt-2 w-full md:mt-10 lg:mt-9  lg:aspect-[1031/800]`}
        src={gameDetails?.gameUrl}
        style={{ height: iframeHeight, overflow: 'hidden' }}
        frameBorder="0"
        allow="autoplay"
        scrolling="no"
      />
    </div>
  ) : (
    <div className="flex h-96 items-center justify-center">
      Something went wrong
    </div>
  );
}

export default GamePage;
