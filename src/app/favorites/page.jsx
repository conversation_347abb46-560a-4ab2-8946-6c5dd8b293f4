/* 'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useGetFavoritesGamesQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';
import GameCard from '@/components/Common/GameCard';
import MainLoader from '@/components/Common/Loader/MainLoader';

function FavoritesPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore((state) => state);
  const { data: favoritesGames, isLoading } = useGetFavoritesGamesQuery({
    enabled: isAuthenticated,
  });

  return isLoading ? (
    <MainLoader className="w-32" />
  ) : favoritesGames?.rows?.length > 0 ? (
    <section className="mb-10 rounded-lg bg-oxfordBlue-1000 p-3 shadow-container">
      <div className="mb-4 flex items-center justify-between gap-4">
        <h6 className="text-xl font-bold text-white-1000">Favorites</h6>
      </div>
      <div className="grid grid-cols-2 gap-4 xs:grid-cols-3 md:grid-cols-5">
        {favoritesGames?.rows?.map((game, index) => (
          <GameCard
            key={game?.name}
            src={game?.MasterCasinoGamesThumbnails?.[0]?.thumbnail}
            alt={game?.name}
            gameId={game?.masterCasinoGameId}
            isFavorite={game?.FavoriteGames}
            width="10000"
            height="10000"
            onClick={() => router.push(`/game/${game?.masterCasinoGameId}`)}
          />
        ))}
      </div>
    </section>
  ) : (
    <div className="flex h-96 items-center justify-center">
      No games are available
    </div>
  );
}

export default FavoritesPage;
 */

'use client';

import GameCard from '@/components/Common/GameCard';
import MainLoader from '@/components/Common/Loader/MainLoader';
import { getFavoritesGames } from '@/utils/apiCalls';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import React, { useCallback, useEffect, useRef } from 'react';
import defaultImage from '../../assets/images/png/default-image.png';
import useAuthStore from '@/store/useAuthStore';
import ZeroBalancePopUp from '@/components/Models/ZeroBalancePopUp';
import useModalStore from '@/store/useModalStore';
import Auth from '@/components/Auth';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { slugify } from '@/utils/helper';
function FavoritesPage() {
  const queryClient = useQueryClient(); // Access the query client
  const { isAuthenticated, userWallet,coin ,userDetails} = useAuthStore((state) => state);
  const { openModal } = useModalStore((state) => state);
  useEffect(() => {
    // Reset the query data when the component mounts
    queryClient.resetQueries(['GET_FAVORITES_GAMES'], { exact: true });
  }, [queryClient]);

  const observerRef = useRef(null)
  
  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    status,
  } = useInfiniteQuery({
    queryKey: ['GET_FAVORITES_GAMES'],
    queryFn: ({ pageParam = 1 }) => {
      return getFavoritesGames({ pageParam });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const { rows = [], count = 0 } = lastPage?.data?.data || {};
      const totalFetched = allPages.flatMap(
        (page) => page?.data?.data?.rows,
      ).length;

      if (totalFetched >= count) {
        return undefined;
      }
      const currentPage = allPages.length; // Based on the number of fetched pages
      return currentPage + 1;
    },
  });
    const loadMoreRef = useCallback(
      (node) => {
        if (isFetchingNextPage) return;
        if (observerRef.current) observerRef.current.disconnect()
  
        observerRef.current = new IntersectionObserver((entries) => {
          if (entries[0].isIntersecting && hasNextPage) {
            fetchNextPage();
          }
        });
  
        if (node) observerRef.current.observe(node)
      },
      [isFetchingNextPage, fetchNextPage, hasNextPage]
    )
  
  const router = useRouter();
  const handleGameClick = (gameName, provider) => {
    if (userDetails?.isRestrict) {
      toast.error('You are restricted, Please contact administrator');
      return;
    }
  
    if (coin === "GC") {
      if (userWallet.gcCoin === 0) {
      
        openModal(<ZeroBalancePopUp message="You have zero GC Coins! Please add funds." />);
        return; 
      }
    } else if (coin === "SC") {
      if (userWallet.scCoin === 0) {

        openModal(<ZeroBalancePopUp message="You have zero SC Coins! Please add funds." />);
        return; 
      }
    }
    if (!isAuthenticated) {
      openModal(<Auth />);
    } else {
      router.push(`/game/${slugify(provider)}/${slugify(gameName)}`);
    }
  };

  const allGames = data?.pages?.flatMap((page) => page?.data?.data?.rows) || [];

  return status === 'pending' ? (
    <section className="item-center  mb-10 flex justify-center rounded-lg p-4 shadow-container">
      <div className="text-white text-center">
        <MainLoader className="w-32" />
      </div>
    </section>
  ) : status === 'error' ? (
    <p>Error: {error.message}</p>
  ) : (
    <>
      <section className="mb-10 rounded-lg  shadow-container  md:mt-10">
        <div className="mb-6 flex items-center justify-between gap-4">
          <h6 className="text-white text-xl font-bold">Favorites Games</h6>
        </div>
        {allGames.length === 0 ? ( // Check if no games are available
          <div className="text-white text-center">No games found</div>
        ) : (
          <div className="grid grid-cols-4 gap-x-5 gap-y-8 md:grid-cols-4 lg:grid-cols-5  max-sm:grid-cols-3  max-sm:gap-[10px]">
            {data?.pages?.map((page, i) =>
              page?.data?.data?.rows?.map((game, index) => (
                <GameCard
                  key={`${game?.name}-${index}`}
                  src={
                    game?.imageUrl ||
                    defaultImage ||
                    game?.MasterCasinoGamesThumbnails?.[0]?.thumbnail
                  }
                  alt={game?.name}
                  gameId={game?.masterCasinoGameId}
                  isFavorite={game?.FavoriteGames}
                  width="200"
                  height="200"
                  onClick={() =>
                    handleGameClick(game?.name, game?.moreDetails?.product)
                  }
                  // onClick={() =>
                  //   router.push(`/game/${game?.masterCasinoGameId}`)
                  // }
                />
              )),
            )}
              </div>
            )}
            {hasNextPage && (
              <div ref={loadMoreRef} className="mt-6 h-10 w-full text-center">
                {isFetchingNextPage && (
                  <MainLoader className="mx-auto w-10 text-white" />
                )}
              </div>
            )}
      </section>
    </>
  );
}

export default FavoritesPage;
