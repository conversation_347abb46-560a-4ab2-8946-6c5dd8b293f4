/* eslint-disable no-nested-ternary */
'use client';

import MainLoader from '@/components/Common/Loader/MainLoader';
import { useSportsbookLaunchQuery } from '@/reactQuery/gamesQuery';
import useAuthStore from '@/store/useAuthStore';

// export const dynamic = 'force-dynamic';
function SportsbookPage() {
  const { isAuthenticated, coin } = useAuthStore((state) => state);

  const { data, isLoading } = useSportsbookLaunchQuery({
    params: {
      coin,
      isMobile: window?.innerWidth < 441,
    },
    enabled: isAuthenticated,
  });

  return isLoading ? (
    <div className="flex h-96 items-center justify-center">
      <MainLoader className="w-32" />
    </div>
  ) : data ? (
    <div className="relative w-full">
      {/* Sportsbook Iframe */}
      <iframe
        title="sportsbook"
        className="-mt-2 h-[calc(100vh-80px)] w-full md:mt-10 lg:mt-9 lg:aspect-[1031/800] lg:h-[calc(100vh-140px)]"
        src={data?.gameUrl}
        frameBorder="0"
      />
    </div>
  ) : (
    <div className="flex h-96 items-center justify-center">
      Something went wrong
    </div>
  );
}

export default SportsbookPage;
