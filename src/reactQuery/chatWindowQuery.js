import {
  acceptDeclineGroupRequest,
  createFriendRequest,
  createGroup,
  getAgoraToken,
  getFriends,
  getFriendsRequest,
  getGroupChats,
  getGroupDetail,
  getGroupDetails,
  getGroupJoinRequestList,
  getGroupList,
  getPrivateChat,
  getPublicChats,
  getPublicGroupDetails,
  getPublicGroupList,
  getRecentChat,
  getUserTags,
  joinGroup,
  sendDeclinedReq,
  sendGroupJoinRequest,
  sendPublicChatsMsg,
  sendTagMsg,
  unFriendRequest,
  updateFriendRequest,
  updateGroup,
  updateJoinedGroup,
} from '@/utils/apiCalls';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';

export const useGetPublicChatsQuery = ({ enabled }) =>
  useInfiniteQuery({
    queryKey: ['GET_PublicChatsQuery'],
    enabled,
    queryFn: ({ pageParam = 1 }) => getPublicChats({ pageParam }),
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data.pages
        .flatMap((page) => page.data.rows)
        .filter((row) => row.groupId === null),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useGetGroupChatsQuery = ({ groupId, enabled }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_CHATS_QUERY', groupId],
    queryFn: ({ pageParam = 1 }) => getGroupChats({ pageParam, groupId }),
    getNextPageParam: (lastPage, allPages) => {
      const morePagesExist = lastPage?.data?.count;
      if (!morePagesExist) return false;
      return allPages.length + 1;
    },
    select: (data) => ({
      pages: data.pages.flatMap((page) => page.data.rows),
      livePlayersCount: data.pages[0]?.data.livePlayersCount,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGroupChatMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_CREATE'],
    mutationFn: (data) => createGroup(data),
    onSuccess,
    onError,
  });

export const useGroupChatUpdateDetailsMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_UPDATE'],
    mutationFn: (data) => updateGroup(data),
    onSuccess,
    onError,
  });

export const useJoinedGroupMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['jOINED_GROUP'],
    mutationFn: (data) => updateJoinedGroup(data),
    onSuccess,
    onError,
  });
export const useGroupJoinRequestMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['GROUP_JOIN_REQUEST'],
    mutationFn: (data) => sendGroupJoinRequest(data),
    onSuccess,
    onError,
  });
export const useGetGroupDetailQuery = ({ groupName,enabled }) =>
  useQuery({
    queryKey: ['GET_GROUP_DETAIL_QUERY', groupName],
    queryFn: () => getGroupDetail(groupName),
    select: (data) => data?.data?.group,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetGroupDetails = ({ enabled, params }) => {
  return useQuery({
    queryKey: ['GET_GROUP_DETAILS_QUERY', params?.groupId],
    queryFn: () => getGroupDetails(params?.groupId),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
};
export const useGetPublicGroupDetails = ({ enabled, params }) => {
  return useQuery({
    queryKey: ['GET_GROUP_DETAILS_QUERY', params?.groupId],
    queryFn: () => getPublicGroupDetails(params?.groupId),
    select: (data) => data?.data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
};

export const useGetGroupListQuery = ({ enabled, search, groupName }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_LIST_QUERY', search, groupName],
    queryFn: async ({ pageParam = 1 }) => {
      const params = { search, page: pageParam, limit: 20, groupName };
      return getGroupList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      const lastPageCount = lastPage?.data?.groups?.length || 0;
      const totalCount = lastPage?.data?.count || 0;
      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.groups?.length || 0),
        0,
      );

      // Stop fetching if the total count is less than the limit (means all items are on the first page)
      if (totalCount <= 20) return undefined;

      // Stop fetching if lastPage has no data or all items are fetched
      return lastPageCount > 0 && currentCount < totalCount
        ? allPages.length + 1
        : undefined;
    },
    select: (data) => ({
      groups: data.pages.flatMap((page) => page?.data?.groups || []),
      total: data.pages?.[0]?.data?.count || 0,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
export const useGetGroupRequestListQuery = ({ enabled, search, groupName, invitationType }) =>
  useInfiniteQuery({
    queryKey: ['GET_GROUP_REQUEST_LIST_QUERY', search, groupName],
    queryFn: async ({ pageParam = 1 }) => {
      const params = { search, page: pageParam, limit: 5, groupName, invitationType };
      return getGroupJoinRequestList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      const lastPageCount = lastPage?.data?.groups?.length || 0;
      const totalCount = lastPage?.data?.count || 0;
      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.groups?.length || 0),
        0,
      );

      // Stop fetching if the total count is less than the limit (means all items are on the first page)
      if (totalCount <= 5) return undefined;

      // Stop fetching if lastPage has no data or all items are fetched
      return lastPageCount > 0 && currentCount < totalCount
        ? allPages.length + 1
        : undefined;
    },
    select: (data) => ({
      groups: data.pages?.[0]?.data?.data?.rows || [],
      total: data.pages?.[0]?.data?.data?.count || 0,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
export const useGetPublicGroupListQuery = ({ enabled, search, groupName, groupType }) =>
  useInfiniteQuery({
    queryKey: ['GET_PUBLIC_GROUP_LIST_QUERY', search, groupName],
    queryFn: async ({ pageParam = 1 }) => {
      const params = { search, page: pageParam, limit: 20, groupName };
      return getPublicGroupList(params);
    },
    getNextPageParam: (lastPage, allPages) => {
      const lastPageCount = lastPage?.data?.groups?.length || 0;
      const totalCount = lastPage?.data?.count || 0;
      const currentCount = allPages.reduce(
        (acc, page) => acc + (page?.data?.groups?.length || 0),
        0,
      );

      // Stop fetching if the total count is less than the limit (means all items are on the first page)
      if (totalCount <= 20) return undefined;

      // Stop fetching if lastPage has no data or all items are fetched
      return lastPageCount > 0 && currentCount < totalCount
        ? allPages.length + 1
        : undefined;
    },
    select: (data) => ({
      groups: data.pages.flatMap((page) => page?.data?.groups || []),
      total: data.pages?.[0]?.data?.count || 0,
    }),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
export const useSendPublicMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_PUBLIC_MSG'],
    mutationFn: (data) => sendPublicChatsMsg(data),
    onSuccess,
    onError,
  });

export const useGenerateAgoraToken = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['get_agora_token'],
    mutationFn: (data) => getAgoraToken(data),
    onSuccess,
    onError,
  });

export const useDeclinedCall = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['cancelled_call'],
    mutationFn: (data) => sendDeclinedReq(data),
    onSuccess,
    onError,
  });

export const useGetPrivateChatsQuery = ({ enabled, receiverId }) =>
  useQuery({
    queryKey: ['GET_PRIVATE_CHAT_QUERY', receiverId],
    queryFn: () => getPrivateChat({ receiverId }),
    select: (data) => data,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetUserTagsQuery = ({ enabled, params }) =>
  useQuery({
    queryKey: ['GET_USER_TAGS_QUERY', params?.search],
    queryFn: () => getUserTags(params),
    select: (data) => data?.data?.rows,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetRecentChatsQuery = ({ params }) =>
  useQuery({
    queryKey: ['GET_RECENT_CHAT_QUERY', params.search],
    queryFn: () => getRecentChat(params),
    select: (data) => data?.data?.chatDetails,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

export const useGetFriendsListQuery = ({ enabled = true, params }) =>
  useQuery({
    queryKey: ['GET_FRIEND_LIST_QUERY', params?.search,params?.playersStatus],
    queryFn: () => getFriends(params),
    select: (data) => data?.data?.myFriends,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useGetFriendsRequestListQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['GET_FRIENDS_REQUEST_LIST'],
    queryFn: () => getFriendsRequest(),
    select: (data) => data?.data?.allFriendRequests,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useCreateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['CREATE_FRIENDS_REQUEST'],
    mutationFn: (data) => createFriendRequest(data),
    onSuccess,
    onError,
  });

export const useUpdateFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_FRIENDS_REQUEST'],
    mutationFn: (data) => updateFriendRequest(data),
    onSuccess,
    onError,
  });

export const useUnFriendsRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UN_FRIENDS_REQUEST'],
    mutationFn: (data) => unFriendRequest(data),
    onSuccess,
    onError,
  });

export const useSendTagMsgMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['SEND_TAG_MSG'],
    mutationFn: (data) => sendTagMsg(data),
    onSuccess,
    onError,
  });

export const useJoinGroup = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['USE_JOIN_GROUP'],
    mutationFn: (data) => joinGroup(data),
    onSuccess,
    onError,
  });
  export const useAcceptDeclineGroupJonRequest = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['USE_ACCEPT_DECLINE_JOIN_GROUP_REQUEST'],
    mutationFn: (data) => acceptDeclineGroupRequest(data),
    onSuccess,
    onError,
  });