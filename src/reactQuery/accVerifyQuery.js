import { initKYC } from '@/utils/apiCalls'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'


const initKYCQuery = ({ enabled, errorHandler, successHandler }) => {
  return useQuery({
    queryKey: "INIT_KYC",
    queryFn: () => {
      return initKYC()
    },
    enabled,
    select: (res) => {
      return res?.data || {}
    },
    onSuccess: (data) => {
      successHandler(data)
    },
    onError: (error) => {
      errorHandler(error?.response?.data?.errors?.[0])
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false
  })
}

export const AccVerifyQuery = {
  initKYCQuery
}

export default AccVerifyQuery
