import { useMutation } from '@tanstack/react-query';
import { processPaymentDeposit, processWithdrawalRequest } from '@/utils/apiCalls';

export const usePaymentDepositMutation = (options = {}) => {
  return useMutation({
    mutationFn: processPaymentDeposit,
    ...options,
  });
};

export const useWithdrawalRequestMutation = (options = {}) => {
  return useMutation({
    mutationFn: processWithdrawalRequest,
    ...options,
  });
};
