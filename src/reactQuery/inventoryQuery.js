import { toast } from 'react-hot-toast';
import {
  addToCart,
  cancelOrder,
  confirmOrder,
  getAllOrders,
  getCart,
  updateCart,
  updateCartAddress,
} from '@/utils/apiCalls';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const useAddToCartMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['ADD_TO_CART'],
    mutationFn: (data) => addToCart(data),
    onSuccess,
    onError,
  });

export const useUpdateDeliveryAddressMutation = ({ onSuccess, onError }) =>
  useMutation({
    mutationKey: ['UPDATE_DELIVERY_ADDRESS'],
    mutationFn: (data) => addToCart(data),
    onSuccess,
    onError,
  });

export const useCartQuery = ({ data, enabled = true }) =>
  useQuery({
    queryKey: ['GET_CART'],
    queryFn: () => getCart(data),
    select: (data) => data?.data?.allCartItems || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useUpdateCartAddressMutation = ({ onSuccess, onError }) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['UPDATE_CART_ADDRESS'],
    mutationFn: (data) => updateCartAddress(data),
    onSuccess: () => {
      queryClient.invalidateQueries(['GET_CART']);
      onSuccess();
    },
    onError,
  });
};

export const useUpdateCartMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['UPDATE_CART'],
    mutationFn: (data) => updateCart(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries(['GET_CART']);
      if (!data?.data.success) {
        toast.error(data?.data?.message);
      } else {
        toast.success(data?.data?.message);
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description || 'Unable to add';
      toast.error(message);
    },
  });
};

export const useConfirmOrderMutation = ({ onNext }) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['CONFIRM_ORDER'],
    mutationFn: (data) => confirmOrder(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries(['GET_CART']);
      if (!data?.data.success) {
        toast.error(data?.data?.message);
      } else {
        toast.success(data?.data?.message);
      }
      onNext();
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Unable to confirm order';
      toast.error(message);
    },
  });
};

export const useAllOrdersQuery = ({ enabled = true }) =>
  useQuery({
    queryKey: ['GET_ALL_ORDERS'],
    queryFn: (data) => getAllOrders(data),
    select: (data) => data?.data?.allOrders || [],
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });

export const useCancelOrderMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['CANCEL_ORDER'],
    mutationFn: (data) => cancelOrder(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries(['GET_ALL_ORDERS']);
      if (!data?.data.success) {
        toast.error(data?.data?.message);
      } else {
        toast.success(data?.data?.message);
      }
    },
    onError: (error) => {
      const message =
        error.response?.data?.errors?.[0]?.description ||
        'Unable to confirm order';
      toast.error(message);
    },
  });
};
