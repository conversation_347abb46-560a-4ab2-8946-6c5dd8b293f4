import { useQuery } from '@tanstack/react-query';
import { getAllBets } from '@/utils/apiCalls';

export const useAllBetsQuery = ({ enabled = true, myBets, limit }) =>
  useQuery({
    queryKey: ['allBets', myBets, limit],
    queryFn: () => {
      if (myBets) {
        return getAllBets({ myBets, limit, page:1 });
      } else {
        return getAllBets({ limit, page:1 });
      }
    },
    select: (data) => data?.data || {},
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled,
  });
