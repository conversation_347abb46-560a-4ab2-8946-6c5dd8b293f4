// socket-config.js - Add these to your existing socket file

import { io } from 'socket.io-client';
const URL = process.env.NEXT_PUBLIC_SOCKET_URL;

// Keep your existing socket exports, and add the chatRoomSocket:
export const chatRoomSocket = io(`${URL}/chat-room`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
});

// Voice chat events
export const VOICE_CHAT_EVENTS = {
  INITIATE_CALL: 'INITIATE_CALL',
  INCOMING_CALL: 'INCOMING_CALL',
  CALL_ACCEPTED: 'CALL_ACCEPTED',
  CALL_REJECTED: 'CALL_REJECTED',
  END_CALL: 'END_CALL',
  CALL_ENDED: 'CALL_ENDED'
};

// Socket connection helper
export const connectSocket = (socket, accessToken) => {
  socket.auth = { token: accessToken };
  socket.connect();
};

// Voice chat helper functions
export const voiceChatActions = {
  initiateCall: (socket, data) => {
    socket.emit(VOICE_CHAT_EVENTS.INITIATE_CALL, {
      receiverId: data.receiverId,
      channelName: data.channelName,
      timestamp: new Date().toISOString()
    });
  },

  acceptCall: (socket, data) => {
    socket.emit(VOICE_CHAT_EVENTS.CALL_ACCEPTED, {
      callerId: data.callerId,
      channelName: data.channelName,
      timestamp: new Date().toISOString()
    });
  },

  rejectCall: (socket, data) => {
    socket.emit(VOICE_CHAT_EVENTS.CALL_REJECTED, {
      callerId: data.callerId,
      channelName: data.channelName,
      timestamp: new Date().toISOString()
    });
  },

  endCall: (socket, data) => {
    socket.emit(VOICE_CHAT_EVENTS.END_CALL, {
      channelName: data.channelName,
      participantIds: data.participantIds,
      timestamp: new Date().toISOString()
    });
  }
};