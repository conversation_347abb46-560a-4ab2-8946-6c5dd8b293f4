export const getDateDaysAgo = (days) => {
  const now = new Date();
  const defaultStartDate = now.setDate(now.getDate() - days);
  return new Date(defaultStartDate);
};


export const formatDate = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0'); // Ensure two digits
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
  const year = d.getFullYear();
  return `${year}-${month}-${day}`;
};


export const formatDateMDY = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0'); // Ensure two digits
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
  const year = d.getFullYear();
  return `${month}-${day}-${year}`; // Return in MMDDYYYY format
};


export const convertTo24HourTime = (datetime) => {
  const date = new Date(datetime);
  const hours = String(date.getHours()).padStart(2, '0'); // Ensure two digits for hours
  const minutes = String(date.getMinutes()).padStart(2, '0'); // Ensure two digits for minutes
  const period = date.getHours() >= 12 ? 'PM' : 'AM'; // Determine AM/PM
  return `${hours}:${minutes} ${period}`; // Return in 24-hour format with AM/PM
};


export const formatDateMDYAnd24Hours = (date) => {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0'); // Ensure two digits
  const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
  const year = d.getFullYear();
  const hours = String(d.getHours()).padStart(2, '0'); // Ensure two digits for hours
  const minutes = String(d.getMinutes()).padStart(2, '0'); // Ensure two digits for minutes
  const period = d.getHours() >= 12 ? 'PM' : 'AM'; // Determine AM/PM
  return `${month}-${day}-${year} ${hours}:${minutes} `; // Return in MMDDYYYY format
};