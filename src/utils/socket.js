import { io } from 'socket.io-client';

const URL = process.env.NEXT_PUBLIC_SOCKET_URL;
export const walletSocket = io(`${URL}/wallet`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
  
});

export const livePlayerSocket = io(`${URL}/live-players`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const liveBetsSocket = io(`${URL}/live-bets`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const myBetsSocket = io(`${URL}/my-bets`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const liveChatsSocket = io(`${URL}/live-chats`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const groupChatsSocket = io(`${URL}/chat-room`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const privateChatsSocket = io(`${URL}/private-chat`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
});

export const playerSocket = io(`${URL}/player`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
});

export const chatRoomSocket = io(`${URL}/chat-room`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
});


export const voiceCallConnected = io(`${URL}`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
});
export const playerActivitySocket = io(`${URL}/player-activity`, {
  transports: ['websocket'],
  withCredentials: true,
  autoConnect: false,
  multiplex: true,
  
});
const privateChatSockets = {};
const groupChatSockets = {};

export const getPrivateChatSocket = (userId, accessToken) => {
  if (!privateChatSockets[userId]) {
    privateChatSockets[userId] = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/private-chat`, {
      query: { userId },
      transports: ['websocket'],
      withCredentials: true,
      auth: { token: accessToken },
    });
  }
  return privateChatSockets[userId];
};
export const getGroupChatSocket = (groupName, accessToken) => {
  if (!groupChatSockets[groupName]) {
    groupChatSockets[groupName] = io(`${process.env.NEXT_PUBLIC_SOCKET_URL}/chat-room`, {
      query: { groupName },
      transports: ['websocket'],
      withCredentials: true,
      auth: { token: accessToken },
    });
  }
  return groupChatSockets[groupName];
};
