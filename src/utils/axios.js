'use client';

import axios from 'axios';
import Cookies from 'js-cookie';
import { getAccessToken, setAccessToken } from './helper';

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  withCredentials: true,
});

// Create a cancellation source that we can use to cancel requests
let cancelSource = axios.CancelToken.source();

// Function to cancel all pending requests
const cancelAllRequests = () => {
  cancelSource.cancel('Operation canceled due to 403 error');
  // Create a new cancellation source for future requests
  cancelSource = axios.CancelToken.source();
};

axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = getAccessToken();
    if (accessToken) {
      config.headers['accesstoken'] = accessToken;
      config.cancelToken = cancelSource.token;
    }
    // Add the cancel token to each request
    return config;
  },
  (error) => Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (res) => {
    const accessToken = res.headers.get("accessToken") || res.headers.get("Accesstoken");
    if (accessToken) {
      setAccessToken(accessToken);
    }
    return res.data;
  },
  (error) => {
    const status = error.response?.status;
    const errorCode = error?.response?.data?.errors?.[0]?.errorCode;

    if (status === 403) {
      // Cancel all pending requests
      cancelAllRequests();
      window.dispatchEvent(new Event('logout'));
    }

    // If the error was due to cancellation, rethrow it as is
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    }

    return Promise.reject(error);
  },
);

const makeRequest = async (
  url,
  method,
  data = {},
  params = {},
  headers = { 'Content-Type': 'application/json' },
) => {
  return axiosInstance({ url, method, data, params, headers });
};

const requestHelper = (method) => (url, data, params, headers) =>
  makeRequest(url, method, data, params, headers);

export const getRequest = requestHelper('GET');
export const postRequest = requestHelper('POST');
export const putRequest = requestHelper('PUT');
export const deleteRequest = requestHelper('DELETE');