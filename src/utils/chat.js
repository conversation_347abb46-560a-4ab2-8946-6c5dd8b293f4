'use client';

import useChatStore from '@/store/useChatStore';
import useGeneralStore from '@/store/useGeneralStore';

export const useOpenChatWindow = () => {
  const { showChatHeader, setShowChatHeader, setShowChat,setChatHeaderActiveTab } = useChatStore();
  const { setOpenChat,setShowHomePopup } = useGeneralStore();

  const openChatWindow = () => {
    if (window.innerWidth < 1024) {
      setShowChat(true);
      setShowChatHeader(true);
      setChatHeaderActiveTab("chat")
      setShowHomePopup(false)
    } else {
      setOpenChat(true);
    }
  };

  return openChatWindow;
};
