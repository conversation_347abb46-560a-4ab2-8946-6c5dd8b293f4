import { create } from 'zustand';

const useGeneralStore = create((set) => ({
  openChat: true,
  bannerLoading:true,
  activeMenu:"/",
  toggleSideMenu:false,
  showHomePopup: false,
  setOpenChat: (data) => {
    set(() => ({ openChat: data }));
  },
  openMenu: false,
  setOpeMenu: (data) => {
    set(() => ({ openMenu: data }));
  },
  
  banners:[],
  setBanners: (data) => {
    set(()=> ({banners: data}))
  },
  setBannerLoading:(data)=>{
    set(()=> ({bannerLoading: data}))

  },
  setActiveMenu:(data)=>{
    set(()=> ({activeMenu: data}))

  },
  setToggleSideMenu:(data)=>{
    set(()=> ({toggleSideMenu: data}))

  },
  setShowHomePopup:(data)=>{
    set(()=>({showHomePopup: data}))
  }

}));

export default useGeneralStore;
