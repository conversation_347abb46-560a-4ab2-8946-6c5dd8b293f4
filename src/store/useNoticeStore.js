import { create } from 'zustand';

const useNotificationStore = create((set) => ({
  showNotificationPopup: false,
  noticeDetails: [],
  notifications: {},
  loading: false,
  error: null,
  page:1,
  setPage:(data)=>
    set({page:data}),
  setShowNotificationPopup: (data) =>
    set({
      showNotificationPopup: data,
    }),


  setNotices: (data) =>
    set({
      notifications: data || [],
    }),

  setNotifications: (data) =>
    set({
      notifications: data || {},
    }),

  addNotice: (notice) =>
    set((state) => ({ notifications: [...state.notifications, notice] })),

  setLoading: (loading) =>
    set({
      loading,
    }),

  setError: (error) =>
    set({
      error,
    }),
}));

export default useNotificationStore;
