import { create } from 'zustand';

const useCallStore = create((set) => ({
  callStartTime: null,
  callDuration: 0,
  toggleMuted: true,
  
  setCallStartTime: (time) => set({ callStartTime: time }),
  setCallDuration: (data) =>
    set((state) => ({
      callDuration: typeof data === 'function' ? data(state.callDuration) : data,
    })),
  
  setToggleMuted: (data) => set({ toggleMuted: data }),
}));

export default useCallStore;
