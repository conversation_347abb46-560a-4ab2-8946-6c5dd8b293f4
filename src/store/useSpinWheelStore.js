'use client';

import { getAllGCValues, getAllSCValues } from '@/utils/helper';
import { create } from 'zustand';

const useSpinWheelStore = create((set) => ({
  spinWheelData: { sc: [], gc: [] },
  spinWheelResult: {
    showResult: false,
    gc: '',
    sc: '',
    index: '',
    bonusActivated: false,
  },
  spinWheelSound: false,
  jackpotSound: false,
  showClose: true,
  setSpinWheelData: (data) => {
    const wheelContentValuesObject = {
      sc: getAllSCValues(data),
      gc: getAllGCValues(data),
    };
    set(() => ({
      spinWheelData: wheelContentValuesObject,
    }));
  },
  setSpinWheelResult: (data) => {
    set(() => ({
      spinWheelResult: data,
    }));
  },
  setSpinWheelSound: (data) => {
    set(() => ({
      spinWheelSound: data,
    }));
  },
  setJackpotSound: (data) => {
    set(() => ({
      jackpotSound: data,
    }));
  },
  setShowClose: (data) => {
    set(() => ({
      showClose: data,
    }));
  },
}));

export default useSpinWheelStore;
