import { create } from 'zustand';

const useGameStore = create((set) => ({
  lobbyGames: [],
  searchActive: false,
  gameDetails:{},
  iframeHeight:"calc(100vh - 80px)",
  setLobbyGames: (lobbyGames) => set({ lobbyGames: lobbyGames }),
  setSearchActive: () => set({ searchActive: true }),
  clearSearch: () => set({ lobbyGames: [], searchActive: false }),
  setGameDetails: (gameDetails) => set({ gameDetails: gameDetails }),
  setIFrameHeight: (iframeHeight) => set({ iframeHeight: iframeHeight }),

}));

export default useGameStore;
