import { create } from 'zustand';

const useActivePlayerStore = create((set) => ({
  myfriends: [],
  publicUsers:[],
  myFriendsLoading:true,
  publicUsersLoading:true,
activeTab:0,
  setMyFriends: (myfriends) => set({ myfriends }),
  setMyPlayerLoading: (data) => set({ myFriendsLoading:data }),
  setPublicUsers: (publicUsers) => set({ publicUsers }),
  setPublicUsersLoading: (data) => set({ publicUsersLoading:data }),
  setActiveTab: (data) => set({ activeTab:data }),
  
}));

export default useActivePlayerStore;
