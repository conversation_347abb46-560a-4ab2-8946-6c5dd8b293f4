import { create } from 'zustand';

const useUserInfoStore = create((set) => ({
  isUserInfoModalOpen: false,
  selectedUserId: null,
  userDetails: null,
  loading: false,
  error: null,
  openUserInfoModal: (userId) => {
    set({
      isUserInfoModalOpen: true,
      selectedUserId: userId,
      loading: true,
      error: null,
      userDetails: null,
    });
  },
  setUserDetails: (details) => set({ userDetails: details, loading: false }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error, loading: false }),
  closeUserInfoModal: () =>
    set({
      isUserInfoModalOpen: false,
      selectedUserId: null,
      userDetails: null,
      loading: false,
      error: null,
    }),
}));

export default useUserInfoStore;
