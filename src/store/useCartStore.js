import { create } from 'zustand';

const useCartStore = create((set) => ({
  cart: [],
  showCart: false,
  toggleShowCart: () => set((state) => ({ showCart: !state.showCart })),
  addToCart: (product) =>
    set((state) => {
      const existingProduct = state.cart.find(
        (item) => item.inventoryId === product.inventoryId,
      );
      if (existingProduct) {
        return {
          cart: state.cart.map((item) =>
            item.inventoryId === product.inventoryId
              ? { ...item, quantity: item.quantity + 1 }
              : item,
          ),
        };
      }
      return {
        cart: [...state.cart, { ...product, quantity: 1 }],
      };
    }),
  removeFromCart: (productId) =>
    set((state) => ({
      cart: state.cart.filter((item) => item.inventoryId !== productId),
    })),
  clearCart: () => set({ cart: [] }),
}));

export default useCartStore;
