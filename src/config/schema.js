import * as yup from 'yup';

export const updateProfileSchema = yup.object().shape({
  firstName: yup
  .string()
  .trim()
  .matches(
    /^[A-Za-z][A-Za-z ]*$/,
    'Only Alphabets and Space Allowed and Must Start with Alphabet'
  )
  .min(3, 'First Name must be at least 3 characters')
  .max(20, 'First Name cannot exceed 20 characters')
  .required('First Name is required'),

  lastName: yup
  .string()
  .trim()
  .matches(
    /^[A-Za-z][A-Za-z ]*$/,
    'Only Alphabets and Space Allowed and Must Start with Alphabet'
  )
  .min(3, 'Last Name must be at least 3 characters')
  .max(20, 'Last Name cannot exceed 20 characters')
  .required('Last Name is required'),
  username: yup.string().trim().required('Username is required'),
  dob: yup.date().required('Date of Birth is required'),
  phoneCode: yup.object().required('Phone Code is required'),
  zipCode: yup
  .string()
  .trim()
  .matches(/^\d+$/, 'Zip Code should contain only numbers')
  .min(5, 'Zip Code must be at least 5 digits')
  .max(9, 'Zip Code cannot exceed 9 digits')
  .required('Zip Code is required'),
    // .matches(
    //   /^\d{5}(-\d{4})?$/,
    //   'Zip Code must be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)',
    // )
    
  phoneNumber: yup
    .string()
    .min(10)
    .max(10)
    .matches(/^\d{1,10}$/, 'Phone Number must be of 10 digits')
    .required('Phone Number is required'),

  address: yup
    .string()
    .trim()
    .required('Address is required')
    .min(5, 'Address must be at least 5 characters')
    .test(
      'not-only-numbers',
      'Address cannot contain only numbers',
      (value) => !/^\d+$/.test(value || '')
    ),

    city: yup
    .string()
    .trim()
    .matches(/^[A-Za-z]+$/, 'City must contain only letters without spaces or special characters')
    .min(3, 'City must be at least 3 characters')
    .required('City is required'),
  
  state: yup
    .object()
    .shape({
      value: yup.string().required('State is required'),
    })
    .nullable()
    .required('State is required')
    .typeError('State is required'),
  country: yup
    .object()
    .shape({
      value: yup.string().required('country is required'),
    })
    .nullable()
    .required('country is required')
    .typeError('country is required'),
});

export const schema = yup.object().shape({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  username: yup.string().required('Username is required'),
});
