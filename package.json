{"name": "alibaba", "version": "0.1.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "husky install", "dev": "next dev -p 8080", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "commit": "cz", "format": "prettier --write \"**/*.{js,ts,tsx,md}\"", "prepare": "husky"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@giphy/js-fetch-api": "^5.6.0", "@giphy/react-components": "^9.5.0", "@hookform/resolvers": "^3.4.2", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.36.0", "@veriff/incontext-sdk": "^2.3.1", "agora-rtc-sdk-ng": "^4.23.0", "axios": "^1.6.8", "embla-carousel-react": "^8.0.4", "emoji-mart": "^5.6.0", "framer-motion": "^11.2.4", "lottie-react": "^2.4.0", "lucide-react": "^0.397.0", "next": "14.2.3", "nookies": "^2.5.2", "pnpm": "^10.6.5", "react": "^18", "react-datepicker": "^6.9.0", "react-dom": "^18", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.10.3", "react-loading-skeleton": "^3.4.0", "react-qr-code": "^2.0.12", "react-select": "^5.8.0", "react-simple-captcha": "^9.3.1", "react-tooltip": "^5.28.0", "socket.io-client": "^4.7.5", "swiper": "^11.1.1", "use-debounce": "9.0.3", "uuid": "^11.0.5", "yup": "^1.4.0", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@commitlint/cz-commitlint": "^17.5.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.1"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --config ./.prettierrc.json --write"], "**/*.{css,scss,md,html,json}": ["prettier --config ./.prettierrc.json --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}