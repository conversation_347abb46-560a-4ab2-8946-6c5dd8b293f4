{
  "extends": [
    "next/core-web-vitals",
    "eslint:recommended",
    "airbnb",
    "airbnb/hooks",
    "plugin:react/recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:jsx-a11y/recommended",
    "plugin:prettier/recommended"
  ],
  "settings": {
    "import/resolver": {
      "alias": {
        "map": [
          ["@", "./src"] // Adjust the alias path as per your project setup
        ],
        "extensions": [".js", ".jsx"]
      }
    }
  },
  "rules": {
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react/display-name": 1,
    "import/extensions": [
      "warn",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never"
      }
    ]
  }
}